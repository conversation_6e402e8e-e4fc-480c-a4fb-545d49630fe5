<?php
if (!isset($data) || !isset($data['vehicule'])) {
    header('Location: ' . BASE_URL . 'vehicules-liste');
    exit;
}

$vehicule = $data['vehicule'];

// Ajouter les styles spécifiques à cette page
$data['additional_head_content'] = '
<link rel="stylesheet" href="' . BASE_URL . 'public/css/style.css">
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>
    .form-container {
        background-color: rgba(255, 255, 255, 0.95);
        border-radius: 10px;
        padding: 30px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
        margin-top: 20px;
        margin-bottom: 30px;
    }
    .form-container h1 {
        color: #333;
        margin-bottom: 20px;
        border-bottom: 2px solid #ddd;
        padding-bottom: 10px;
    }
    .form-label {
        font-weight: bold;
        color: #333;
    }
    .form-control {
        border: 1px solid #ccc;
    }
    .card {
        background-color: rgba(255, 255, 255, 0.9);
    }
    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #ddd;
    }
    .btn-primary {
        background-color: #0066cc;
    }
    .btn-secondary {
        background-color: #6c757d;
    }
</style>
';

// Inclure le header commun
require_once __DIR__ . '/../templates/header.php';
?>
    <div class="container mt-4">
        <div class="form-container">
            <h1>Modifier le véhicule</h1>

            <?php if (isset($_GET['error'])): ?>
                <div class="alert alert-danger">
                    <?php
                        switch($_GET['error']) {
                            case 'update_failed':
                                echo "Erreur lors de la mise à jour du véhicule.";
                                break;
                            case 'invalid_statut_client':
                                echo isset($_GET['message']) ? htmlspecialchars($_GET['message']) : "Incohérence entre le statut et le client.";
                                break;
                            default:
                                echo "Une erreur est survenue.";
                        }
                    ?>
                </div>
            <?php endif; ?>

        <form action="<?php echo BASE_URL; ?>modifier-vehicule" method="POST" class="needs-validation" novalidate>
            <input type="hidden" name="id" value="<?php echo htmlspecialchars($vehicule['id']); ?>">

            <div class="mb-3">
                <label for="reference" class="form-label">Référence</label>
                <input type="text" class="form-control" id="reference" name="reference"
                       value="<?php echo htmlspecialchars($vehicule['reference']); ?>" required>
            </div>

            <div class="mb-3">
                <label for="libelle" class="form-label">Libellé</label>
                <input type="text" class="form-control" id="libelle" name="libelle"
                       value="<?php echo htmlspecialchars($vehicule['libelle']); ?>" required>
            </div>

            <div class="mb-3">
                <label for="descriptif" class="form-label">Descriptif</label>
                <textarea class="form-control" id="descriptif" name="descriptif" rows="3"><?php echo htmlspecialchars($vehicule['descriptif']); ?></textarea>
            </div>

            <div class="mb-3">
                <label for="annee" class="form-label">Année</label>
                <input type="text" class="form-control" id="annee" name="annee"
                       value="<?php echo htmlspecialchars($vehicule['annee'] ?? ''); ?>"
                       maxlength="50">
            </div>

            <div class="mb-3">
                <label for="prix" class="form-label">Prix</label>
                <div class="input-group">
                    <input type="number" class="form-control" id="prix" name="prix"
                           value="<?php echo htmlspecialchars($vehicule['prix']); ?>" step="0.01" required>
                    <span class="input-group-text">€</span>
                </div>
            </div>

            <div class="mb-3">
                <label for="client" class="form-label">Client</label>
                <input type="text" class="form-control" id="client" name="client"
                       value="<?php echo htmlspecialchars($vehicule['client']); ?>">
            </div>

            <div class="mb-3">
                <label for="client_adresse" class="form-label">Adresse du client</label>
                <textarea class="form-control" id="client_adresse" name="client_adresse"
                          rows="3"><?php echo htmlspecialchars($vehicule['client_adresse'] ?? ''); ?></textarea>
            </div>

            <div class="mb-3">
                <label for="statut" class="form-label">Statut</label>
                <select class="form-select" id="statut" name="statut" required>
                    <option value="Disponible" <?php echo $vehicule['statut'] === 'Disponible' ? 'selected' : ''; ?>>Disponible</option>
                    <option value="Vendu" <?php echo $vehicule['statut'] === 'Vendu' ? 'selected' : ''; ?>>Vendu</option>
                    <option value="Achat en cours" <?php echo $vehicule['statut'] === 'Achat en cours' ? 'selected' : ''; ?>>Achat en cours</option>
                </select>
            </div>

            <div class="mb-3">
                <label for="rgltreference" class="form-label">Référence de règlement</label>
                <input type="text" class="form-control" id="rgltreference" name="rgltreference"
                       value="<?php echo htmlspecialchars($vehicule['rgltreference']); ?>">
            </div>

            <div class="mb-3">
                <label for="dateheureachat" class="form-label">Date et heure d'achat</label>
                <input type="datetime-local" class="form-control" id="dateheureachat" name="dateheureachat"
                       value="<?php echo !empty($vehicule['dateheureachat']) ? date('Y-m-d\TH:i', strtotime($vehicule['dateheureachat'])) : ''; ?>">
                <div class="form-text">
                    Date et heure auxquelles le client a démarré le processus d'achat.
                </div>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="certificat" name="certificat"
                       <?php echo $vehicule['certificat'] ? 'checked' : ''; ?>>
                <label class="form-check-label" for="certificat">Certificat disponible</label>
            </div>

            <div class="mb-3">
                <label for="libelle_certificat" class="form-label">Libellé du certificat</label>
                <input type="text" class="form-control" id="libelle_certificat" name="libelle_certificat"
                       value="<?php echo htmlspecialchars($vehicule['libelle_certificat'] ?? ''); ?>">
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="aidetransport" name="aidetransport"
                       <?php echo isset($vehicule['aidetransport']) && $vehicule['aidetransport'] ? 'checked' : ''; ?>>
                <label class="form-check-label" for="aidetransport">Aide au transport demandée</label>
            </div>

            <div class="mb-3">
                <button type="submit" id="submit-btn" class="btn btn-primary">Enregistrer les modifications</button>
                <a href="<?php echo BASE_URL; ?>vehicules-liste" class="btn btn-secondary">Annuler</a>
            </div>
        </form>

        <!-- Section de gestion des images -->
        <div class="card mb-4 mt-4">
            <div class="card-header bg-light">
                <h3 class="card-title h5 mb-0">Images du véhicule</h3>
            </div>
            <div class="card-body">
                <?php if (isset($_GET['error']) && $_GET['error'] === 'invalid_format'): ?>
                    <div class="alert alert-danger">
                        Format d'image invalide. Formats acceptés : JPG, JPEG, PNG, GIF
                    </div>
                <?php endif; ?>

                <?php if (!empty($data['images'])): ?>
                    <!-- Affichage des images existantes -->
                    <div class="row vehicle-image-gallery mb-4">
                        <?php foreach ($data['images'] as $image): ?>
                            <div class="col-md-3 mb-3">
                                <div class="card h-100">
                                    <img src="<?php echo htmlspecialchars(BASE_URL . $image); ?>"
                                         class="card-img-top"
                                         alt="Image du véhicule"
                                         loading="lazy">
                                    <div class="card-body">
                                        <form action="<?php echo BASE_URL; ?>supprimer-image"
                                              method="POST"
                                              class="d-inline">
                                            <input type="hidden" name="image_path"
                                                   value="<?php echo htmlspecialchars($image); ?>">
                                            <input type="hidden" name="vehicule_id"
                                                   value="<?php echo htmlspecialchars($vehicule['id']); ?>">
                                            <button type="submit"
                                                    class="btn btn-danger btn-sm w-100"
                                                    onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette image ?')">
                                                <i class="fas fa-trash-alt"></i> Supprimer
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="alert alert-info">
                        Aucune image n'est actuellement associée à ce véhicule.
                    </div>
                <?php endif; ?>

                <!-- Formulaire d'ajout d'image -->
                <form action="<?php echo BASE_URL; ?>ajouter-image"
                      method="POST"
                      enctype="multipart/form-data"
                      class="image-upload-form">
                    <input type="hidden" name="vehicule_id"
                           value="<?php echo htmlspecialchars($vehicule['id']); ?>">
                    <div class="mb-3">
                        <label for="image" class="form-label">Ajouter une nouvelle image</label>
                        <input type="file"
                               class="form-control"
                               id="image"
                               name="image"
                               accept="image/*"
                               required>
                        <div class="form-text">
                            Formats acceptés : JPG, JPEG, PNG, GIF.
                            L'image sera automatiquement redimensionnée si nécessaire.
                        </div>
                    </div>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-upload"></i> Ajouter l'image
                    </button>
                </form>
            </div>
        </div>


        </div><!-- Fermeture de form-container -->
    </div><!-- Fermeture de container -->

    <!-- Validation du formulaire -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            'use strict';

            // Récupérer le formulaire et le bouton de soumission
            var form = document.querySelector('form.needs-validation');
            var submitBtn = document.getElementById('submit-btn');
            var statutSelect = document.getElementById('statut');
            var clientInput = document.getElementById('client');

            // Fonction pour vérifier la cohérence entre statut et client
            function validateStatutClient() {
                var statut = statutSelect.value;
                var client = clientInput.value.trim();
                var isValid = true;
                var errorMessage = '';

                // Vérifier si un statut "Disponible" a un client renseigné
                if (statut === 'Disponible' && client !== '') {
                    isValid = false;
                    errorMessage = 'Un véhicule avec statut "Disponible" ne peut pas avoir de client renseigné.';
                }

                // Vérifier si un statut "Vendu" a un client vide
                if (statut === 'Vendu' && client === '') {
                    isValid = false;
                    errorMessage = 'Un véhicule avec statut "Vendu" doit obligatoirement avoir un client renseigné.';
                }

                // Afficher ou masquer le message d'erreur
                var errorDiv = document.getElementById('statut-client-error');
                if (!errorDiv) {
                    errorDiv = document.createElement('div');
                    errorDiv.id = 'statut-client-error';
                    errorDiv.className = 'alert alert-danger mt-3';
                    form.insertBefore(errorDiv, submitBtn.parentNode);
                }

                if (!isValid) {
                    errorDiv.textContent = errorMessage;
                    errorDiv.style.display = 'block';
                } else {
                    errorDiv.style.display = 'none';
                }

                return isValid;
            }

            // Ajouter des écouteurs d'événements pour valider en temps réel
            statutSelect.addEventListener('change', validateStatutClient);
            clientInput.addEventListener('input', validateStatutClient);

            // Exécuter la validation au chargement de la page
            validateStatutClient();

            // Ajouter un gestionnaire d'événements au bouton de soumission
            submitBtn.addEventListener('click', function(event) {
                event.preventDefault(); // Empêcher la soumission par défaut
                console.log('Bouton de soumission cliqué');

                // Vérifier la cohérence entre statut et client
                var statutClientValid = validateStatutClient();

                // Vérifier si le formulaire est valide
                if (form.checkValidity() && statutClientValid) {
                    console.log('Formulaire valide, soumission en cours...');
                    form.submit(); // Soumettre le formulaire manuellement
                } else {
                    console.log('Formulaire invalide');
                    form.classList.add('was-validated');
                }
            });
        });
    </script>

<?php
// Inclure le footer commun
require_once __DIR__ . '/../templates/footer.php';
?>