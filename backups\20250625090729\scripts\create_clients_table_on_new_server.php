<?php
// Charger la configuration de la base de données
require_once __DIR__ . '/../app/config/database.php';

// Connexion à la base de données des clients
$conn = getClientsDbConnection();

// Vérifier si la connexion est établie
echo "Connexion à la base de données des clients: " . ($conn ? "OK" : "ÉCHEC") . "\n";

// Créer la table clients si elle n'existe pas déjà
$sql = "CREATE TABLE IF NOT EXISTS clients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    emailclient VARCHAR(100) NOT NULL UNIQUE,
    passwdclient VARCHAR(32) NOT NULL,
    nomclient VARCHAR(100),
    prenomclient VARCHAR(100),
    isadmin TINYINT(1) DEFAULT 0,
    datecreate DATE
)";

if ($conn->query($sql) === TRUE) {
    echo "Table 'clients' créée ou déjà existante.\n";
} else {
    echo "Erreur lors de la création de la table 'clients': " . $conn->error . "\n";
}

// Créer la table adresses_facturation si elle n'existe pas déjà
$sql = "CREATE TABLE IF NOT EXISTS adresses_facturation (
    id INT AUTO_INCREMENT PRIMARY KEY,
    af_emailclient VARCHAR(100) NOT NULL,
    af_raisonsocial VARCHAR(255),
    af_nomrue VARCHAR(255),
    af_codepostal VARCHAR(10),
    af_ville VARCHAR(100),
    af_pays VARCHAR(100) DEFAULT 'France',
    FOREIGN KEY (af_emailclient) REFERENCES clients(emailclient) ON DELETE CASCADE
)";

if ($conn->query($sql) === TRUE) {
    echo "Table 'adresses_facturation' créée ou déjà existante.\n";
} else {
    echo "Erreur lors de la création de la table 'adresses_facturation': " . $conn->error . "\n";
}

// Insérer un utilisateur de test (mot de passe: test123)
$email = '<EMAIL>';
$password = md5('test123');
$nom = 'Utilisateur';
$prenom = 'Test';
$isadmin = 0;
$date = date('Y-m-d');

// Vérifier si l'utilisateur existe déjà
$stmt = $conn->prepare("SELECT * FROM clients WHERE emailclient = ? LIMIT 1");
$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    // Insérer l'utilisateur de test
    $stmt = $conn->prepare("INSERT INTO clients (emailclient, passwdclient, nomclient, prenomclient, isadmin, datecreate) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("ssssis", $email, $password, $nom, $prenom, $isadmin, $date);
    
    if ($stmt->execute()) {
        echo "Utilisateur de test créé avec succès.\n";
        
        // Insérer l'adresse de facturation de test
        $raisonsocial = 'Société Test';
        $nomrue = '123 Rue de Test';
        $codepostal = '75000';
        $ville = 'Paris';
        $pays = 'France';
        
        $stmt = $conn->prepare("INSERT INTO adresses_facturation (af_emailclient, af_raisonsocial, af_nomrue, af_codepostal, af_ville, af_pays) VALUES (?, ?, ?, ?, ?, ?)");
        $stmt->bind_param("ssssss", $email, $raisonsocial, $nomrue, $codepostal, $ville, $pays);
        
        if ($stmt->execute()) {
            echo "Adresse de facturation de test créée avec succès.\n";
        } else {
            echo "Erreur lors de la création de l'adresse de facturation de test: " . $stmt->error . "\n";
        }
    } else {
        echo "Erreur lors de la création de l'utilisateur de test: " . $stmt->error . "\n";
    }
} else {
    echo "L'utilisateur de test existe déjà.\n";
}

// Fermer la connexion
$conn->close();

echo "\nScript terminé.\n";
?>
