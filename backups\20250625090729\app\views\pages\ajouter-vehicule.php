<?php
// Ajouter les styles spécifiques à cette page
$data['additional_head_content'] = '
<link rel="stylesheet" href="' . BASE_URL . 'public/css/style.css">
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>
    .form-container {
        background-color: rgba(255, 255, 255, 0.95);
        border-radius: 10px;
        padding: 30px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
        margin-top: 20px;
        margin-bottom: 30px;
    }
    .form-container h1 {
        color: #333;
        margin-bottom: 20px;
        border-bottom: 2px solid #ddd;
        padding-bottom: 10px;
    }
    .form-label {
        font-weight: bold;
        color: #333;
    }
    .form-control {
        border: 1px solid #ccc;
    }
    .card {
        background-color: rgba(255, 255, 255, 0.9);
    }
    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #ddd;
    }
    .btn-primary {
        background-color: #0066cc;
    }
    .btn-secondary {
        background-color: #6c757d;
    }
</style>
';

// Inclure le header commun
require_once __DIR__ . '/../templates/header.php';
?>
    <div class="container mt-4">
        <div class="form-container">
            <h1>Ajouter un véhicule</h1>

            <?php if (isset($_GET['error'])): ?>
                <div class="alert alert-danger">
                    <?php
                        switch($_GET['error']) {
                            case 'add_failed':
                                echo "Erreur lors de l'ajout du véhicule.";
                                break;
                            case 'invalid_statut_client':
                                echo isset($_GET['message']) ? htmlspecialchars($_GET['message']) : "Incohérence entre le statut et le client.";
                                break;
                            default:
                                echo "Une erreur est survenue.";
                        }
                    ?>
                </div>
            <?php endif; ?>

        <form action="<?php echo BASE_URL; ?>ajouter-vehicule" method="POST" class="needs-validation" novalidate>
            <div class="mb-3">
                <label for="reference" class="form-label">Référence</label>
                <input type="text" class="form-control" id="reference" name="reference" required>
            </div>

            <div class="mb-3">
                <label for="libelle" class="form-label">Libellé</label>
                <input type="text" class="form-control" id="libelle" name="libelle" required>
            </div>

            <div class="mb-3">
                <label for="descriptif" class="form-label">Descriptif</label>
                <textarea class="form-control" id="descriptif" name="descriptif" rows="3"></textarea>
            </div>

            <div class="mb-3">
                <label for="annee" class="form-label">Année</label>
                <input type="text" class="form-control" id="annee" name="annee"
                       maxlength="50">
            </div>

            <div class="mb-3">
                <label for="prix" class="form-label">Prix</label>
                <div class="input-group">
                    <input type="number" class="form-control" id="prix" name="prix" step="0.01" required>
                    <span class="input-group-text">€</span>
                </div>
            </div>

            <div class="mb-3">
                <label for="client" class="form-label">Client</label>
                <input type="text" class="form-control" id="client" name="client">
            </div>

            <div class="mb-3">
                <label for="client_adresse" class="form-label">Adresse du client</label>
                <textarea class="form-control" id="client_adresse" name="client_adresse" rows="3"></textarea>
            </div>

            <div class="mb-3">
                <label for="statut" class="form-label">Statut</label>
                <select class="form-select" id="statut" name="statut" required>
                    <option value="Disponible" selected>Disponible</option>
                    <option value="Vendu">Vendu</option>
                    <option value="Achat en cours">Achat en cours</option>
                </select>
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="certificat" name="certificat">
                <label class="form-check-label" for="certificat">Certificat disponible</label>
            </div>

            <div class="mb-3">
                <label for="libelle_certificat" class="form-label">Libellé du certificat</label>
                <input type="text" class="form-control" id="libelle_certificat" name="libelle_certificat">
            </div>

            <div class="mb-3 form-check">
                <input type="checkbox" class="form-check-input" id="aidetransport" name="aidetransport">
                <label class="form-check-label" for="aidetransport">Aide au transport demandée</label>
            </div>

            <div class="mb-3">
                <button type="submit" class="btn btn-primary">Ajouter le véhicule</button>
                <a href="<?php echo BASE_URL; ?>vehicules-liste" class="btn btn-secondary">Annuler</a>
            </div>
        </form>
        </div><!-- Fermeture de form-container -->
    </div><!-- Fermeture de container -->

    <!-- Validation du formulaire -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            'use strict';

            // Récupérer le formulaire
            var form = document.querySelector('form.needs-validation');
            var statutSelect = document.getElementById('statut');
            var clientInput = document.getElementById('client');

            // Fonction pour vérifier la cohérence entre statut et client
            function validateStatutClient() {
                var statut = statutSelect.value;
                var client = clientInput.value.trim();
                var isValid = true;
                var errorMessage = '';

                // Vérifier si un statut "Disponible" a un client renseigné
                if (statut === 'Disponible' && client !== '') {
                    isValid = false;
                    errorMessage = 'Un véhicule avec statut "Disponible" ne peut pas avoir de client renseigné.';
                }

                // Vérifier si un statut "Vendu" a un client vide
                if (statut === 'Vendu' && client === '') {
                    isValid = false;
                    errorMessage = 'Un véhicule avec statut "Vendu" doit obligatoirement avoir un client renseigné.';
                }

                // Afficher ou masquer le message d'erreur
                var errorDiv = document.getElementById('statut-client-error');
                if (!errorDiv) {
                    errorDiv = document.createElement('div');
                    errorDiv.id = 'statut-client-error';
                    errorDiv.className = 'alert alert-danger mt-3';
                    form.insertBefore(errorDiv, document.querySelector('button[type="submit"]').parentNode);
                }

                if (!isValid) {
                    errorDiv.textContent = errorMessage;
                    errorDiv.style.display = 'block';
                } else {
                    errorDiv.style.display = 'none';
                }

                return isValid;
            }

            // Ajouter des écouteurs d'événements pour valider en temps réel
            statutSelect.addEventListener('change', validateStatutClient);
            clientInput.addEventListener('input', validateStatutClient);

            // Exécuter la validation au chargement de la page
            validateStatutClient();

            // Validation du formulaire à la soumission
            form.addEventListener('submit', function(event) {
                if (!form.checkValidity() || !validateStatutClient()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    </script>

<?php
// Inclure le footer commun
require_once __DIR__ . '/../templates/footer.php';
?>
