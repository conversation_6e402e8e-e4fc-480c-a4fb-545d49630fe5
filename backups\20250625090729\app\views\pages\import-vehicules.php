<?php
if (!isset($data)) {
    header('Location: ' . BASE_URL);
    exit;
}

// Ajouter les styles spécifiques à cette page
$data['additional_head_content'] = '
<link rel="stylesheet" href="' . BASE_URL . 'css/style.css">
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<style>
    .container {
        background-color: white;
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        margin-top: 20px;
    }
    .form-group {
        margin-bottom: 15px;
    }
    .alert {
        margin-top: 20px;
    }
    .stats {
        margin-top: 20px;
        padding: 15px;
        border-radius: 5px;
        background-color: #f8f9fa;
    }
    .textarea-container {
        position: relative;
    }
    .textarea-info {
        position: absolute;
        bottom: 5px;
        right: 10px;
        font-size: 0.8em;
        color: #6c757d;
    }
    #csv_data {
        min-height: 200px;
    }
</style>';

// Inclure le header commun
require_once __DIR__ . '/../templates/header.php';
?>

<div class="container">
    <h1>Importation de véhicules</h1>
    <p class="lead">Importez des véhicules à partir de données CSV.</p>

    <?php if (isset($data['error']) && $data['error']): ?>
        <div class="alert alert-danger">
            <strong>Erreur :</strong> <?php echo htmlspecialchars($data['message']); ?>
        </div>
    <?php endif; ?>

    <?php if (isset($data['success']) && $data['success']): ?>
        <div class="alert alert-success">
            <strong>Succès :</strong> <?php echo htmlspecialchars($data['message']); ?>
        </div>

        <?php if (isset($data['stats'])): ?>
            <div class="stats">
                <h4>Statistiques d'importation</h4>
                <ul>
                    <li>Total de lignes traitées : <?php echo $data['stats']['total']; ?></li>
                    <li>Véhicules ajoutés : <?php echo $data['stats']['inserted']; ?></li>
                    <li>Véhicules mis à jour : <?php echo $data['stats']['updated']; ?></li>
                    <li>Lignes ignorées : <?php echo $data['stats']['skipped']; ?></li>
                    <li>Erreurs : <?php echo $data['stats']['errors']; ?></li>
                </ul>

                <?php if (isset($data['errors']) && !empty($data['errors'])): ?>
                    <h5>Détails des erreurs</h5>
                    <ul class="text-danger">
                        <?php foreach ($data['errors'] as $error): ?>
                            <li><?php echo htmlspecialchars($error); ?></li>
                        <?php endforeach; ?>
                    </ul>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>

    <form action="<?php echo BASE_URL; ?>import-vehicules-text" method="POST" class="mt-4">
        <div class="form-group">
            <label for="csv_data">Données CSV (une ligne par véhicule)</label>
            <div class="textarea-container">
                <textarea id="csv_data" name="csv_data" class="form-control" rows="10" required></textarea>
                <div class="textarea-info">Format: référence;libellé;descriptif;libellé_certificat;info complémentaire;certificat;prix</div>
            </div>
            <small class="form-text text-muted">
                Collez vos données CSV ici. Chaque ligne représente un véhicule avec les champs séparés par des points-virgules (;).
            </small>
        </div>

        <div class="form-group">
            <label for="delimiter">Délimiteur</label>
            <select id="delimiter" name="delimiter" class="form-select">
                <option value=";" selected>Point-virgule (;)</option>
                <option value=",">Virgule (,)</option>
                <option value="\t">Tabulation</option>
                <option value="|">Barre verticale (|)</option>
            </select>
        </div>

        <div class="form-check mb-3">
            <input type="checkbox" class="form-check-input" id="skip_header" name="skip_header" value="1">
            <label class="form-check-label" for="skip_header">Ignorer la première ligne (en-tête)</label>
        </div>

        <button type="submit" class="btn btn-primary">Importer</button>
        <a href="<?php echo BASE_URL; ?>vehicules-liste" class="btn btn-secondary">Retour à la liste</a>
    </form>

    <div class="mt-4">
        <h4>Instructions</h4>
        <p>Le format attendu pour chaque ligne est le suivant :</p>
        <ol>
            <li><strong>Référence</strong> (obligatoire) - Identifiant unique du véhicule</li>
            <li><strong>Libellé</strong> - Nom ou titre du véhicule</li>
            <li><strong>Descriptif (partie 1)</strong> - Première partie de la description du véhicule</li>
            <li><strong>Libellé Certificat</strong> - Description du certificat</li>
            <li><strong>Info complémentaire</strong> - Sera ajoutée au descriptif (avec un espace)</li>
            <li><strong>Certificat</strong> - Mettre "N" pour indiquer l'absence d'un certificat, toute autre valeur sera considérée comme présence d'un certificat</li>
            <li><strong>Prix</strong> - Prix du véhicule (nombre décimal)</li>
        </ol>
        <p>Exemples :
            <br><code>DODGE_123;Dodge Challenger 2022;Superbe véhicule;Certificat d'authenticité;Année 2022;Oui;45000</code> (avec certificat)
            <br><code>JEEP_456;Jeep Willys 1944;Véhicule militaire;Sans certificat;Restauré;N;38000</code> (sans certificat)
        </p>
        <p class="text-info"><i>Note: Le champ "descriptif" final sera la combinaison des colonnes 3 et 5 (avec un espace entre les deux si nécessaire).</i></p>
    </div>
</div>

<?php
// Ajouter les scripts spécifiques à cette page
$data['additional_footer_scripts'] = '
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
    // Script pour compter les lignes dans le textarea
    document.getElementById("csv_data").addEventListener("input", function() {
        const lines = this.value.split("\\n").filter(line => line.trim() !== "").length;
        document.querySelector(".textarea-info").textContent = lines + " ligne(s) - Format: référence;libellé;descriptif;libellé_certificat;info complémentaire;certificat;prix";
    });
</script>';

// Inclure le footer commun
require_once __DIR__ . '/../templates/footer.php';
?>
