<?php
// Script simplifié pour tester l'API vehicule-images.php sur le serveur de production
// Ce script exécute l'API directement et affiche uniquement la sortie JSON

// Définir le chemin de base
define('BASE_PATH', dirname(__DIR__));

// Vérifier si une référence est fournie en argument
$reference = isset($argv[1]) ? $argv[1] : 'HALF_TRACK';

// Sauvegarder la valeur actuelle de $_GET
$originalGet = $_GET;

// Définir la référence pour l'API
$_GET['reference'] = $reference;

// Désactiver l'affichage des erreurs
ini_set('display_errors', 0);

// Capturer la sortie de l'API
ob_start();
include BASE_PATH . '/api/vehicule-images.php';
$apiResponse = ob_get_clean();

// Restaurer $_GET
$_GET = $originalGet;

// Afficher la réponse JSON
echo $apiResponse;
?>
