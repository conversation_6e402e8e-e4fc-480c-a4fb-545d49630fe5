<?php
// Inclure les fichiers nécessaires
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../models/VehiculeModel.php';

// Activer les erreurs pour le débogage
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Définir l'en-tête pour JSON
header('Content-Type: application/json');

// Vérifier si la méthode est POST
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Méthode non autorisée']);
    exit;
}

// Récupérer les données JSON du corps de la requête
$json_data = file_get_contents('php://input');
$data = json_decode($json_data, true);

// Vérifier si les données nécessaires sont présentes
if (!isset($data['vehiculeReference']) || !isset($data['email'])) {
    echo json_encode(['success' => false, 'message' => 'Données manquantes']);
    exit;
}

$vehiculeReference = $data['vehiculeReference'];
$email = $data['email'];

try {
    // Initialiser le modèle de véhicule
    $vehiculeModel = new VehiculeModel();

    // Vérifier si le client a déjà acheté un véhicule
    if ($vehiculeModel->clientHasVehicle($email)) {
        echo json_encode([
            'success' => false,
            'message' => 'Vous avez déjà acheté un véhicule. Un seul véhicule peut être acheté par client.'
        ]);
        exit;
    }

    // Vérifier si le véhicule est disponible
    $vehicule = $vehiculeModel->getVehiculeByReference($vehiculeReference);
    if (!$vehicule) {
        error_log("Véhicule avec référence $vehiculeReference non trouvé");
        echo json_encode([
            'success' => false,
            'message' => 'Véhicule non trouvé'
        ]);
        exit;
    }

    // Vérifier le statut du véhicule
    if (!empty($vehicule['statut']) && $vehicule['statut'] !== 'Disponible' && ($vehicule['statut'] !== 'Achat en cours' || $vehicule['client'] !== $email)) {
        error_log("Véhicule avec référence $vehiculeReference non disponible, statut: " . $vehicule['statut'] . ", client: " . $vehicule['client']);
        echo json_encode([
            'success' => false,
            'message' => 'Ce véhicule n\'est pas disponible à l\'achat'
        ]);
        exit;
    }

    error_log("Démarrage du processus d'achat pour le véhicule référence $vehiculeReference, ID: " . $vehicule['id'] . ", libellé: " . $vehicule['libelle'] . ", client: $email");

    // Démarrer le processus d'achat en utilisant la référence (qui est unique)
    if ($vehiculeModel->startPurchaseProcessByReference($vehiculeReference, $email)) {
        error_log("Processus d'achat démarré avec succès pour le véhicule référence $vehiculeReference");
        echo json_encode([
            'success' => true,
            'message' => 'Processus d\'achat démarré avec succès'
        ]);
    } else {
        error_log("Erreur lors du démarrage du processus d'achat pour le véhicule référence $vehiculeReference");
        echo json_encode([
            'success' => false,
            'message' => 'Erreur lors du démarrage du processus d\'achat'
        ]);
    }
} catch (Exception $e) {
    // En cas d'erreur
    error_log("Erreur lors du démarrage du processus d'achat: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Erreur lors du démarrage du processus d\'achat: ' . $e->getMessage()
    ]);
}
