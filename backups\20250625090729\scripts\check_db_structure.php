<?php
// Charger la configuration de la base de données
require_once __DIR__ . '/../app/config/database.php';

// Connexion à la base de données
$conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
if ($conn->connect_error) {
    die('Erreur de connexion: ' . $conn->connect_error);
}

echo "Connexion réussie à la base de données.\n\n";

// Vérifier la structure de la table clients
$result = $conn->query('DESCRIBE clients');
echo "Structure de la table clients:\n";
while ($row = $result->fetch_assoc()) {
    echo "- " . $row['Field'] . " (" . $row['Type'] . ")\n";
}

echo "\n";

// Vérifier les données d'un client connecté
$email = isset($_COOKIE['USERLOG']) ? json_decode($_COOKIE['USERLOG'], true)['email'] : null;
if ($email) {
    echo "Vérification des données pour l'email: $email\n";
    $stmt = $conn->prepare("SELECT * FROM clients WHERE emailclient = ? LIMIT 1");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if ($result->num_rows > 0) {
        $client = $result->fetch_assoc();
        echo "Données du client:\n";
        foreach ($client as $key => $value) {
            echo "- $key: $value\n";
        }
        
        // Vérifier l'adresse de facturation
        $stmt = $conn->prepare("SELECT * FROM adresses_facturation WHERE af_emailclient = ? LIMIT 1");
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $adresseResult = $stmt->get_result();
        
        echo "\nAdresse de facturation:\n";
        if ($adresseResult->num_rows > 0) {
            $adresse = $adresseResult->fetch_assoc();
            foreach ($adresse as $key => $value) {
                echo "- $key: $value\n";
            }
        } else {
            echo "Aucune adresse trouvée pour cet email.\n";
        }
    } else {
        echo "Aucun client trouvé avec cet email.\n";
    }
} else {
    echo "Aucun utilisateur connecté.\n";
}

// Fermer la connexion
$conn->close();
?>
