-- Script pour vérifier la structure de la table clients
DESCRIBE clients;

-- Si la table n'existe pas ou n'a pas la bonne structure, la recréer
DROP TABLE IF EXISTS clients;

CREATE TABLE IF NOT EXISTS clients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    nom VARCHAR(100),
    prenom VARCHAR(100),
    role VARCHAR(50) DEFAULT 'client'
);

-- Insérer un utilisateur de test (mot de passe: test123)
-- Le mot de passe est hashé avec password_hash()
INSERT INTO clients (email, password, nom, prenom, role) VALUES
('<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Utilisateur', 'Test', 'client');
