<?php
if (!isset($data)) {
    header('Location: ' . BASE_URL);
    exit;
}

$vehicules = $data['vehicules'];
$currentPage = $data['currentPage'];
$totalPages = $data['totalPages'];

// Ajouter les styles spécifiques à cette page
$data['additional_head_content'] = '
<link rel="stylesheet" href="' . BASE_URL . 'public/css/style.css">
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>
    .container {
        background-color: white;
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
        margin-top: 20px;
    }
    .form-container {
        background-color: rgba(255, 255, 255, 0.95);
        border-radius: 10px;
        padding: 30px;
        box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
        margin-top: 20px;
        margin-bottom: 30px;
    }
</style>';

// Inclure le header commun
require_once __DIR__ . '/../templates/header.php';
?>
    <div class="container mt-4">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h1>Liste des Véhicules</h1>
            <div>
                <a href="<?php echo BASE_URL; ?>ajouter-vehicule" class="btn btn-success me-2">
                    <i class="fas fa-plus"></i> Ajouter un véhicule
                </a>
                <a href="<?php echo BASE_URL; ?>import-vehicules" class="btn btn-primary">
                    <i class="fas fa-file-import"></i> Importer des véhicules
                </a>
            </div>
        </div>

        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success">
                <?php
                    switch($_GET['success']) {
                        case 'update':
                            echo "Le véhicule a été mis à jour avec succès.";
                            break;
                        case 'delete':
                            echo "Le véhicule a été supprimé avec succès.";
                            break;
                        case 'bon_commande_generated':
                            echo "Le bon de commande a été généré avec succès et enregistré dans le répertoire private.";
                            break;
                        case 'import':
                            echo "Les véhicules ont été importés avec succès.";
                            break;
                        case 'add':
                            echo "Le véhicule a été ajouté avec succès.";
                            break;
                        default:
                            echo "Opération réussie.";
                    }
                ?>
            </div>
        <?php endif; ?>

        <?php
        // Code pour afficher un message si le bon de commande a été envoyé par email (désactivé pour l'instant)
        /*
        if (session_status() == PHP_SESSION_NONE) {
            session_start();
        }

        if (isset($_SESSION['bon_commande_email_sent'])):
            if ($_SESSION['bon_commande_email_sent']): ?>
                <div class="alert alert-success">
                    Le bon de commande a été envoyé par email à l'adresse : <?php echo htmlspecialchars($_SESSION['bon_commande_email']); ?>
                </div>
            <?php else: ?>
                <div class="alert alert-warning">
                    L'envoi du bon de commande par email à l'adresse <?php echo htmlspecialchars($_SESSION['bon_commande_email']); ?> a échoué.
                </div>
            <?php endif;

            // Supprimer les variables de session pour ne pas afficher le message plusieurs fois
            unset($_SESSION['bon_commande_email_sent']);
            unset($_SESSION['bon_commande_email']);
        endif;
        */
        ?>

        <?php if (isset($_GET['error'])): ?>
            <div class="alert alert-danger">
                <?php
                    switch($_GET['error']) {
                        case 'delete_failed':
                            echo "Erreur lors de la suppression du véhicule.";
                            break;
                        case 'vehicule_not_found':
                            echo "Le véhicule demandé n'existe pas.";
                        default:
                            echo "Une erreur est survenue.";
                    }
                ?>
            </div>
        <?php endif; ?>

        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Référence</th>
                        <th>Libellé et Description</th>
                        <th>Certificat</th>
                        <th>Aide Transport</th>
                        <th>Prix</th>
                        <th>Client</th>
                        <th>Statut</th>
                        <th>Date règlement</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($vehicules as $vehicule): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($vehicule['reference']); ?></td>
                        <td>
                            <strong><?php echo htmlspecialchars($vehicule['libelle']); ?></strong>
                            <br>
                            <small class="text-muted"><?php echo htmlspecialchars($vehicule['descriptif']); ?></small>
                        </td>
                        <td><?php echo $vehicule['certificat'] ? 'Oui' : 'Non'; ?></td>
                        <td><?php echo isset($vehicule['aidetransport']) && $vehicule['aidetransport'] ? 'Oui' : 'Non'; ?></td>
                        <td><?php echo number_format($vehicule['prix'], 2, ',', ' ') . ' €'; ?></td>
                        <td><?php echo htmlspecialchars($vehicule['client']); ?></td>
                        <td><?php echo htmlspecialchars($vehicule['statut']); ?></td>
                        <td><?php echo $vehicule['rgltreference'] ? htmlspecialchars($vehicule['rgltreference']) : '-'; ?></td>
                        <td>
                            <div class="btn-group" role="group">
                                <a href="<?php echo strtolower(BASE_URL . 'modifier-vehicule?id=' . $vehicule['id']); ?>"
                                   class="btn btn-sm btn-primary">Modifier</a>

                                <button type="button"
                                        onclick="confirmDelete(<?php echo $vehicule['id']; ?>)"
                                        class="btn btn-sm btn-danger">Supprimer</button>

                                <a href="<?php echo strtolower(BASE_URL . 'generer-bon?id=' . $vehicule['id']); ?>"
                                   class="btn btn-sm btn-success">Bon de commande</a>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <?php if ($totalPages > 1): ?>
        <nav aria-label="Navigation des pages">
            <ul class="pagination justify-content-center">
                <?php if ($currentPage > 1): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?php echo BASE_URL; ?>vehicules-liste?page=<?php echo ($currentPage - 1); ?>">Précédent</a>
                    </li>
                <?php endif; ?>

                <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <li class="page-item <?php echo $i === $currentPage ? 'active' : ''; ?>">
                        <a class="page-link" href="<?php echo BASE_URL; ?>vehicules-liste?page=<?php echo $i; ?>"><?php echo $i; ?></a>
                    </li>
                <?php endfor; ?>

                <?php if ($currentPage < $totalPages): ?>
                    <li class="page-item">
                        <a class="page-link" href="<?php echo BASE_URL; ?>vehicules-liste?page=<?php echo ($currentPage + 1); ?>">Suivant</a>
                    </li>
                <?php endif; ?>
            </ul>
        </nav>
        <?php endif; ?>
    </div>

<?php
// Ajouter les scripts spécifiques à cette page
$data['additional_footer_scripts'] = '
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script>
    function confirmDelete(id) {
        if (confirm("Êtes-vous sûr de vouloir supprimer ce véhicule ?")) {
            window.location.href = "' . BASE_URL . 'supprimer-vehicule?id=" + id;
        }
    }
</script>';

// Inclure le footer commun
require_once __DIR__ . '/../templates/footer.php';
?>