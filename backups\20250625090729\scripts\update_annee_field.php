<?php
// Script pour mettre à jour la taille du champ année dans la table vehicules

// Charger la configuration
require_once __DIR__ . '/../app/config/config.php';
require_once __DIR__ . '/../app/config/database.php';

// Fonction pour afficher un message
function output($message) {
    echo $message . "\n";
}

output("=== Mise à jour de la taille du champ année dans la table vehicules ===");

// Établir une connexion à la base de données
try {
    $conn = getDbConnection();
    output("Connexion à la base de données établie avec succès");
} catch (Exception $e) {
    output("Erreur de connexion à la base de données: " . $e->getMessage());
    exit(1);
}

// Vérifier la taille actuelle du champ année
$sql = "SHOW COLUMNS FROM vehicules LIKE 'annee'";
$result = $conn->query($sql);

if ($result && $result->num_rows > 0) {
    $column = $result->fetch_assoc();
    output("Taille actuelle du champ année: " . $column['Type']);
    
    // Vérifier si la taille est déjà de 50 caractères
    if ($column['Type'] === 'varchar(50)') {
        output("Le champ année a déjà la taille souhaitée (50 caractères)");
    } else {
        // Modifier la taille du champ année
        $sql = "ALTER TABLE vehicules MODIFY COLUMN annee VARCHAR(50)";
        
        if ($conn->query($sql) === TRUE) {
            output("SUCCÈS: La taille du champ année a été modifiée avec succès");
            
            // Vérifier que la modification a bien été effectuée
            $sql = "SHOW COLUMNS FROM vehicules LIKE 'annee'";
            $result = $conn->query($sql);
            
            if ($result && $result->num_rows > 0) {
                $column = $result->fetch_assoc();
                output("Nouvelle taille du champ année: " . $column['Type']);
            }
        } else {
            output("ERREUR: Échec de la modification de la taille du champ année");
            output("Erreur MySQL: " . $conn->error);
        }
    }
} else {
    output("ERREUR: Impossible de récupérer les informations sur le champ année");
    output("Erreur MySQL: " . $conn->error);
}

// Fermer la connexion
$conn->close();

output("\n=== Fin de la mise à jour ===");
?>
