<?php

// Charger la configuration de la base de données
require_once __DIR__ . '/../config/database.php';

class UtilisateurController {

    private $db;

    public function __construct() {
        // Démarrer la session si ce n'est pas déjà fait
        if (session_status() === PHP_SESSION_NONE) {
            session_start();
        }

        // Connexion à la base de données des clients
        $this->db = getClientsDbConnection();
    }

    /**
     * Méthode pour gérer la connexion de l'utilisateur
     */
    public function connexion() {
        // Vérifier si le formulaire a été soumis
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['loginform'])) {
            $email = $_POST['email'] ?? '';
            $password = $_POST['password'] ?? '';

            // Validation basique
            if (empty($email) || empty($password)) {
                // Rediriger avec un message d'erreur
                $this->redirectWithError('Veuillez remplir tous les champs.');
                exit;
            }

            // Vérifier les identifiants dans la base de données
            $stmt = $this->db->prepare("SELECT * FROM clients WHERE emailclient = ? LIMIT 1");
            $stmt->bind_param("s", $email);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $client = $result->fetch_assoc();

                // Vérifier le mot de passe (en supposant qu'il est stocké avec MD5 car le champ est varchar(32))
                if (md5($password) === $client['passwdclient']) {
                    // Créer le cookie de session USERLOG
                    setcookie('USERLOG', json_encode([
                        'email' => $client['emailclient'],
                        'nom' => $client['nomclient'],
                        'prenom' => $client['prenomclient'],
                        'role' => $client['isadmin'] ? 'admin' : 'client'
                    ]), time() + 86400, '/'); // Cookie valide pour 24 heures

                    // Stocker également les informations dans la session
                    $_SESSION['USER'] = [
                        'email' => $client['emailclient'],
                        'nom' => $client['nomclient'],
                        'prenom' => $client['prenomclient'],
                        'role' => $client['isadmin'] ? 'admin' : 'client'
                    ];

                    // Rediriger vers la page d'accueil
                    header('Location: ' . BASE_URL);
                    exit;
                }
            }

            // Identifiants incorrects
            $this->redirectWithError('Identifiants incorrects. Veuillez réessayer.');
            exit;
        } else {
            // Rediriger vers la page d'accueil avec le formulaire de connexion
            header('Location: ' . BASE_URL);
            exit;
        }
    }

    /**
     * Méthode pour gérer la déconnexion de l'utilisateur
     */
    public function deconnexion() {
        // Supprimer le cookie USERLOG
        setcookie('USERLOG', '', time() - 3600, '/');

        // Supprimer la session
        $_SESSION = [];

        // Supprimer le cookie de session
        if (ini_get("session.use_cookies")) {
            $params = session_get_cookie_params();
            setcookie(session_name(), '', time() - 42000,
                $params["path"], $params["domain"],
                $params["secure"], $params["httponly"]
            );
        }

        // Détruire la session
        session_destroy();

        // Rediriger vers la page d'accueil
        header('Location: ' . BASE_URL);
        exit;
    }

    /**
     * Méthode pour afficher le profil de l'utilisateur
     */
    public function profil() {
        // Vérifier si l'utilisateur est connecté
        if (!isset($_SESSION['USER']) && !isset($_COOKIE['USERLOG'])) {
            header('Location: ' . BASE_URL);
            exit;
        }

        // Si l'utilisateur est connecté via cookie mais pas via session
        if (!isset($_SESSION['USER']) && isset($_COOKIE['USERLOG'])) {
            $_SESSION['USER'] = json_decode($_COOKIE['USERLOG'], true);
        }

        // Récupérer l'email de l'utilisateur (clé principale)
        $email = $_SESSION['USER']['email'];

        // Récupérer les informations du client par email (clé principale)
        $stmt = $this->db->prepare("SELECT * FROM clients WHERE emailclient = ? LIMIT 1");
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $client = $result->fetch_assoc();

            // Récupérer l'adresse de facturation du client
            $stmt = $this->db->prepare("SELECT * FROM adresses_facturation WHERE af_emailclient = ? LIMIT 1");
            $stmt->bind_param("s", $email);
            $stmt->execute();
            $adresseResult = $stmt->get_result();

            $adresse = null;
            if ($adresseResult->num_rows > 0) {
                $adresse = $adresseResult->fetch_assoc();
            }

            // Préparer les données pour la vue
            $data = [
                'title' => 'Mon Profil - Jeep Dodge GMC',
                'description' => 'Gérez votre profil utilisateur',
                'client' => $client,
                'adresse' => $adresse
            ];

            // Afficher la page de profil avec les données du client
            $this->view('utilisateur/profil', $data);
        } else {
            // Session invalide, déconnecter l'utilisateur
            $this->deconnexion();
        }
    }

    /**
     * Méthode pour afficher la page de mot de passe oublié
     */
    public function mot_de_passe_oublie() {
        // Préparer les données pour la vue
        $data = [
            'title' => 'Mot de passe oublié - Jeep Dodge GMC',
            'description' => 'Récupérez votre mot de passe'
        ];

        // Afficher le formulaire de récupération de mot de passe
        $this->view('utilisateur/mot_de_passe_oublie', $data);
    }

    /**
     * Méthode pour traiter la demande de récupération de mot de passe
     */
    public function recuperer_mot_de_passe() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: ' . BASE_URL . 'utilisateur/mot_de_passe_oublie');
            exit;
        }

        $email = $_POST['email'] ?? '';

        if (empty($email)) {
            $_SESSION['error_message'] = 'Veuillez entrer votre adresse e-mail.';
            header('Location: ' . BASE_URL . 'utilisateur/mot_de_passe_oublie');
            exit;
        }

        // Vérifier si l'email existe dans la base de données
        $stmt = $this->db->prepare("SELECT * FROM clients WHERE emailclient = ? LIMIT 1");
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows === 0) {
            $_SESSION['error_message'] = 'Aucun compte n\'est associé à cette adresse e-mail.';
            header('Location: ' . BASE_URL . 'utilisateur/mot_de_passe_oublie');
            exit;
        }

        // Dans une application réelle, nous enverrions un e-mail avec un lien de réinitialisation
        // Pour cette démo, nous affichons simplement un message de succès
        $_SESSION['success_message'] = 'Un e-mail de réinitialisation a été envoyé à votre adresse. Veuillez vérifier votre boîte de réception.';
        header('Location: ' . BASE_URL . 'utilisateur/mot_de_passe_oublie');
        exit;
    }

    /**
     * Méthode pour afficher la page d'inscription
     */
    public function s_inscrire() {
        // Préparer les données pour la vue
        $data = [
            'title' => 'Inscription - Jeep Dodge GMC',
            'description' => 'Créez votre compte utilisateur'
        ];

        // Afficher le formulaire d'inscription
        $this->view('utilisateur/inscription', $data);
    }

    /**
     * Méthode pour traiter l'inscription d'un nouvel utilisateur
     */
    public function inscription_traitement() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: ' . BASE_URL . 'utilisateur/s_inscrire');
            exit;
        }

        // Récupérer les données du formulaire pour le client
        $prenom = $_POST['prenom'] ?? '';
        $nom = $_POST['nom'] ?? '';
        $email = $_POST['email'] ?? '';
        $password = $_POST['password'] ?? '';
        $password_confirm = $_POST['password_confirm'] ?? '';

        // Récupérer les données du formulaire pour l'adresse de facturation
        $raisonsocial = $_POST['raisonsocial'] ?? '';
        $nomrue = $_POST['nomrue'] ?? '';
        $codepostal = $_POST['codepostal'] ?? '';
        $ville = $_POST['ville'] ?? '';
        $pays = $_POST['pays'] ?? 'France';

        // Validation basique pour le client
        if (empty($prenom) || empty($nom) || empty($email) || empty($password) || empty($password_confirm)) {
            $_SESSION['error_message'] = 'Veuillez remplir tous les champs du profil.';
            header('Location: ' . BASE_URL . 'utilisateur/s_inscrire');
            exit;
        }

        // Validation basique pour l'adresse
        if (empty($raisonsocial) || empty($nomrue) || empty($codepostal) || empty($ville) || empty($pays)) {
            $_SESSION['error_message'] = 'Veuillez remplir tous les champs de l\'adresse.';
            header('Location: ' . BASE_URL . 'utilisateur/s_inscrire');
            exit;
        }

        // Vérifier que les mots de passe correspondent
        if ($password !== $password_confirm) {
            $_SESSION['error_message'] = 'Les mots de passe ne correspondent pas.';
            header('Location: ' . BASE_URL . 'utilisateur/s_inscrire');
            exit;
        }

        // Vérifier si l'email existe déjà
        $stmt = $this->db->prepare("SELECT * FROM clients WHERE emailclient = ? LIMIT 1");
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $_SESSION['error_message'] = 'Cette adresse e-mail est déjà utilisée.';
            header('Location: ' . BASE_URL . 'utilisateur/s_inscrire');
            exit;
        }

        // Démarrer une transaction pour assurer l'intégrité des données
        $this->db->begin_transaction();

        try {
            // Hasher le mot de passe avec MD5 pour correspondre à la structure existante
            $hashed_password = md5($password);

            // Date actuelle pour datecreate
            $date = date('Y-m-d');

            // Valeur par défaut pour isadmin (0 = client)
            $isadmin = 0;

            // Insérer le nouvel utilisateur dans la base de données
            // L'email est la clé principale, donc pas besoin de générer un UID
            $stmt = $this->db->prepare("INSERT INTO clients (emailclient, passwdclient, nomclient, prenomclient, isadmin, datecreate) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->bind_param("ssssis", $email, $hashed_password, $nom, $prenom, $isadmin, $date);

            if (!$stmt->execute()) {
                throw new Exception('Erreur lors de l\'insertion du client: ' . $stmt->error);
            }

            // Insérer l'adresse de facturation
            $stmt = $this->db->prepare("INSERT INTO adresses_facturation (af_emailclient, af_raisonsocial, af_nomrue, af_codepostal, af_ville, af_pays) VALUES (?, ?, ?, ?, ?, ?)");
            $stmt->bind_param("ssssss", $email, $raisonsocial, $nomrue, $codepostal, $ville, $pays);

            if (!$stmt->execute()) {
                throw new Exception('Erreur lors de l\'insertion de l\'adresse: ' . $stmt->error);
            }

            // Valider la transaction
            $this->db->commit();

            // Créer le cookie de session USERLOG
            setcookie('USERLOG', json_encode([
                'email' => $email,
                'nom' => $nom,
                'prenom' => $prenom,
                'role' => 'client'
            ]), time() + 86400, '/'); // Cookie valide pour 24 heures

            // Stocker également les informations dans la session
            $_SESSION['USER'] = [
                'email' => $email,
                'nom' => $nom,
                'prenom' => $prenom,
                'role' => 'client'
            ];

            // Rediriger vers la page d'accueil
            header('Location: ' . BASE_URL);
            exit;

        } catch (Exception $e) {
            // Annuler la transaction en cas d'erreur
            $this->db->rollback();

            $_SESSION['error_message'] = 'Une erreur est survenue lors de l\'inscription: ' . $e->getMessage();
            header('Location: ' . BASE_URL . 'utilisateur/s_inscrire');
            exit;
        }
    }

    /**
     * Méthode helper pour rediriger avec un message d'erreur
     */
    private function redirectWithError($message) {
        $_SESSION['error_message'] = $message;
        header('Location: ' . BASE_URL);
    }

    /**
     * Méthode helper pour charger une vue
     */
    private function view($view, $data = []) {
        // Construire le chemin complet vers la vue
        $viewPath = __DIR__ . '/../views/' . $view . '.php';

        // Vérifier si le fichier de vue existe
        if (file_exists($viewPath)) {
            // Démarrer la mise en tampon de sortie
            ob_start();

            // Extraire les données pour les rendre disponibles dans la vue
            extract($data);

            // Inclure la vue
            include $viewPath;

            // Récupérer le contenu du tampon et l'afficher
            $content = ob_get_clean();
            echo $content;
        } else {
            // Gérer l'erreur si la vue n'existe pas
            die('La vue ' . $view . ' n\'existe pas. Chemin: ' . $viewPath);
        }
    }
}
