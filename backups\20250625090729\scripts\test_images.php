<?php
// Script de test pour vérifier la détection des images

// Définir le chemin de base
define('BASE_PATH', __DIR__);

// Référence à tester
$reference = 'HALF_TRACK';

// Répertoire des images
$directory = BASE_PATH . '/public/img/autres/';

// Si la référence contient des underscores, les remplacer par des espaces pour la recherche
$searchRef = str_replace('_', ' ', $reference);

echo "Recherche d'images pour la référence: " . $reference . " (searchRef: " . $searchRef . ")\n";
echo "Répertoire de recherche: " . $directory . "\n\n";

// Vérifier si le répertoire existe
if (!is_dir($directory)) {
    echo "Le répertoire n'existe pas: " . $directory . "\n";
    exit;
}

// Lister tous les fichiers dans le répertoire
echo "Tous les fichiers dans le répertoire:\n";
$allFiles = scandir($directory);
foreach ($allFiles as $file) {
    if ($file != '.' && $file != '..') {
        echo "- " . $file . "\n";
    }
}
echo "\n";

// Chercher l'image exacte d'abord
echo "Recherche de l'image exacte: " . $searchRef . ".{jpg,jpeg,png,gif}\n";
$exactMatch = glob($directory . $searchRef . '.{jpg,jpeg,png,gif}', GLOB_BRACE);
if (!empty($exactMatch)) {
    echo "Images exactes trouvées:\n";
    foreach ($exactMatch as $path) {
        echo "- " . $path . "\n";
    }
} else {
    echo "Aucune image exacte trouvée.\n";
}
echo "\n";

// Chercher des images avec préfixe
echo "Recherche d'images avec préfixe: " . $reference . "_*.{jpg,jpeg,png,gif}\n";
$pattern = $reference . '_*.{jpg,jpeg,png,gif}';
$images = glob($directory . $pattern, GLOB_BRACE);
if (!empty($images)) {
    echo "Images avec préfixe trouvées:\n";
    foreach ($images as $path) {
        echo "- " . $path . "\n";
    }
} else {
    echo "Aucune image avec préfixe trouvée.\n";
}
echo "\n";

// Essayer avec le nom sans underscore
echo "Recherche d'images sans underscore: " . $searchRef . ".{jpg,jpeg,png,gif}\n";
$pattern = $searchRef . '.{jpg,jpeg,png,gif}';
$images2 = glob($directory . $pattern, GLOB_BRACE);
if (!empty($images2)) {
    echo "Images sans underscore trouvées:\n";
    foreach ($images2 as $path) {
        echo "- " . $path . "\n";
    }
} else {
    echo "Aucune image sans underscore trouvée.\n";
}
echo "\n";

// Chercher toutes les images qui contiennent la référence
echo "Recherche de toutes les images contenant la référence:\n";
$allImages = glob($directory . '*' . $searchRef . '*.{jpg,jpeg,png,gif}', GLOB_BRACE);
if (!empty($allImages)) {
    echo "Images trouvées:\n";
    foreach ($allImages as $path) {
        echo "- " . $path . "\n";
    }
} else {
    echo "Aucune image trouvée.\n";
}
?>
