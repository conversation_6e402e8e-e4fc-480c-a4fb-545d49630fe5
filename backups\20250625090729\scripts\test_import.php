<?php
// Script de test pour l'importation de véhicules

// Charger la configuration
require_once __DIR__ . '/../app/config/config.php';
require_once __DIR__ . '/../app/models/VehiculeModel.php';

// Créer une instance du modèle
$vehiculeModel = new VehiculeModel();

// Données de test
$testData = [
    [
        'reference' => 'TEST_001',
        'libelle' => 'Véhicule de test 1',
        'descriptif' => 'Description du véhicule de test 1',
        'libelle_certificat' => 'Certificat de test',
        'annee' => '2023',
        'certificat' => 1,
        'prix' => 25000.00,
        'statut' => 'Disponible',
        'categorie' => 'AUTRE VEHICULE',
        'client' => '',
        'client_adresse' => '',
        'rgltreference' => '',
        'dateheureachat' => null
    ],
    [
        'reference' => 'TEST_002',
        'libelle' => 'Véhicule de test 2',
        'descriptif' => 'Description du véhicule de test 2',
        'libelle_certificat' => 'Certificat de test 2',
        'annee' => '2022',
        'certificat' => 0,
        'prix' => 18500.50,
        'statut' => 'Disponible',
        'categorie' => 'AUTRE VEHICULE',
        'client' => '',
        'client_adresse' => '',
        'rgltreference' => '',
        'dateheureachat' => null
    ]
];

// Tester l'ajout de véhicules
echo "Test d'importation de véhicules\n";
echo "==============================\n\n";

foreach ($testData as $index => $data) {
    echo "Test " . ($index + 1) . " - Ajout/Mise à jour du véhicule " . $data['reference'] . "\n";
    
    $result = $vehiculeModel->addVehicule($data);
    
    echo "Résultat: " . ($result['success'] ? 'Succès' : 'Échec') . "\n";
    echo "Action: " . ($result['action'] ?? 'N/A') . "\n";
    echo "Message: " . ($result['message'] ?? 'Aucun message') . "\n\n";
}

// Vérifier que les véhicules ont été ajoutés
echo "Vérification des véhicules ajoutés\n";
echo "================================\n\n";

foreach ($testData as $data) {
    $vehicule = $vehiculeModel->getVehiculeByReference($data['reference']);
    
    if ($vehicule) {
        echo "Véhicule " . $data['reference'] . " trouvé dans la base de données.\n";
        echo "ID: " . $vehicule['id'] . "\n";
        echo "Libellé: " . $vehicule['libelle'] . "\n";
        echo "Prix: " . $vehicule['prix'] . " €\n\n";
    } else {
        echo "Véhicule " . $data['reference'] . " NON trouvé dans la base de données.\n\n";
    }
}

echo "Test terminé.\n";
