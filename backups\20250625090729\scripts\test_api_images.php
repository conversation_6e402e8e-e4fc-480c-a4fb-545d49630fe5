<?php
// Script pour tester l'API de récupération des images

// Définir les en-têtes pour permettre l'accès AJAX
header('Content-Type: text/html');

// Vérifier si APP_PATH est défini
if (!defined('APP_PATH')) {
    define('APP_PATH', dirname(__DIR__) . '/app');
}

// Charger la configuration
require_once APP_PATH . '/config/config.php';

// Référence à tester
$reference = isset($_GET['reference']) ? $_GET['reference'] : 'HALF_TRACK';

// URL de l'API
$apiUrl = BASE_URL . 'api/vehicule-images.php?reference=' . urlencode($reference);

// Fonction pour faire une requête HTTP
function makeRequest($url) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    $response = curl_exec($ch);
    $info = curl_getinfo($ch);
    curl_close($ch);
    
    return [
        'response' => $response,
        'info' => $info
    ];
}

// Faire la requête à l'API
$result = makeRequest($apiUrl);
$response = $result['response'];
$info = $result['info'];

// Décoder la réponse JSON
$data = json_decode($response, true);
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test API Images</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #333;
        }
        pre {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .debug-info {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .image-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        .image-card img {
            width: 100%;
            height: 300px;
            object-fit: contain;
            background-color: #f9f9f9;
        }
        .image-info {
            padding: 10px;
        }
        .exists {
            color: green;
        }
        .not-exists {
            color: red;
        }
        .request-info {
            margin-bottom: 20px;
        }
        .request-info table {
            width: 100%;
            border-collapse: collapse;
        }
        .request-info table th, .request-info table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .request-info table th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>Test API Images pour <?php echo htmlspecialchars($reference); ?></h1>
    
    <div class="request-info">
        <h2>Informations de la requête</h2>
        <table>
            <tr>
                <th>URL</th>
                <td><?php echo htmlspecialchars($apiUrl); ?></td>
            </tr>
            <tr>
                <th>Code HTTP</th>
                <td><?php echo $info['http_code']; ?></td>
            </tr>
            <tr>
                <th>Content Type</th>
                <td><?php echo $info['content_type']; ?></td>
            </tr>
            <tr>
                <th>Temps total</th>
                <td><?php echo $info['total_time']; ?> secondes</td>
            </tr>
        </table>
    </div>
    
    <?php if ($data): ?>
        <div class="debug-info">
            <h2>Informations de débogage</h2>
            <p><strong>Référence:</strong> <?php echo htmlspecialchars($data['debug']['reference'] ?? 'Non disponible'); ?></p>
            <p><strong>Nombre d'images trouvées:</strong> <?php echo $data['debug']['count'] ?? 0; ?></p>
            <p><strong>Timestamp:</strong> <?php echo htmlspecialchars($data['debug']['timestamp'] ?? 'Non disponible'); ?></p>
            
            <h3>Vérification des fichiers:</h3>
            <table>
                <tr>
                    <th>Chemin</th>
                    <th>Statut</th>
                    <th>Chemin absolu</th>
                    <th>URL originale</th>
                </tr>
                <?php foreach ($data['debug']['files_check'] as $path => $info): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($path); ?></td>
                        <td class="<?php echo $info['exists'] === 'Existe' ? 'exists' : 'not-exists'; ?>">
                            <?php echo htmlspecialchars($info['exists']); ?>
                        </td>
                        <td><?php echo htmlspecialchars($info['absolute_path']); ?></td>
                        <td><?php echo htmlspecialchars($info['original_url'] ?? 'N/A'); ?></td>
                    </tr>
                <?php endforeach; ?>
            </table>
        </div>
        
        <h2>Images trouvées</h2>
        <div class="image-grid">
            <?php foreach ($data['images'] as $index => $image): ?>
                <div class="image-card">
                    <img src="<?php echo htmlspecialchars($image); ?>" alt="Image <?php echo $index + 1; ?>" onerror="this.onerror=null; this.src='<?php echo BASE_URL; ?>public/img/image_not_found.png'; this.alt='Image non disponible';">
                    <div class="image-info">
                        <p><strong>URL:</strong> <?php echo htmlspecialchars($image); ?></p>
                        <p><strong>Index:</strong> <?php echo $index; ?></p>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    <?php else: ?>
        <div class="error">
            <h2>Erreur</h2>
            <p>Impossible de décoder la réponse JSON.</p>
            <h3>Réponse brute:</h3>
            <pre><?php echo htmlspecialchars($response); ?></pre>
        </div>
    <?php endif; ?>
    
    <script>
        // Afficher les erreurs de chargement d'image dans la console
        document.querySelectorAll('img').forEach(img => {
            img.addEventListener('error', function() {
                console.error('Erreur de chargement de l\'image:', this.src);
            });
        });
    </script>
</body>
</html>
