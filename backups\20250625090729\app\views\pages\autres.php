<?php
// Démarrer la session si ce n'est pas déjà fait
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Vérifier si APP_PATH est défini
if (!defined('APP_PATH')) {
    define('APP_PATH', dirname(dirname(__DIR__)));
}

// Inclure la configuration Scellius
require_once __DIR__ . '/../../scellius/scellius_module/config.php';
// Inclure le modèle VehiculeModel
require_once APP_PATH . '/models/VehiculeModel.php';

// Fonction pour générer la signature Scellius V2
function calculate_scellius_signature(array $fields, string $secretKey, string $algo = SCELLIUS_SIGN_ALGO): string {
    ksort($fields);
    $stringToSign = '';
    foreach ($fields as $key => $value) {
        if (strpos($key, 'vads_') === 0) {
            $stringToSign .= $value . '+';
        }
    }
    $stringToSign .= $secretKey;
    return base64_encode(hash_hmac(strtolower(str_replace('-', '', $algo)), $stringToSign, $secretKey, true));
}

// Fonction pour vérifier si un client a déjà acheté un véhicule
function clientHasVehicle() {
    // Vérifier si l'utilisateur est connecté
    if (!isset($_SESSION['USER']) && !isset($_COOKIE['USERLOG'])) {
        return false;
    }

    // Si l'utilisateur est connecté via cookie mais pas via session
    if (!isset($_SESSION['USER']) && isset($_COOKIE['USERLOG'])) {
        $_SESSION['USER'] = json_decode($_COOKIE['USERLOG'], true);
    }

    // Vérifier si l'email est présent dans la session
    if (!isset($_SESSION['USER']['email'])) {
        return false;
    }

    $email = $_SESSION['USER']['email'];

    // Initialiser le modèle de véhicule
    $vehiculeModel = new VehiculeModel();

    // Vérifier si le client a déjà acheté un véhicule
    return $vehiculeModel->clientHasVehicle($email);
}

// Fonction pour vérifier si un client a un véhicule en cours d'achat
function getClientPendingVehicle() {
    // Vérifier si l'utilisateur est connecté
    if (!isset($_SESSION['USER']) && !isset($_COOKIE['USERLOG'])) {
        return null;
    }

    // Si l'utilisateur est connecté via cookie mais pas via session
    if (!isset($_SESSION['USER']) && isset($_COOKIE['USERLOG'])) {
        $_SESSION['USER'] = json_decode($_COOKIE['USERLOG'], true);
    }

    // Vérifier si l'email est présent dans la session
    if (!isset($_SESSION['USER']['email'])) {
        return null;
    }

    $email = $_SESSION['USER']['email'];

    // Initialiser le modèle de véhicule
    $vehiculeModel = new VehiculeModel();

    // Récupérer le véhicule en cours d'achat du client
    return $vehiculeModel->getClientPendingVehicle($email);
}

// Ajouter les styles spécifiques à cette page
$data['additional_head_content'] = '<link rel="stylesheet" href="' . BASE_URL . 'public/css/autres.css">';

// Inclure le header commun
require_once __DIR__ . '/../templates/header.php';
?>

    <div id="site">
        <div id="middle">
            <div class="form-style-10">
                <?php
                // Vérifier si nous sommes dans la période de vente flash
                $currentDateTime = new DateTime();
                $debutFlashDate = new DateTime(DEBUT_FLASH_DATE);
                $finFlashDate = new DateTime(FIN_FLASH_DATE);
                $isFlashSalePeriod = ($currentDateTime >= $debutFlashDate && $currentDateTime <= $finFlashDate);

                // Afficher un message en fonction de la période
                if ($isFlashSalePeriod): ?>
                    <div style="background-color: #28a745; color: white; padding: 10px; margin-bottom: 15px; border-radius: 5px; text-align: center;">
                        <strong>Vente Flash en cours !</strong> Vous pouvez acheter les véhicules disponibles jusqu'au <?php echo $finFlashDate->format('d/m/Y à H:i'); ?>.
                    </div>
                <?php elseif ($currentDateTime < $debutFlashDate): ?>
                    <div style="background-color: #007bff; color: white; padding: 10px; margin-bottom: 15px; border-radius: 5px; text-align: center;">
                        <strong>Prochaine Vente Flash :</strong> Les achats seront possibles le <?php echo $debutFlashDate->format('d/m/Y à H:i'); ?> jusqu'au <?php echo $finFlashDate->format('d/m/Y à H:i'); ?>.
                    </div>
                <?php else: ?>
                    <div style="background-color: #dc3545; color: white; padding: 10px; margin-bottom: 15px; border-radius: 5px; text-align: center;">
                        <strong>Vente Flash terminée.</strong> La dernière vente flash s'est terminée le <?php echo $finFlashDate->format('d/m/Y à H:i'); ?>. Restez à l'affût pour la prochaine !
                    </div>
                <?php endif; ?>
                <div class="filter-container">
                    <form method="GET" action="" class="filter-form">
                        <div class="filter-item">
                            <input type="text" name="search" placeholder="Rechercher par référence, libellé ou descriptif..." value="<?php echo htmlspecialchars($data['current_search'] ?? ''); ?>" class="search-input">
                        </div>
                        <div class="filter-item">
                            <select name="annee" class="year-select">
                                <option value="all">Tous les modèles</option>
                                <?php if (is_array($data['models']) || is_object($data['models'])): ?>
                                    <?php foreach ($data['models'] as $model): ?>
                                        <option value="<?php echo htmlspecialchars($model); ?>" <?php echo ($data['current_year'] == $model) ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($model); ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                        <div class="filter-item">
                            <label class="checkbox-container">
                                <input type="checkbox" name="showVendus" <?php echo (!$data['hide_vendus']) ? 'checked' : ''; ?>>
                                <span class="checkbox-text">Afficher les véhicules vendus</span>
                            </label>
                        </div>
                        <div class="filter-item">
                            <button type="submit" class="filter-button">Filtrer</button>
                            <?php if (!empty($data['current_search']) || (!empty($data['current_year']) && $data['current_year'] !== 'all') || isset($_GET['showVendus'])): ?>
                                <a href="?" class="reset-button">Réinitialiser</a>
                            <?php endif; ?>
                        </div>
                    </form>
                </div>
                <div class="grid-container">
                    <?php if (is_array($data['vehicules']) || is_object($data['vehicules'])): ?>
                        <?php foreach ($data['vehicules'] as $vehicule):
                            // Récupérer la référence pour l'image
                            $reference = $vehicule['reference'];

                            // Utiliser le modèle VehiculeModel pour récupérer les images
                            require_once APP_PATH . '/models/VehiculeModel.php';
                            $vehiculeModel = new VehiculeModel();
                            $images = $vehiculeModel->getVehiculeImages($reference);

                            // Utiliser la première image trouvée ou l'image par défaut
                            if (!empty($images)) {
                                $imagePath = $images[0];
                            } else {
                                $imagePath = "public/img/image_not_found.png";
                            }
                        ?>
                    <div class="inner-wrap2">
                        <div class="vehicle-card">
                            <?php if (!empty($vehicule['prix']) && $vehicule['prix'] > 0): ?>
                                <div class="vehicle-reference" style="text-align: right; font-size: 12px; color: #6c757d; margin-bottom: 2px; font-weight: bold;">
                                    Réf: <?php echo htmlspecialchars($vehicule['reference']); ?>
                                </div>
                                <div class="vehicle-price"><?php echo number_format($vehicule['prix'], 2, ',', ' '); ?> €</div>
                                <?php if (!empty($vehicule['certificat']) && $vehicule['certificat'] == TRUE): ?>
                                <div class="certificate-container">
                                    <input type="checkbox" class="certificate-checkbox" id="certificat-<?php echo $vehicule['id']; ?>" data-price="<?php echo $vehicule['prix']; ?>" data-id="<?php echo $vehicule['id']; ?>" />
                                    <label for="certificat-<?php echo $vehicule['id']; ?>">Certificat</label>
                                </div>
                                <?php endif; ?>
                            <?php endif; ?>
                            <div class="vehicle-image-container">
                                <img src="<?php echo BASE_URL . $imagePath; ?>" width="240"
                                     alt="<?php echo htmlspecialchars($vehicule['libelle']); ?>"
                                     class="gallery-image"
                                     data-reference="<?php echo htmlspecialchars($reference); ?>"
                                     onclick="openGallery(this)"
                                     onerror="this.src='<?php echo BASE_URL; ?>public/img/image_not_found.png';"/>
                                <?php if (!empty($vehicule['statut']) && $vehicule['statut'] === 'Vendu'): ?>
                                    <div class="sold-label">Vendu</div>
                                <?php elseif (!empty($vehicule['statut']) && $vehicule['statut'] === 'Achat en cours'): ?>
                                    <div class="purchase-in-progress-label">Achat en cours</div>
                                <?php endif; ?>
                            </div>
                            <div style="display: flex; justify-content: space-between; align-items: center;">
                                <div>
                                    <div class="title_autre" style="text-align: left;">
                                        <?php echo htmlspecialchars($vehicule['libelle']); ?>
                                        <?php if (!empty($vehicule['annee'])): ?>
                                            (<?php echo htmlspecialchars($vehicule['annee']); ?>)
                                        <?php endif; ?>
                                    </div>
                                    <div class="subtitle_autre" style="text-align: left;">
                                        <?php echo htmlspecialchars($vehicule['descriptif']); ?>
                                    </div>
                                </div>
                                <?php
                                // Vérifier si l'utilisateur est connecté
                                $isUserLoggedIn = isset($_SESSION['USER']) || isset($_COOKIE['USERLOG']);
                                $userEmail = '';
                                if ($isUserLoggedIn) {
                                    if (!isset($_SESSION['USER']) && isset($_COOKIE['USERLOG'])) {
                                        $_SESSION['USER'] = json_decode($_COOKIE['USERLOG'], true);
                                    }
                                    $userEmail = $_SESSION['USER']['email'] ?? '';
                                }

                                // Vérifier si le véhicule est disponible ou si c'est un achat en cours du client actuel
                                $isVehiculeAvailable = (empty($vehicule['statut']) || $vehicule['statut'] === 'Disponible');
                                $isClientPendingPurchase = ($vehicule['statut'] === 'Achat en cours' && $vehicule['client'] === $userEmail);
                                $canBuyVehicule = $isVehiculeAvailable || $isClientPendingPurchase;

                                // Vérifier si nous sommes dans la période de vente flash
                                $currentDateTime = new DateTime();
                                $debutFlashDate = new DateTime(DEBUT_FLASH_DATE);
                                $finFlashDate = new DateTime(FIN_FLASH_DATE);
                                $isFlashSalePeriod = ($currentDateTime >= $debutFlashDate && $currentDateTime <= $finFlashDate);

                                // Vérifier si le client a déjà acheté un véhicule
                                $hasAlreadyPurchased = clientHasVehicle();

                                // Afficher le bouton Acheter si:
                                // 1. Le véhicule est disponible OU c'est un achat en cours du client actuel
                                // 2. Nous sommes dans la période de vente flash
                                // 3. Le client n'a pas déjà acheté un véhicule
                                if ($canBuyVehicule && $isFlashSalePeriod && !$hasAlreadyPurchased):
                                ?>
                                    <div>
                                        <button
                                            style="background-color: #28a745; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; font-size: 14px;"
                                            onclick="openPurchaseModal(
                                                '<?php echo addslashes($vehicule['libelle']); ?>',
                                                '<?php echo addslashes($vehicule['descriptif']); ?>',
                                                <?php echo $vehicule['prix']; ?>,
                                                <?php echo !empty($vehicule['certificat']) && $vehicule['certificat'] == TRUE ? 'true' : 'false'; ?>,
                                                'certificat-<?php echo $vehicule['id']; ?>',
                                                '<?php echo number_format($vehicule['prix'], 2, ',', ' '); ?> €',
                                                '<?php echo addslashes($vehicule['reference']); ?>'
                                            )"
                                        >
                                            Acheter
                                        </button>
                                    </div>
                                <?php elseif ($canBuyVehicule && $isFlashSalePeriod && $hasAlreadyPurchased): ?>
                                    <div>
                                        <button
                                            style="background-color: #dc3545; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: not-allowed; font-size: 14px;"
                                            disabled
                                            title="Vous avez déjà acheté un véhicule"
                                        >
                                            Limite atteinte
                                        </button>
                                        <div style="font-size: 12px; color: #dc3545; margin-top: 5px;">
                                            Un seul véhicule par client
                                        </div>
                                    </div>
                                <?php elseif ($canBuyVehicule && !$isFlashSalePeriod): ?>
                                    <div>
                                        <button
                                            style="background-color: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: not-allowed; font-size: 14px;"
                                            disabled
                                            title="La vente flash n'est pas encore commencée ou est terminée"
                                        >
                                            Vente flash
                                        </button>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>

                <!-- Pagination -->
                <?php if ($data['total_pages'] > 1): ?>
                <div class="pagination-container" style="margin: 20px 0; text-align: center;">
                    <div class="pagination-info" style="margin-bottom: 10px;">
                        Affichage de <?php echo ($data['current_page'] - 1) * $data['per_page'] + 1; ?> à
                        <?php echo min($data['current_page'] * $data['per_page'], $data['total_vehicules']); ?>
                        sur <?php echo $data['total_vehicules']; ?> véhicules
                    </div>
                    <div class="pagination-controls">
                        <?php
                        // Construire l'URL de base pour la pagination
                        $baseUrl = '?';
                        if (!empty($data['current_search'])) {
                            $baseUrl .= 'search=' . urlencode($data['current_search']) . '&';
                        }
                        if (!empty($data['current_year']) && $data['current_year'] !== 'all') {
                            $baseUrl .= 'annee=' . urlencode($data['current_year']) . '&';
                        }
                        // Ajouter le paramètre showVendus si nécessaire
                        if (!$data['hide_vendus']) {
                            $baseUrl .= 'showVendus=1&';
                        }
                        ?>

                        <!-- Bouton Précédent -->
                        <?php if ($data['current_page'] > 1): ?>
                            <a href="<?php echo $baseUrl . 'page=' . ($data['current_page'] - 1); ?>" class="pagination-button" style="display: inline-block; padding: 8px 15px; margin: 0 5px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;">
                                &laquo; Précédent
                            </a>
                        <?php else: ?>
                            <span class="pagination-button disabled" style="display: inline-block; padding: 8px 15px; margin: 0 5px; background-color: #6c757d; color: white; text-decoration: none; border-radius: 5px; cursor: not-allowed;">
                                &laquo; Précédent
                            </span>
                        <?php endif; ?>

                        <!-- Numéros de page -->
                        <?php
                        $startPage = max(1, $data['current_page'] - 2);
                        $endPage = min($data['total_pages'], $data['current_page'] + 2);

                        // Afficher la première page si nécessaire
                        if ($startPage > 1): ?>
                            <a href="<?php echo $baseUrl . 'page=1'; ?>" class="pagination-button" style="display: inline-block; padding: 8px 15px; margin: 0 5px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;">
                                1
                            </a>
                            <?php if ($startPage > 2): ?>
                                <span class="pagination-ellipsis" style="margin: 0 5px;">...</span>
                            <?php endif; ?>
                        <?php endif; ?>

                        <!-- Pages autour de la page courante -->
                        <?php for ($i = $startPage; $i <= $endPage; $i++): ?>
                            <?php if ($i == $data['current_page']): ?>
                                <span class="pagination-button active" style="display: inline-block; padding: 8px 15px; margin: 0 5px; background-color: #28a745; color: white; text-decoration: none; border-radius: 5px;">
                                    <?php echo $i; ?>
                                </span>
                            <?php else: ?>
                                <a href="<?php echo $baseUrl . 'page=' . $i; ?>" class="pagination-button" style="display: inline-block; padding: 8px 15px; margin: 0 5px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;">
                                    <?php echo $i; ?>
                                </a>
                            <?php endif; ?>
                        <?php endfor; ?>

                        <!-- Afficher la dernière page si nécessaire -->
                        <?php if ($endPage < $data['total_pages']): ?>
                            <?php if ($endPage < $data['total_pages'] - 1): ?>
                                <span class="pagination-ellipsis" style="margin: 0 5px;">...</span>
                            <?php endif; ?>
                            <a href="<?php echo $baseUrl . 'page=' . $data['total_pages']; ?>" class="pagination-button" style="display: inline-block; padding: 8px 15px; margin: 0 5px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;">
                                <?php echo $data['total_pages']; ?>
                            </a>
                        <?php endif; ?>

                        <!-- Bouton Suivant -->
                        <?php if ($data['current_page'] < $data['total_pages']): ?>
                            <a href="<?php echo $baseUrl . 'page=' . ($data['current_page'] + 1); ?>" class="pagination-button" style="display: inline-block; padding: 8px 15px; margin: 0 5px; background-color: #007bff; color: white; text-decoration: none; border-radius: 5px;">
                                Suivant &raquo;
                            </a>
                        <?php else: ?>
                            <span class="pagination-button disabled" style="display: inline-block; padding: 8px 15px; margin: 0 5px; background-color: #6c757d; color: white; text-decoration: none; border-radius: 5px; cursor: not-allowed;">
                                Suivant &raquo;
                            </span>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div> <!-- Fin #middle -->
    </div> <!-- Fin #site -->

    <!-- Modal pour la galerie d'images -->
    <div id="imageGalleryModal" class="modal">
        <span class="close">&times;</span>
        <div class="modal-content">
            <div class="image-container">
                <img id="modalImage" src="" alt="Image en grand format" onerror="this.onerror=null; this.src='<?php echo BASE_URL; ?>public/img/image_not_found.png'; this.alt='Image non disponible';">
            </div>
            <div class="navigation">
                <button id="prevImage" class="nav-button">&lt;</button>
                <div id="imageCounter">1 / 1</div>
                <button id="nextImage" class="nav-button">&gt;</button>
            </div>
        </div>
    </div>

    <!-- Modale d'achat -->
    <div id="purchaseModal" class="purchase-modal">
        <div class="purchase-modal-content">
            <span class="close-modal" onclick="closePurchaseModal()">&times;</span>
            <h2>Confirmation d'achat</h2>
            <div class="modal-vehicle-info">
                <div class="modal-vehicle-image">
                    <img id="modalVehicleImage" src="" alt="Image du véhicule">
                </div>
                <div class="modal-vehicle-details">
                    <h3 id="modalVehicleTitle"></h3>
                    <p id="modalVehicleDescription"></p>
                    <div class="modal-price">
                        Prix: <span id="modalPrice"></span>
                    </div>
                    <div class="modal-checkboxes">
                        <label>
                            <input type="checkbox" id="modalCertificateCheckbox" onchange="updateModalPrice()">
                            Certificat (+200€)
                        </label>
                        <label>
                            <input type="checkbox" id="modalTransportCheckbox">
                            J'ai besoin d'aide pour le transport
                        </label>
                    </div>
                    <div class="vehicle-disclaimer" style="margin-top: 10px; color: #dc3545; font-weight: bold; text-align: center; font-size: 14px; padding: 5px; border-top: 1px solid #dee2e6;">
                        véhicule épave, vendu en l'état
                    </div>
                </div>
            </div>

            <!-- Section des coordonnées client (visible uniquement si connecté) -->
            <div id="clientInfoSection" class="client-info-section" style="display: none;">
                <h3>Vos coordonnées</h3>
                <div class="client-info-grid">
                    <div class="client-info-item" style="display: flex;">
                        <div class="client-info-label">Nom :</div>
                        <div id="clientNom" class="client-info-value"></div>
                    </div>
                    <div class="client-info-item" style="display: flex;">
                        <div class="client-info-label">Prénom :</div>
                        <div id="clientPrenom" class="client-info-value"></div>
                    </div>
                    <div class="client-info-item" style="display: flex;">
                        <div class="client-info-label">Email :</div>
                        <div id="clientEmail" class="client-info-value"></div>
                    </div>
                    <div class="client-info-item" style="display: flex;">
                        <div class="client-info-label">Téléphone :</div>
                        <div id="clientTelephone" class="client-info-value"></div>
                    </div>
                </div>

                <h3>Adresse de facturation</h3>
                <div id="clientAdresseContainer">
                    <div class="client-info-grid">
                        <div class="client-info-item" style="display: flex;">
                            <div class="client-info-label">Raison sociale :</div>
                            <div id="clientRaisonSociale" class="client-info-value"></div>
                        </div>
                        <div class="client-info-item" style="display: flex;">
                            <div class="client-info-label">Adresse :</div>
                            <div id="clientAdresse" class="client-info-value"></div>
                        </div>
                        <div class="client-info-item" style="display: flex;">
                            <div class="client-info-label">Code postal :</div>
                            <div id="clientCodePostal" class="client-info-value"></div>
                        </div>
                        <div class="client-info-item" style="display: flex;">
                            <div class="client-info-label">Ville :</div>
                            <div id="clientVille" class="client-info-value"></div>
                        </div>
                        <div class="client-info-item" style="display: flex;">
                            <div class="client-info-label">Pays :</div>
                            <div id="clientPays" class="client-info-value"></div>
                        </div>
                    </div>
                </div>
                <div id="noClientAdresse" style="display: none;">
                    <p style="color: #dc3545; font-size: 12px; margin-top: 5px;">Aucune adresse de facturation n'est enregistrée. <a href="<?= BASE_URL ?>utilisateur/profil" style="color: #007bff; text-decoration: underline;">Compléter mon profil</a></p>
                </div>
            </div>

            <button id="validatePurchaseBtn" class="validate-purchase" onclick="validatePurchase()">Procéder au règlement</button>
            <div id="loginMessage" class="login-message" style="display: none;">
                Vous devez être connecté pour procéder au règlement. <a href="<?= BASE_URL ?>utilisateur/s_inscrire">S'inscrire</a> ou <a href="#" onclick="scrollToLogin(); return false;">Se connecter</a>
            </div>
        </div>
    </div>

<?php
// Ajouter les scripts spécifiques à cette page
$data['additional_footer_scripts'] = '
<script src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>
<script async src="https://www.googletagmanager.com/gtag/js?id=G-SF3KDYRET4"></script>
<script>
    window.datalayer = window.datalayer || []; function gtag(){datalayer.push(arguments);} gtag("js", new Date()); gtag("config", "G-SF3KDYRET4");
</script>
<script>
// Initialiser les cases à cocher de certificat
document.addEventListener("DOMContentLoaded", function() {
    // Sélectionner toutes les cases à cocher de certificat
    const certificateCheckboxes = document.querySelectorAll(".certificate-checkbox");

    // Ajouter un écouteur d\'événement à chaque case à cocher
    certificateCheckboxes.forEach(function(checkbox) {
        checkbox.addEventListener("change", function() {
            // Récupérer le prix de base et l\'ID du véhicule
            const basePrice = parseFloat(this.getAttribute("data-price"));
            const vehicleId = this.getAttribute("data-id");

            // Trouver l\'élément d\'affichage du prix pour ce véhicule
            const priceElement = this.closest(".inner-wrap2").querySelector(".vehicle-price");

            // Calculer le nouveau prix en fonction de l\'état de la case à cocher
            let newPrice = basePrice;
            if (this.checked) {
                newPrice += 200; // Ajouter 200€ si la case est cochée
            }

            // Formater le nouveau prix et mettre à jour l\'affichage
            const formattedPrice = new Intl.NumberFormat("fr-FR", { minimumFractionDigits: 2, maximumFractionDigits: 2 }).format(newPrice);
            priceElement.textContent = formattedPrice + " €";
        });
    });

    // L\'initialisation de la galerie a été déplacée dans le fichier public/js/gallery.js
});

// Définir la fonction openPurchaseModal dans le scope global
window.openPurchaseModal = function(title, description, basePrice, hasCertificate, certificateId, currentPrice, reference) {
    console.log("openPurchaseModal appelé avec:", { title, description, basePrice, hasCertificate, certificateId, currentPrice, reference });

    // Vérifier l\'existence de tous les éléments DOM nécessaires
    const modalVehicleTitle = document.getElementById("modalVehicleTitle");
    const modalVehicleDescription = document.getElementById("modalVehicleDescription");
    const modalPrice = document.getElementById("modalPrice");
    const certCheckbox = document.getElementById("modalCertificateCheckbox");
    const modalVehicleImage = document.getElementById("modalVehicleImage");
    const purchaseModal = document.getElementById("purchaseModal");
    const validatePurchaseBtn = document.getElementById("validatePurchaseBtn");
    const loginMessage = document.getElementById("loginMessage");

    // Stocker la référence du véhicule dans un attribut data
    if (modalVehicleTitle) {
        modalVehicleTitle.dataset.reference = reference;
    }

    // Vérifier que tous les éléments existent
    const missingElements = [];
    if (!modalVehicleTitle) missingElements.push("modalVehicleTitle");
    if (!modalVehicleDescription) missingElements.push("modalVehicleDescription");
    if (!modalPrice) missingElements.push("modalPrice");
    if (!certCheckbox) missingElements.push("modalCertificateCheckbox");
    if (!modalVehicleImage) missingElements.push("modalVehicleImage");
    if (!purchaseModal) missingElements.push("purchaseModal");
    if (!validatePurchaseBtn) missingElements.push("validatePurchaseBtn");
    if (!loginMessage) missingElements.push("loginMessage");

    if (missingElements.length > 0) {
        console.error("Éléments DOM manquants:", missingElements);
        return;
    }

    // Mettre à jour le contenu de la modale
    modalVehicleTitle.textContent = title;
    modalVehicleDescription.textContent = description;

    // Stocker le prix de base et mettre à jour le prix affiché
    modalPrice.dataset.basePrice = basePrice;

    // Gérer l\'affichage de l\'option certificat
    if (hasCertificate) {
        const listCertCheckbox = document.getElementById(certificateId);
        if (listCertCheckbox) {
            // Récupérer l\'état de la case à cocher depuis la liste
            certCheckbox.checked = listCertCheckbox.checked;

            // Calculer le prix en fonction de l\'état de la case à cocher
            let finalPrice = basePrice;
            if (listCertCheckbox.checked) {
                finalPrice += 200; // Ajouter 200€ si la case est cochée
            }

            // Formater et afficher le prix correct
            const formattedPrice = new Intl.NumberFormat("fr-FR", {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(finalPrice) + " €";

            modalPrice.textContent = formattedPrice;
        } else {
            certCheckbox.checked = false;
            modalPrice.textContent = currentPrice;
        }
        certCheckbox.parentElement.style.display = "block";
    } else {
        certCheckbox.checked = false;
        modalPrice.textContent = currentPrice;
        certCheckbox.parentElement.style.display = "none";
    }

    // Réinitialiser la case transport
    const transportCheckbox = document.getElementById("modalTransportCheckbox");
    if (transportCheckbox) {
        transportCheckbox.checked = false;
    }

    // Rechercher et configurer l\'image
    const listImage = document.querySelector(`img[alt="${title}"]`);
    if (listImage) {
        modalVehicleImage.src = listImage.src;
    } else {
        modalVehicleImage.src = "<?php echo BASE_URL; ?>public/img/image_not_found.png";
    }
    modalVehicleImage.onerror = function() {
        this.src = "<?php echo BASE_URL; ?>public/img/image_not_found.png";
        this.alt = "Image non disponible";
    };

    // Vérifier si l\'utilisateur est connecté
    const isLoggedIn = ' . ((isset($_COOKIE['USERLOG']) || isset($_SESSION['USER'])) ? 'true' : 'false') . ';

    // Mettre à jour l\'état du bouton et afficher le message si nécessaire
    if (isLoggedIn) {
        validatePurchaseBtn.className = "validate-purchase";
        validatePurchaseBtn.disabled = false;
        loginMessage.style.display = "none";

        // Récupérer les informations du client
        console.log("Tentative de récupération des informations client...");

        // Afficher un message de chargement
        const clientInfoSection = document.getElementById("clientInfoSection");
        if (clientInfoSection) {
            clientInfoSection.style.display = "block";
            clientInfoSection.innerHTML = \'<div style="text-align: center; padding: 20px;"><p>Chargement de vos informations...</p></div>\';
        }

        // Utiliser l\'URL complète avec BASE_URL
        const apiUrl = "' . BASE_URL . 'app/ajax/get_client_info.php";
        console.log("URL de l\'API:", apiUrl);

        fetch(apiUrl)
            .then(response => {
                console.log("Réponse reçue:", response.status);
                if (!response.ok) {
                    throw new Error("Erreur réseau: " + response.status);
                }
                return response.text().then(text => {
                    try {
                        // Essayer de parser le texte en JSON
                        return JSON.parse(text);
                    } catch (e) {
                        // Si le parsing échoue, afficher l\'erreur et le texte brut
                        console.error("Erreur de parsing JSON:", e);
                        console.log("Réponse brute:", text);
                        throw new Error("Erreur lors du parsing de la réponse JSON");
                    }
                });
            })
            .then(data => {
                console.log("Données reçues:", data);

                // Réinitialiser le contenu de la section client
                if (clientInfoSection) {
                    // Restaurer le HTML original
                    clientInfoSection.innerHTML = `
                        <h3>Vos coordonnées</h3>
                        <div class="client-info-grid">
                            <div class="client-info-item" style="display: flex;">
                                <div class="client-info-label">Nom :</div>
                                <div id="clientNom" class="client-info-value"></div>
                            </div>
                            <div class="client-info-item" style="display: flex;">
                                <div class="client-info-label">Prénom :</div>
                                <div id="clientPrenom" class="client-info-value"></div>
                            </div>
                            <div class="client-info-item" style="display: flex;">
                                <div class="client-info-label">Email :</div>
                                <div id="clientEmail" class="client-info-value"></div>
                            </div>
                            <div class="client-info-item" style="display: flex;">
                                <div class="client-info-label">Téléphone :</div>
                                <div id="clientTelephone" class="client-info-value"></div>
                            </div>
                        </div>

                        <h3>Adresse de facturation</h3>
                        <div id="clientAdresseContainer">
                            <div class="client-info-grid">
                                <div class="client-info-item" style="display: flex;">
                                    <div class="client-info-label">Raison sociale :</div>
                                    <div id="clientRaisonSociale" class="client-info-value"></div>
                                </div>
                                <div class="client-info-item" style="display: flex;">
                                    <div class="client-info-label">Adresse :</div>
                                    <div id="clientAdresse" class="client-info-value"></div>
                                </div>
                                <div class="client-info-item" style="display: flex;">
                                    <div class="client-info-label">Code postal :</div>
                                    <div id="clientCodePostal" class="client-info-value"></div>
                                </div>
                                <div class="client-info-item" style="display: flex;">
                                    <div class="client-info-label">Ville :</div>
                                    <div id="clientVille" class="client-info-value"></div>
                                </div>
                                <div class="client-info-item" style="display: flex;">
                                    <div class="client-info-label">Pays :</div>
                                    <div id="clientPays" class="client-info-value"></div>
                                </div>
                            </div>
                        </div>
                        <div id="noClientAdresse" style="display: none;">
                            <p style="color: #dc3545; font-size: 12px; margin-top: 5px;">Aucune adresse de facturation n\'est enregistrée. <a href="' . BASE_URL . 'utilisateur/profil" style="color: #007bff; text-decoration: underline;">Compléter mon profil</a></p>
                        </div>
                    `;

                    if (data.success) {
                        // Remplir les informations du client
                        document.getElementById("clientNom").textContent = data.client.nom;
                        document.getElementById("clientPrenom").textContent = data.client.prenom;
                        document.getElementById("clientEmail").textContent = data.client.email;
                        document.getElementById("clientTelephone").textContent = data.client.telephone;

                        // Gérer l\'affichage de l\'adresse
                        const clientAdresseContainer = document.getElementById("clientAdresseContainer");
                        const noClientAdresse = document.getElementById("noClientAdresse");

                        if (data.hasAdresse) {
                            clientAdresseContainer.style.display = "block";
                            noClientAdresse.style.display = "none";

                            // Remplir les informations d\'adresse
                            document.getElementById("clientRaisonSociale").textContent = data.adresse.raisonSociale;
                            document.getElementById("clientAdresse").textContent = data.adresse.adresse;
                            document.getElementById("clientCodePostal").textContent = data.adresse.codePostal;
                            document.getElementById("clientVille").textContent = data.adresse.ville;
                            document.getElementById("clientPays").textContent = data.adresse.pays;
                        } else {
                            clientAdresseContainer.style.display = "none";
                            noClientAdresse.style.display = "block";
                        }
                    } else {
                        // Afficher un message d\'erreur
                        clientInfoSection.innerHTML = `
                            <div style="text-align: center; padding: 20px; color: #dc3545;">
                                <p>Erreur lors de la récupération de vos informations: ${data.message || "Erreur inconnue"}</p>
                            </div>
                        `;
                        console.error("Erreur lors de la récupération des informations client:", data.message);
                    }
                }
            })
            .catch(error => {
                console.error("Erreur lors de la récupération des informations client:", error);

                // Afficher un message d\'erreur
                if (clientInfoSection) {
                    clientInfoSection.innerHTML = `
                        <div style="text-align: center; padding: 20px; color: #dc3545;">
                            <p>Erreur lors de la récupération de vos informations: ${error.message}</p>
                            <p>Veuillez réessayer ultérieurement ou contacter le support.</p>
                        </div>
                    `;
                }
            });
    } else {
        validatePurchaseBtn.className = "validate-purchase-disabled";
        validatePurchaseBtn.disabled = true;
        loginMessage.style.display = "block";

        // Cacher la section des coordonnées client
        const clientInfoSection = document.getElementById("clientInfoSection");
        if (clientInfoSection) {
            clientInfoSection.style.display = "none";
        }
    }

    // Afficher la modale
    purchaseModal.style.display = "block";
    console.log("Modale d\'achat affichée avec succès");
}

window.closePurchaseModal = function() {
    document.getElementById("purchaseModal").style.display = "none";
};

window.updateModalPrice = function() {
    const priceElement = document.getElementById("modalPrice");
    const basePrice = parseFloat(priceElement.dataset.basePrice);
    let finalPrice = basePrice;

    // Ajouter 200€ si le certificat est coché
    if (document.getElementById("modalCertificateCheckbox").checked) {
        finalPrice += 200;
    }

    // Formater et afficher le prix
    priceElement.textContent = new Intl.NumberFormat("fr-FR", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(finalPrice) + " €";

    // Synchroniser l\'état de la case à cocher avec celle de la liste si possible
    const modalVehicleTitle = document.getElementById("modalVehicleTitle");
    if (modalVehicleTitle) {
        const title = modalVehicleTitle.textContent;
        // Trouver la carte du véhicule correspondant au titre
        const vehicleCards = document.querySelectorAll(".vehicle-card");
        for (let i = 0; i < vehicleCards.length; i++) {
            const cardTitle = vehicleCards[i].querySelector(".title_autre");
            if (cardTitle && cardTitle.textContent.trim() === title.trim()) {
                // Trouver la case à cocher dans cette carte
                const listCheckbox = vehicleCards[i].querySelector(".certificate-checkbox");
                if (listCheckbox) {
                    // Mettre à jour l\'état de la case à cocher dans la liste
                    listCheckbox.checked = document.getElementById("modalCertificateCheckbox").checked;

                    // Déclencher l\'événement change pour mettre à jour le prix dans la liste
                    const event = new Event("change", { bubbles: true });
                    listCheckbox.dispatchEvent(event);
                }
                break;
            }
        }
    }
}

window.validatePurchase = function() {
    // Vérifier si l\'utilisateur est connecté
    const isLoggedIn = ' . ((isset($_COOKIE['USERLOG']) || isset($_SESSION['USER'])) ? 'true' : 'false') . ';

    // Si l\'utilisateur n\'est pas connecté, afficher un message et arrêter l\'exécution
    if (!isLoggedIn) {
        alert("Vous devez être connecté pour procéder au règlement.");
        return;
    }

    const title = document.getElementById("modalVehicleTitle").textContent;
    const priceElement = document.getElementById("modalPrice");
    const basePrice = parseFloat(priceElement.dataset.basePrice);
    const needsTransport = document.getElementById("modalTransportCheckbox").checked;
    const hasCertificate = document.getElementById("modalCertificateCheckbox").checked;

    // Calculer le prix final en fonction de l\'état de la case à cocher du certificat
    let finalPrice = basePrice;
    if (hasCertificate) {
        finalPrice += 200;
    }

    // Vérifier que le prix calculé correspond au prix affiché dans la modale
    const displayedPrice = priceElement.textContent.replace(/[^\d,]/g, "").replace(",", ".");
    const displayedPriceValue = parseFloat(displayedPrice);

    // Si le prix affiché est différent du prix calculé, utiliser le prix affiché
    if (!isNaN(displayedPriceValue) && Math.abs(displayedPriceValue - finalPrice) > 0.01) {
        console.log("Prix recalculé (" + finalPrice + ") différent du prix affiché (" + displayedPriceValue + "). Utilisation du prix affiché.");
        finalPrice = displayedPriceValue;
    }

    // Récupérer les informations client affichées
    const clientNom = document.getElementById("clientNom").textContent;
    const clientPrenom = document.getElementById("clientPrenom").textContent;
    const clientEmail = document.getElementById("clientEmail").textContent;

    // Vérifier si le client a déjà acheté un véhicule
    fetch("app/ajax/check_client_vehicle.php?email=" + encodeURIComponent(clientEmail))
        .then(response => response.json())
        .then(data => {
            if (data.hasVehicle) {
                alert("Vous avez déjà acheté un véhicule. Un seul véhicule peut être acheté par client.");
                // Fermer la fenêtre modale
                document.getElementById("purchaseModal").style.display = "none";
                return;
            }

            // Vérifier si le client a un véhicule en cours d\'achat différent de celui-ci
            if (data.pendingVehicle && modalVehicleTitle.textContent !== data.pendingVehicle.libelle) {
                alert("Vous avez déjà un achat en cours pour le véhicule " + data.pendingVehicle.libelle + ". Veuillez terminer cet achat ou attendre qu\'il expire avant d\'en commencer un nouveau.");
                // Fermer la fenêtre modale
                document.getElementById("purchaseModal").style.display = "none";
                return;
            }

            // Utiliser directement la référence du véhicule (qui est unique) au lieu de l\'ID
            const vehiculeReference = document.getElementById("modalVehicleTitle").dataset.reference;
            if (!vehiculeReference) {
                alert("Erreur: Référence du véhicule non trouvée. Veuillez réessayer.");
                return;
            }

            // Démarrer le processus d\'achat en mettant à jour le statut et la date/heure d\'achat
            fetch("app/ajax/start_purchase_process.php", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    vehiculeReference: vehiculeReference,
                    email: clientEmail
                })
            })
            .then(response => response.json())
            .then(startData => {
                if (!startData.success) {
                    alert("Erreur: " + startData.message);
                    return;
                }

                // Continuer avec le processus d\'achat
                proceedWithPurchase();
            })
            .catch(error => {
                console.error("Erreur lors du démarrage du processus d\'achat:", error);
                alert("Une erreur est survenue lors du démarrage du processus d\'achat. Veuillez réessayer.");
            });
        })
        .catch(error => {
            console.error("Erreur lors de la vérification du véhicule:", error);
            alert("Une erreur est survenue lors de la vérification de votre compte. Veuillez réessayer.");
        });

    function proceedWithPurchase() {
        let message = `Confirmation de votre achat :\n\n`;
        message += `${title}\n`;
        message += `Prix total : ${finalPrice.toFixed(2)}€\n\n`;
        message += `Options sélectionnées :\n`;
        if (hasCertificate) message += `- Certificat inclus\n`;
        if (needsTransport) message += `- Assistance transport demandée\n\n`;

        // Ajouter les informations du client
        message += `Informations client :\n`;
        message += `- ${clientPrenom} ${clientNom}\n`;
        message += `- ${clientEmail}\n`;

        if (confirm(message + "\nSouhaitez-vous procéder au paiement ?")) {
        // Créer un formulaire dynamique pour la redirection vers Scellius
        const form = document.createElement("form");
        form.method = "POST";
        form.action = "' . SCELLIUS_URL . '";

        // Générer un identifiant de transaction unique
        const transId = new Date().getTime().toString().substr(-6);

        // Formater la date selon le format AAAAMMJJHHMMSS
        const now = new Date();
        const vadsDate = now.getFullYear().toString() +
            (now.getMonth() + 1).toString().padStart(2, "0") +
            now.getDate().toString().padStart(2, "0") +
            now.getHours().toString().padStart(2, "0") +
            now.getMinutes().toString().padStart(2, "0") +
            now.getSeconds().toString().padStart(2, "0");

        // Préparer les champs Scellius
        const fields = {
            "vads_action_mode": "INTERACTIVE",
            "vads_amount": Math.round(finalPrice * 100), // Conversion en centimes
            "vads_ctx_mode": "' . SCELLIUS_CTX_MODE . '",
            "vads_currency": "978", // Code pour EUR
            "vads_page_action": "PAYMENT",
            "vads_payment_config": "SINGLE",
            "vads_site_id": "' . SCELLIUS_SITE_ID . '",
            "vads_trans_date": vadsDate,
            "vads_trans_id": transId,
            "vads_version": "' . SCELLIUS_API_VERSION . '",
            "vads_url_return": "' . SCELLIUS_URL_RETURN . '",
            "vads_url_notify": "' . SCELLIUS_URL_NOTIFY . '",
            "vads_order_info": "*VF",
            "vads_order_info2": document.getElementById("modalVehicleTitle").dataset.reference + (needsTransport ? "|Transport:Oui" : "|Transport:Non"), // Référence + info transport
            "vads_order_info3": "Certificat: " + (hasCertificate ? "Oui" : "Non"),
            // Informations client
            "vads_cust_email": clientEmail,
            "vads_cust_first_name": clientPrenom,
            "vads_cust_last_name": clientNom
        };

        // Calculer la signature côté serveur via une requête AJAX
        fetch("app/scellius/calculate_signature.php", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(fields)
        })
        .then(response => response.json())
        .then(data => {
            if (data.signature) {
                // Ajouter tous les champs au formulaire
                Object.entries(fields).forEach(([key, value]) => {
                    const input = document.createElement("input");
                    input.type = "hidden";
                    input.name = key;
                    input.value = value;
                    form.appendChild(input);
                });

                // Ajouter la signature
                const signatureInput = document.createElement("input");
                signatureInput.type = "hidden";
                signatureInput.name = "signature";
                signatureInput.value = data.signature;
                form.appendChild(signatureInput);

                // Soumettre le formulaire
                document.body.appendChild(form);
                form.submit();
            } else {
                alert("Une erreur est survenue lors de la préparation du paiement. Veuillez réessayer.");
            }
        })
        .catch(error => {
            console.error("Erreur:", error);
            alert("Une erreur est survenue lors de la préparation du paiement. Veuillez réessayer.");
        });
    }
    }
}

// Fonction pour faire défiler jusqu\'au formulaire de connexion
window.scrollToLogin = function() {
    // Fermer la modale d\'achat
    closePurchaseModal();

    // Trouver l\'élément du formulaire de connexion
    const loginForm = document.querySelector("form[action*=\"utilisateur/connexion\"]");

    if (loginForm) {
        // Faire défiler jusqu\'au formulaire de connexion avec une animation fluide
        loginForm.scrollIntoView({ behavior: "smooth", block: "center" });

        // Mettre en évidence le formulaire de connexion
        loginForm.style.transition = "background-color 0.5s";
        loginForm.style.backgroundColor = "rgba(255, 255, 0, 0.2)";

        // Remettre la couleur d\'arrière-plan normale après un délai
        setTimeout(() => {
            loginForm.style.backgroundColor = "transparent";
        }, 2000);

        // Mettre le focus sur le champ email
        const emailField = loginForm.querySelector("input[name=\"email\"]");
        if (emailField) {
            setTimeout(() => {
                emailField.focus();
            }, 800);
        }
    }
}

// Fermer la modale si l\'utilisateur clique en dehors
window.onclick = function(event) {
    if (event.target.className === "purchase-modal") {
        closePurchaseModal();
    }
};
</script>';

// Inclure le footer commun
require_once __DIR__ . '/../templates/footer.php';
?>



