<?php
/**
 * Script de traitement des notifications automatiques Scellius
 * Ce fichier est exécuté lorsque le serveur Scellius envoie une notification de paiement
 */

/**
 * Calcule la signature pour vérifier l'authenticité des données reçues
 * 
 * @param array $params Tableau contenant les champs reçus dans l'IPN
 * @param string $key Clé de TEST ou de PRODUCTION
 * @return string Signature calculée
 */
function getSignature($params, $key)
{
    // Initialisation de la variable qui contiendra la chaine à chiffrer
    $contenu_signature = "";
    
    // Tri des champs par ordre alphabétique
    ksort($params);
    
    // Construction de la chaîne à signer
    foreach($params as $nom => $valeur) {
        // Récupération des champs vads_
        if (substr($nom, 0, 5) == 'vads_') {
            // Concaténation avec le séparateur "+"
            $contenu_signature .= $valeur . "+";
        }
    }
    
    // Ajout de la clé en fin de chaine
    $contenu_signature .= $key;
    
    // Encodage base64 de la chaine chiffrée avec l'algorithme HMAC-SHA-256
    $sign = base64_encode(hash_hmac('sha256', $contenu_signature, $key, true));
    
    return $sign;
}

/**
 * Redirige les données vers le script notification.php
 * 
 * @param array $data Données à rediriger
 * @param resource $fp Ressource du fichier de log
 */
function redirectToNotification($data, $fp) {
    // Vérifier que le fichier de log est valide
    if (!$fp || !is_resource($fp)) {
        error_log("ERREUR: Impossible d'écrire dans le fichier de log");
        return;
    }
    
    fwrite($fp, "---DEBUT REDIRECTION vers notification.php---\n");
    fflush($fp);
    
    // Préparation de la requête
    $url = 'http://vf.jeep-dodge-gmc.com/app/scellius/notification.php';
    fwrite($fp, "URL: " . $url . "\n");
    fflush($fp);
    
    try {
        // Préparation des options pour file_get_contents
        fwrite($fp, "Préparation des options pour file_get_contents...\n");
        fflush($fp);
        
        $postData = http_build_query($data);
        fwrite($fp, "Données POST préparées (" . strlen($postData) . " octets)\n");
        fflush($fp);
        
        $options = [
            'http' => [
                'method' => 'POST',
                'header' => 'Content-type: application/x-www-form-urlencoded',
                'content' => $postData,
                'timeout' => 30
            ]
        ];
        
        $context = stream_context_create($options);
        fwrite($fp, "Contexte de flux créé\n");
        fflush($fp);
        
        // Exécution de la requête
        fwrite($fp, "Exécution de file_get_contents...\n");
        fflush($fp);
        
        $result = @file_get_contents($url, false, $context);
        
        // Vérification du résultat
        if ($result === FALSE) {
            fwrite($fp, "ERREUR: file_get_contents a échoué\n");
            
            // Récupérer les détails de l'erreur si possible
            if (isset($http_response_header)) {
                fwrite($fp, "Réponse HTTP: " . print_r($http_response_header, true) . "\n");
            }
        } else {
            fwrite($fp, "Redirection réussie\n");
            fwrite($fp, "Réponse (longueur: " . strlen($result) . " octets): " . substr($result, 0, 200) . (strlen($result) > 200 ? "..." : "") . "\n");
            
            // Afficher les en-têtes HTTP de réponse
            if (isset($http_response_header)) {
                fwrite($fp, "En-têtes HTTP: " . print_r($http_response_header, true) . "\n");
            }
        }
    } catch (Exception $e) {
        fwrite($fp, "EXCEPTION: " . $e->getMessage() . "\n");
    }
    
    fwrite($fp, "---FIN REDIRECTION---\n");
    fflush($fp);
}

// Configuration du fichier de log
$logfile = "/home/<USER>/www/smi/scellius/log.txt";

// Ouverture du fichier de log en mode append
$fp = fopen($logfile, "a");

// Début d'une nouvelle entrée de log
fwrite($fp, "-------------------------------------------\n");
fwrite($fp, "Date: " . date('Y-m-d H:i:s') . "\n");

// Initialisation de l'état d'erreur
$Erreur = "O"; // O = Oui (erreur par défaut)

// Vérification de la présence de données POST
if (empty($_POST)) {
    fwrite($fp, "ERREUR: Le POST est vide !!!\n");
} else {
    fwrite($fp, "Données reçues :\n");
    
    // Clés de signature pour les environnements TEST et PRODUCTION
    $keyTest = 'CLE-TEST-SCELLIUS';
    $keyProduction = '5KLmzxQLYOgKCzGu'; // À remplacer par votre clé de production
    
    // Déterminer quelle clé utiliser en fonction du mode
    $key = (isset($_POST['vads_ctx_mode']) && $_POST['vads_ctx_mode'] == 'PRODUCTION') ? $keyProduction : $keyTest;
    
    // Vérification de la présence du hash Scellius
    if (isset($_POST['vads_hash'])) {
        fwrite($fp, "Form API notification détectée\n");
        
        // Créer une copie des données POST sans la signature
        $dataToSign = $_POST;
        if (isset($dataToSign['signature'])) {
            unset($dataToSign['signature']);
        }
        
        // Calcul de la signature
        $Signature = getSignature($dataToSign, $key);
        
        // Vérification de la signature
        if (isset($_POST['signature']) && $_POST['signature'] == $Signature) {
            $Erreur = "N"; // N = Non (pas d'erreur)
            fwrite($fp, "Signature valide\n");
            
            // Vérification si le champ vads_order_info contient *VF
            if (isset($_POST['vads_order_info']) && strpos($_POST['vads_order_info'], '*VF') !== false) {
                fwrite($fp, "Détection de *VF dans vads_order_info\n");
                redirectToNotification($_POST, $fp);
                fclose($fp);
                exit; // Arrêt du script après redirection
            }
        } else {
            fwrite($fp, "ERREUR: Signature invalide !!!\n");
            fwrite($fp, "Signature reçue = " . (isset($_POST['signature']) ? $_POST['signature'] : "non définie") . "\n");
            fwrite($fp, "Signature Calculée = " . $Signature . "\n");
            
            // Redirection vers notification.php en cas de signature invalide
            redirectToNotification($_POST, $fp);
            fclose($fp);
            exit; // Arrêt du script après redirection
        }
        
        // Journalisation des données reçues
        foreach ($_POST as $k => $v) {
            fwrite($fp, $k . " = " . $v . "\n");
        }
        fwrite($fp, "Signature Calculée = " . $Signature . "\n");
    } else {
        fwrite($fp, "ERREUR: Notification non reconnue (vads_hash manquant)\n");
        // Redirection vers notification.php même si vads_hash est manquant
        redirectToNotification($_POST, $fp);
        fclose($fp);
        exit; // Arrêt du script après redirection
    }
}

// Traitement de la commande si la signature est valide
if ($Erreur == "N") {    
    // Chargement des dépendances
    require_once dirname(__FILE__) . '/../../../init.php';
    require_once 'commande_static_class.php';
    
    // Traitement selon le résultat d'authentification
    switch ($_POST['vads_auth_result']) {
        case '00': // Code 00 = Paiement accepté
            // Récupération et mise à jour de la commande
            $cs = commande_staticDB::getCommandeById($_POST['vads_order_id']);
            $cs->setStatut("Paiement validé");
            fwrite($fp, "Commande " . $_POST['vads_order_id'] . " validée\n");
            
            // Sauvegarde de la commande (sans recréer l'ID)
            commande_staticDB::saveCommande($cs, false);
            
            // TODO: Envoi d'un email de confirmation au client
            // require_once dirname(__FILE__).'/../mail_commande.php';
            break;
            
        default: // Tous les autres cas = Échec du paiement
            // Suppression de la commande
            commande_staticDB::deleteCommande($_POST['vads_order_id']);
            fwrite($fp, "Commande " . $_POST['vads_order_id'] . " supprimée (code: " . $_POST['vads_auth_result'] . ")\n");
            break;
    }
}

// Fermeture du fichier de log
fclose($fp);

// Pas de réponse nécessaire pour le serveur Scellius
?>		
