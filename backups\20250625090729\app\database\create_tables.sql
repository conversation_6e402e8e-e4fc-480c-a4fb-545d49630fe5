CREATE DATABASE IF NOT EXISTS jeep_dodge_gmc;
USE jeep_dodge_gmc;

CREATE TABLE IF NOT EXISTS vehicules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    reference VARCHAR(50) NOT NULL UNIQUE,
    libelle VARCHAR(100) NOT NULL,
    descriptif VARCHAR(255),
    annee <PERSON>(50),
    prix DECIMAL(10,2),
    client VARCHAR(100),
    client_adresse TEXT,
    categorie VARCHAR(50) NOT NULL,
    statut VARCHAR(50),
    rgltreference VARCHAR(100),
    certificat BOOLEAN DEFAULT FALSE,
    libelle_certificat VARCHAR(255),
    date_ajout TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Données d'exemple
INSERT INTO vehicules (reference, libelle, descriptif, prix, client, categorie, statut, rgltreference, certificat) VALUES
('HALF_TRACK', 'Half-Track', 'White, International, M2, M3, M4', 45000.00, '', 'AUTRE VEHICULE', 'Disponible', '', TRUE),
('FORD_M8_M20', 'M8 - M20', 'Light Armored Car', 38500.00, '', 'AUTRE VEHICULE', 'Vendu', '', FALSE),
('SCOUT_CAR', 'Scout Car', 'M3A1', 42750.00, '', 'AUTRE VEHICULE', 'Achat en cours', '', TRUE),
('PACIFIC', 'Pacific', 'Dragon Wagon M26', 65000.00, '', 'AUTRE VEHICULE', 'Vendu', '', FALSE),
('WARD_LA_FRANCE', 'Ward La France', 'Heavy Wreacker M1A1', 52500.00, '', 'AUTRE VEHICULE', 'Disponible', '', TRUE),
('MOTO', 'Moto', 'Harley Davidson, BMW,...', 12800.00, '', 'AUTRE VEHICULE', 'Achat en cours', 'REF123', FALSE),
('AUTOCAR', 'Autocar', 'GMC PD 4104', 35000.00, '', 'AUTRE VEHICULE', 'Disponible', '', FALSE),
('DUKW', 'DUKW', 'Amphibie', 48900.00, '', 'AUTRE VEHICULE', 'Vendu', '', TRUE),
('WEASEL', 'WEASEL', 'M29', 29500.00, '', 'AUTRE VEHICULE', 'Achat en cours', '', FALSE);