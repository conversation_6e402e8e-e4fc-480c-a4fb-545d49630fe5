<?php
// Démarrer la session si ce n'est pas déjà fait
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Inclure la configuration
require_once __DIR__ . '/../app/config/config.php';
require_once __DIR__ . '/../app/config/database.php';

// Vérifier si l'utilisateur est connecté
if (!isset($_SESSION['USER']) && !isset($_COOKIE['USERLOG'])) {
    echo "Aucun utilisateur connecté.\n";
    exit;
}

// Si l'utilisateur est connecté via cookie mais pas via session
if (!isset($_SESSION['USER']) && isset($_COOKIE['USERLOG'])) {
    $_SESSION['USER'] = json_decode($_COOKIE['USERLOG'], true);
}

// Récupérer l'email de l'utilisateur (clé principale)
$email = $_SESSION['USER']['email'];
echo "Email de l'utilisateur connecté: $email\n\n";

// Connexion à la base de données des clients
$db = getClientsDbConnection();

// Récupérer les informations du client
$stmt = $db->prepare("SELECT * FROM clients WHERE emailclient = ? LIMIT 1");
$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    echo "Client non trouvé dans la base de données.\n";
    exit;
}

$client = $result->fetch_assoc();
echo "Informations du client:\n";
echo "- Nom: " . $client['nomclient'] . "\n";
echo "- Prénom: " . $client['prenomclient'] . "\n";
echo "- Email: " . $client['emailclient'] . "\n";
echo "- Téléphone: " . ($client['telclient'] ?? 'Non renseigné') . "\n\n";

// Récupérer l'adresse de facturation
$stmt = $db->prepare("SELECT * FROM adresses_facturation WHERE af_emailclient = ? LIMIT 1");
$stmt->bind_param("s", $email);
$stmt->execute();
$adresseResult = $stmt->get_result();

if ($adresseResult->num_rows > 0) {
    $adresse = $adresseResult->fetch_assoc();
    echo "Adresse de facturation:\n";
    echo "- Raison sociale: " . ($adresse['af_raisonsocial'] ?? 'Non renseigné') . "\n";
    echo "- Adresse: " . ($adresse['af_nomrue'] ?? 'Non renseigné') . "\n";
    echo "- Code postal: " . ($adresse['af_codepostal'] ?? 'Non renseigné') . "\n";
    echo "- Ville: " . ($adresse['af_ville'] ?? 'Non renseigné') . "\n";
    echo "- Pays: " . ($adresse['af_pays'] ?? 'Non renseigné') . "\n";
} else {
    echo "Aucune adresse de facturation trouvée pour cet utilisateur.\n";
}

// Fermer la connexion à la base de données
$db->close();

// Simuler la réponse JSON que l'API renverrait
$response = [
    'success' => true,
    'client' => [
        'nom' => $client['nomclient'],
        'prenom' => $client['prenomclient'],
        'email' => $client['emailclient'],
        'telephone' => $client['telclient'] ?? 'Non renseigné'
    ],
    'hasAdresse' => ($adresseResult->num_rows > 0)
];

// Ajouter l'adresse si elle existe
if ($adresseResult->num_rows > 0) {
    $response['adresse'] = [
        'raisonSociale' => $adresse['af_raisonsocial'] ?? 'Non renseigné',
        'adresse' => $adresse['af_nomrue'] ?? 'Non renseigné',
        'codePostal' => $adresse['af_codepostal'] ?? 'Non renseigné',
        'ville' => $adresse['af_ville'] ?? 'Non renseigné',
        'pays' => $adresse['af_pays'] ?? 'Non renseigné'
    ];
}

echo "\nRéponse JSON qui serait envoyée par l'API:\n";
echo json_encode($response, JSON_PRETTY_PRINT);
?>
