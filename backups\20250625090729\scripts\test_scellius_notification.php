<?php
// Script pour tester la notification Scellius
// Ce script simule une notification de paiement réussie

// Définir le chemin de base
define('BASE_PATH', dirname(__DIR__));

// Charger la configuration
require_once BASE_PATH . '/app/config/config.php';

// Charger le modèle VehiculeModel
require_once BASE_PATH . '/app/models/VehiculeModel.php';

// Fonction pour afficher un message coloré
function coloredOutput($message, $type = 'info') {
    $colors = [
        'info' => "\033[0;36m", // Cyan
        'success' => "\033[0;32m", // Vert
        'warning' => "\033[0;33m", // Jaune
        'error' => "\033[0;31m", // Rouge
        'reset' => "\033[0m" // Réinitialiser
    ];
    
    echo $colors[$type] . $message . $colors['reset'] . PHP_EOL;
}

// Vérifier si un ID de véhicule est fourni en argument
$vehiculeId = isset($argv[1]) ? intval($argv[1]) : null;
$clientEmail = isset($argv[2]) ? $argv[2] : '<EMAIL>';

if (!$vehiculeId) {
    coloredOutput("Erreur: Veuillez fournir un ID de véhicule en argument", 'error');
    coloredOutput("Usage: php test_scellius_notification.php [ID_VEHICULE] [EMAIL_CLIENT]", 'info');
    exit(1);
}

coloredOutput("Test de notification Scellius pour le véhicule ID: $vehiculeId et le client: $clientEmail", 'info');
echo PHP_EOL;

// Instancier le modèle de véhicule
$vehiculeModel = new VehiculeModel();

// Récupérer les informations du véhicule
$vehicule = $vehiculeModel->getVehiculeById($vehiculeId);

if (!$vehicule) {
    coloredOutput("Erreur: Véhicule ID $vehiculeId non trouvé", 'error');
    exit(1);
}

coloredOutput("Informations du véhicule:", 'info');
echo "ID: " . $vehicule['id'] . PHP_EOL;
echo "Référence: " . $vehicule['reference'] . PHP_EOL;
echo "Libellé: " . $vehicule['libelle'] . PHP_EOL;
echo "Statut actuel: " . ($vehicule['statut'] ?? 'Non défini') . PHP_EOL;
echo "Client actuel: " . ($vehicule['client'] ?? 'Aucun') . PHP_EOL;
echo "Référence de paiement: " . ($vehicule['rgltreference'] ?? 'Aucune') . PHP_EOL;
echo PHP_EOL;

// Vérifier si le véhicule est déjà vendu
if (isset($vehicule['statut']) && $vehicule['statut'] === 'Vendu') {
    coloredOutput("Attention: Ce véhicule est déjà marqué comme vendu", 'warning');
    $confirm = readline("Voulez-vous continuer quand même? (o/n): ");
    if (strtolower($confirm) !== 'o') {
        coloredOutput("Test annulé", 'info');
        exit(0);
    }
}

// Vérifier si le client a déjà acheté un véhicule
$clientHasVehicle = $vehiculeModel->clientHasVehicle($clientEmail);
if ($clientHasVehicle) {
    coloredOutput("Attention: Le client $clientEmail a déjà acheté un véhicule", 'warning');
    $confirm = readline("Voulez-vous continuer quand même? (o/n): ");
    if (strtolower($confirm) !== 'o') {
        coloredOutput("Test annulé", 'info');
        exit(0);
    }
}

// Générer un ID de transaction unique
$transId = 'TEST' . date('YmdHis') . rand(1000, 9999);

// Préparer les données pour la mise à jour
$data = [
    'reference' => $vehicule['reference'],
    'libelle' => $vehicule['libelle'],
    'descriptif' => $vehicule['descriptif'],
    'annee' => $vehicule['annee'],
    'prix' => $vehicule['prix'],
    'client' => $clientEmail,
    'client_adresse' => $vehicule['client_adresse'] ?? '',
    'statut' => 'Vendu',
    'rgltreference' => $transId,
    'certificat' => $vehicule['certificat'] ?? 0,
    'libelle_certificat' => $vehicule['libelle_certificat'] ?? '',
    'dateheureachat' => date('Y-m-d H:i:s'),
    'rgltdateheure' => date('Y-m-d H:i:s')
];

coloredOutput("Tentative de mise à jour du véhicule...", 'info');

// Mettre à jour le véhicule
$result = $vehiculeModel->updateVehicule($vehiculeId, $data);

if ($result) {
    coloredOutput("SUCCÈS: Véhicule ID $vehiculeId mis à jour avec succès", 'success');
    
    // Vérifier que la mise à jour a bien été effectuée
    $updatedVehicule = $vehiculeModel->getVehiculeById($vehiculeId);
    
    coloredOutput("Informations du véhicule après mise à jour:", 'info');
    echo "ID: " . $updatedVehicule['id'] . PHP_EOL;
    echo "Référence: " . $updatedVehicule['reference'] . PHP_EOL;
    echo "Libellé: " . $updatedVehicule['libelle'] . PHP_EOL;
    echo "Statut: " . ($updatedVehicule['statut'] ?? 'Non défini') . PHP_EOL;
    echo "Client: " . ($updatedVehicule['client'] ?? 'Aucun') . PHP_EOL;
    echo "Référence de paiement: " . ($updatedVehicule['rgltreference'] ?? 'Aucune') . PHP_EOL;
    echo "Date/heure d'achat: " . ($updatedVehicule['dateheureachat'] ?? 'Non définie') . PHP_EOL;
    echo "Date/heure de règlement: " . ($updatedVehicule['rgltdateheure'] ?? 'Non définie') . PHP_EOL;
} else {
    coloredOutput("ERREUR: Échec de la mise à jour du véhicule", 'error');
    echo "Erreur MySQL: " . $vehiculeModel->conn->error . PHP_EOL;
    
    // Tentative alternative avec une requête SQL simple
    coloredOutput("Tentative de mise à jour alternative...", 'info');
    
    $sql = "UPDATE vehicules SET statut = 'Vendu', client = ?, rgltreference = ?, dateheureachat = NOW() WHERE id = ?";
    $stmt = $vehiculeModel->conn->prepare($sql);
    $stmt->bind_param("ssi", $clientEmail, $transId, $vehiculeId);
    
    if ($stmt->execute()) {
        coloredOutput("SUCCÈS: Mise à jour alternative réussie", 'success');
        
        // Vérifier que la mise à jour a bien été effectuée
        $updatedVehicule = $vehiculeModel->getVehiculeById($vehiculeId);
        
        coloredOutput("Informations du véhicule après mise à jour alternative:", 'info');
        echo "ID: " . $updatedVehicule['id'] . PHP_EOL;
        echo "Statut: " . ($updatedVehicule['statut'] ?? 'Non défini') . PHP_EOL;
        echo "Client: " . ($updatedVehicule['client'] ?? 'Aucun') . PHP_EOL;
        echo "Référence de paiement: " . ($updatedVehicule['rgltreference'] ?? 'Aucune') . PHP_EOL;
    } else {
        coloredOutput("ERREUR CRITIQUE: Échec de la mise à jour alternative", 'error');
        echo "Erreur MySQL: " . $vehiculeModel->conn->error . PHP_EOL;
    }
}

echo PHP_EOL;
coloredOutput("Test terminé", 'info');
?>
