<?php
// Script pour tester la galerie d'images en ligne de commande
// Ce script teste directement la fonction getVehiculeImages et affiche les résultats

// Définir le chemin de base
define('BASE_PATH', dirname(__DIR__));

// Charger la configuration
require_once BASE_PATH . '/app/config/config.php';

// Charger le modèle VehiculeModel
require_once BASE_PATH . '/app/models/VehiculeModel.php';

// Fonction pour afficher un message coloré
function coloredOutput($message, $type = 'info') {
    $colors = [
        'info' => "\033[0;36m", // Cyan
        'success' => "\033[0;32m", // Vert
        'warning' => "\033[0;33m", // Jaune
        'error' => "\033[0;31m", // Rouge
        'reset' => "\033[0m" // Réinitialiser
    ];
    
    echo $colors[$type] . $message . $colors['reset'] . PHP_EOL;
}

// Fonction pour afficher un tableau
function displayTable($data, $headers = []) {
    // Déterminer la largeur des colonnes
    $columnWidths = [];
    
    // Initialiser les largeurs avec les en-têtes
    if (!empty($headers)) {
        foreach ($headers as $index => $header) {
            $columnWidths[$index] = strlen($header);
        }
    }
    
    // Ajuster les largeurs en fonction des données
    foreach ($data as $row) {
        foreach ($row as $index => $value) {
            $width = strlen($value);
            if (!isset($columnWidths[$index]) || $width > $columnWidths[$index]) {
                $columnWidths[$index] = $width;
            }
        }
    }
    
    // Afficher les en-têtes
    if (!empty($headers)) {
        foreach ($headers as $index => $header) {
            echo str_pad($header, $columnWidths[$index] + 2);
        }
        echo PHP_EOL;
        
        // Afficher une ligne de séparation
        foreach ($columnWidths as $width) {
            echo str_repeat('-', $width + 2);
        }
        echo PHP_EOL;
    }
    
    // Afficher les données
    foreach ($data as $row) {
        foreach ($row as $index => $value) {
            echo str_pad($value, $columnWidths[$index] + 2);
        }
        echo PHP_EOL;
    }
}

// Vérifier si une référence est fournie en argument
$reference = isset($argv[1]) ? $argv[1] : 'DODGE_1';

coloredOutput("Test de la galerie d'images pour la référence: $reference", 'info');
echo PHP_EOL;

// Instancier le modèle
$vehiculeModel = new VehiculeModel();

// Récupérer les images du véhicule
$startTime = microtime(true);
$images = $vehiculeModel->getVehiculeImages($reference);
$endTime = microtime(true);
$executionTime = round(($endTime - $startTime) * 1000, 2); // en millisecondes

// Afficher les résultats
coloredOutput("Résultats pour la référence: $reference", 'info');
coloredOutput("Temps d'exécution: $executionTime ms", 'info');
coloredOutput("Nombre d'images trouvées: " . count($images), count($images) > 0 ? 'success' : 'warning');
echo PHP_EOL;

if (count($images) > 0) {
    coloredOutput("Images trouvées:", 'success');
    $tableData = [];
    foreach ($images as $index => $image) {
        $absolutePath = BASE_PATH . '/' . $image;
        $exists = file_exists($absolutePath);
        $size = $exists ? filesize($absolutePath) : 0;
        
        $tableData[] = [
            ($index + 1),
            $image,
            $exists ? 'Oui' : 'Non',
            $size . ' octets'
        ];
    }
    
    displayTable($tableData, ['#', 'Chemin', 'Existe', 'Taille']);
} else {
    coloredOutput("Aucune image trouvée pour la référence: $reference", 'error');
}

echo PHP_EOL;

// Récupérer les informations de débogage
$debugInfo = VehiculeModel::$lastDebugInfo;

if (!empty($debugInfo)) {
    coloredOutput("Informations de débogage:", 'info');
    
    if (isset($debugInfo['debug']) && is_array($debugInfo['debug'])) {
        coloredOutput("Détails de la recherche:", 'info');
        
        $tableData = [];
        foreach ($debugInfo['debug'] as $debug) {
            if (isset($debug['pattern']) && isset($debug['count'])) {
                $tableData[] = [
                    $debug['source'] ?? 'N/A',
                    $debug['pattern'] ?? 'N/A',
                    $debug['count'] ?? 0
                ];
            }
        }
        
        if (!empty($tableData)) {
            displayTable($tableData, ['Source', 'Pattern', 'Nombre']);
        }
    }
}

echo PHP_EOL;

// Tester l'API directement
coloredOutput("Test de l'API vehicule-images.php:", 'info');

// Sauvegarder la valeur actuelle de $_GET
$originalGet = $_GET;

// Définir la référence pour l'API
$_GET['reference'] = $reference;

// Capturer la sortie de l'API
ob_start();
include_once(BASE_PATH . '/api/vehicule-images.php');
$apiResponse = ob_get_clean();

// Restaurer $_GET
$_GET = $originalGet;

// Décoder la réponse JSON
$apiData = json_decode($apiResponse, true);

if ($apiData) {
    coloredOutput("Réponse de l'API:", 'success');
    coloredOutput("Nombre d'images retournées: " . count($apiData['images']), count($apiData['images']) > 0 ? 'success' : 'warning');
    
    if (count($apiData['images']) > 0) {
        coloredOutput("URLs des images:", 'success');
        foreach ($apiData['images'] as $index => $url) {
            echo ($index + 1) . ". " . $url . PHP_EOL;
        }
    }
} else {
    coloredOutput("Erreur lors du décodage de la réponse de l'API", 'error');
    coloredOutput("Réponse brute:", 'error');
    echo substr($apiResponse, 0, 500) . (strlen($apiResponse) > 500 ? '...' : '') . PHP_EOL;
}

echo PHP_EOL;
coloredOutput("Fin du test", 'info');
?>
