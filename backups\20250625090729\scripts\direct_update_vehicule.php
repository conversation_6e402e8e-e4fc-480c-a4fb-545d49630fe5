<?php
// Script pour mettre à jour directement un véhicule dans la base de données
// sans passer par le modèle VehiculeModel

// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Définir les chemins manuellement
$basePath = dirname(__DIR__);
$appPath = $basePath . '/app';

// Inclure les fichiers nécessaires
require_once $appPath . '/config/config.php';
require_once $appPath . '/config/database.php';

// Fonction pour afficher un message
function output($message) {
    echo $message . "\n";
}

// Vérifier si un ID de véhicule est fourni en argument
$vehiculeId = isset($argv[1]) ? intval($argv[1]) : null;

if (!$vehiculeId) {
    output("Erreur: Veuillez fournir un ID de véhicule en argument");
    output("Usage: php direct_update_vehicule.php [ID_VEHICULE]");
    exit(1);
}

output("=== Test de mise à jour directe du véhicule ID: $vehiculeId ===");

// Établir une connexion à la base de données
try {
    $conn = getDbConnection();
    output("Connexion à la base de données établie avec succès");
} catch (Exception $e) {
    output("Erreur de connexion à la base de données: " . $e->getMessage());
    exit(1);
}

// Récupérer les informations du véhicule
$sql = "SELECT * FROM vehicules WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $vehiculeId);
$stmt->execute();
$result = $stmt->get_result();
$vehicule = $result->fetch_assoc();

if (!$vehicule) {
    output("Erreur: Véhicule ID $vehiculeId non trouvé");
    exit(1);
}

output("Informations du véhicule avant mise à jour:");
output("ID: " . $vehicule['id']);
output("Référence: " . $vehicule['reference']);
output("Libellé: " . $vehicule['libelle']);
output("Descriptif: " . $vehicule['descriptif']);
output("Prix: " . $vehicule['prix'] . " €");
output("Statut actuel: " . ($vehicule['statut'] ?? 'Non défini'));
output("Client actuel: " . ($vehicule['client'] ?? 'Aucun'));
output("Référence de paiement: " . ($vehicule['rgltreference'] ?? 'Aucune'));

// Générer un nouveau libellé pour le test
$newLibelle = $vehicule['libelle'] . ' (Test direct ' . date('Y-m-d H:i:s') . ')';

output("\nTentative de mise à jour directe du véhicule...");
output("Nouveau libellé: " . $newLibelle);

// Vérifier si le champ dateheureachat existe dans la table
$checkDateHeureAchatSql = "SHOW COLUMNS FROM vehicules LIKE 'dateheureachat'";
$checkDateHeureAchatResult = $conn->query($checkDateHeureAchatSql);
$dateheureachatExists = $checkDateHeureAchatResult->num_rows > 0;
output("Le champ dateheureachat existe: " . ($dateheureachatExists ? 'Oui' : 'Non'));

// Préparer la requête SQL de mise à jour
if ($dateheureachatExists) {
    $updateSql = "UPDATE vehicules SET 
                  libelle = ?,
                  descriptif = ?,
                  annee = ?,
                  prix = ?,
                  client = ?,
                  client_adresse = ?,
                  statut = ?,
                  rgltreference = ?,
                  certificat = ?,
                  libelle_certificat = ?,
                  dateheureachat = ?
                  WHERE id = ?";
    
    $stmt = $conn->prepare($updateSql);
    $stmt->bind_param("sssdssssiisi",
        $newLibelle,
        $vehicule['descriptif'],
        $vehicule['annee'],
        $vehicule['prix'],
        $vehicule['client'],
        $vehicule['client_adresse'],
        $vehicule['statut'],
        $vehicule['rgltreference'],
        $vehicule['certificat'],
        $vehicule['libelle_certificat'],
        $vehicule['dateheureachat'],
        $vehiculeId
    );
} else {
    $updateSql = "UPDATE vehicules SET 
                  libelle = ?,
                  descriptif = ?,
                  annee = ?,
                  prix = ?,
                  client = ?,
                  client_adresse = ?,
                  statut = ?,
                  rgltreference = ?,
                  certificat = ?,
                  libelle_certificat = ?
                  WHERE id = ?";
    
    $stmt = $conn->prepare($updateSql);
    $stmt->bind_param("sssdsssssii",
        $newLibelle,
        $vehicule['descriptif'],
        $vehicule['annee'],
        $vehicule['prix'],
        $vehicule['client'],
        $vehicule['client_adresse'],
        $vehicule['statut'],
        $vehicule['rgltreference'],
        $vehicule['certificat'],
        $vehicule['libelle_certificat'],
        $vehiculeId
    );
}

// Exécuter la requête
$result = $stmt->execute();

if ($result) {
    output("\nSUCCÈS: Véhicule ID $vehiculeId mis à jour avec succès");
    output("Lignes affectées: " . $stmt->affected_rows);
    
    // Vérifier que la mise à jour a bien été effectuée
    $checkSql = "SELECT * FROM vehicules WHERE id = ?";
    $checkStmt = $conn->prepare($checkSql);
    $checkStmt->bind_param("i", $vehiculeId);
    $checkStmt->execute();
    $checkResult = $checkStmt->get_result();
    $updatedVehicule = $checkResult->fetch_assoc();
    
    output("\nInformations du véhicule après mise à jour:");
    output("ID: " . $updatedVehicule['id']);
    output("Référence: " . $updatedVehicule['reference']);
    output("Libellé: " . $updatedVehicule['libelle']);
    output("Descriptif: " . $updatedVehicule['descriptif']);
    output("Prix: " . $updatedVehicule['prix'] . " €");
    output("Statut: " . ($updatedVehicule['statut'] ?? 'Non défini'));
    output("Client: " . ($updatedVehicule['client'] ?? 'Aucun'));
    output("Référence de paiement: " . ($updatedVehicule['rgltreference'] ?? 'Aucune'));
    
    if ($updatedVehicule['libelle'] === $newLibelle) {
        output("\nVérification: La mise à jour a fonctionné correctement.");
    } else {
        output("\nVérification: La mise à jour n'a pas fonctionné correctement.");
        output("Libellé attendu: " . $newLibelle);
        output("Libellé actuel: " . $updatedVehicule['libelle']);
    }
} else {
    output("\nERREUR: Échec de la mise à jour du véhicule");
    output("Erreur MySQL: " . $stmt->error . " / " . $conn->error);
}

// Fermer la connexion
$conn->close();

output("\n=== Test terminé ===");
?>
