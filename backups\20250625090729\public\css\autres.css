/* Remplacement de 'Army' par '<PERSON>' */
#top1, #top2 .suiveznous, #top #topdesc, #top #logo #help1 {
    font-family: '<PERSON>', sans-serif !important;
}
/* Remplacement de 'Conv_BERNHC' par 'Merriweather' */
#site #middle #titre_genre, .section {
    font-family: 'Merriweather', serif !important;
}

.grid-container {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

.inner-wrap2 {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    position: relative;
}

.vehicle-image-container {
    flex: 0 0 240px;
}

.vehicle-info {
    flex: 1;
}

.title_autre, .subtitle_autre {
    text-align: left;
}

.vehicle-card {
    display: block;
    width: 100%;
    position: relative;
    cursor: pointer;
}

.vehicle-image-container {
    position: relative;
    display: inline-block;
}

.sold-label {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-30deg);
    background-color: rgba(255, 0, 0, 0.7);
    color: white;
    font-weight: bold;
    padding: 10px 20px;
    font-size: 24px;
    text-transform: uppercase;
    border: 2px solid white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    z-index: 10;
}

.purchase-in-progress-label {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-30deg);
    background-color: rgba(40, 167, 69, 0.7); /* Couleur verte */
    color: white;
    font-weight: bold;
    padding: 10px 20px;
    font-size: 20px;
    text-transform: uppercase;
    border: 2px solid white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    z-index: 10;
    white-space: nowrap;
}

.vehicle-price {
    position: absolute;
    top: 10px;
    right: 10px;
    background-color: transparent;
    color: #333;
    font-weight: bold;
    padding: 8px 12px;
    font-size: 20px;
    z-index: 5;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

.certificate-container {
    position: absolute;
    top: 45px;
    right: 10px;
    background-color: transparent;
    color: #000000;
    padding: 5px 10px;
    z-index: 5;
    display: flex;
    align-items: center;
}

/* Cacher la case à cocher par défaut */
.certificate-checkbox {
    position: absolute;
    opacity: 0;
    cursor: pointer;
    height: 0;
    width: 0;
}

/* Créer une case à cocher personnalisée */
.certificate-container label {
    position: relative;
    padding-left: 22px; /* Reduit l'espace pour la case plus petite */
    cursor: pointer;
    display: inline-block;
    line-height: 16px;
    font-weight: bold;
    font-size: 15px;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.9);
}

.certificate-container label:before {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 14px; /* Case plus petite */
    height: 14px; /* Case plus petite */
    border: 1px solid #555;
    background: #fff;
    border-radius: 2px;
}

/* Style lorsque la case est cochée */
.certificate-checkbox:checked + label:after {
    content: "";
    position: absolute;
    left: 5px;
    top: 2px;
    width: 4px; /* Coche plus petite */
    height: 8px; /* Coche plus petite */
    border: solid #555;
    border-width: 0 2px 2px 0;
    transform: rotate(45deg);
}

/* Styles pour la galerie d'images */
.vehicle-image-container img {
    cursor: pointer;
    transition: transform 0.3s ease;
}

.vehicle-image-container img:hover {
    transform: scale(1.05);
}

/* Modal */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.9);
}

.modal-content {
    position: relative;
    margin: auto;
    padding: 0;
    width: 90%;
    max-width: 1200px;
    height: 90vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.image-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: calc(90vh - 60px);
    overflow: hidden;
}

#modalImage {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.close {
    position: absolute;
    top: 15px;
    right: 35px;
    color: #f1f1f1;
    font-size: 40px;
    font-weight: bold;
    transition: 0.3s;
    z-index: 1001;
}

.close:hover,
.close:focus {
    color: #bbb;
    text-decoration: none;
    cursor: pointer;
}

.navigation {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px 0;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 5px;
    margin-top: 10px;
}

.nav-button {
    background-color: transparent;
    color: white;
    border: 2px solid white;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    cursor: pointer;
    margin: 0 20px;
    transition: background-color 0.3s;
}

.nav-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
}

#imageCounter {
    color: white;
    font-size: 16px;
    font-weight: bold;
    min-width: 60px;
    text-align: center;
}

/* Styles pour le formulaire de filtrage */
.filter-container {
    margin-bottom: 20px;
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 5px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.filter-form {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    align-items: center;
}

.filter-item {
    flex: 1;
    min-width: 200px;
}

.search-input, .year-select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.filter-button {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.filter-button:hover {
    background-color: #45a049;
}

.reset-button {
    display: inline-block;
    margin-left: 10px;
    color: #666;
    text-decoration: none;
    font-size: 14px;
}

.reset-button:hover {
    text-decoration: underline;
}

/* Style pour la case à cocher "Afficher les véhicules vendus" */
.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    margin: 0;
    padding: 0;
}

.checkbox-container input[type="checkbox"] {
    margin-right: 5px;
    cursor: pointer;
}

.checkbox-text {
    font-size: 14px;
    color: #333;
}

/* Styles pour la modale */
.purchase-modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.purchase-modal-content {
    background-color: #fefefe;
    margin: 0 auto;
    padding: 15px;
    border: 1px solid #888;
    width: 90%;
    max-width: 500px;
    border-radius: 8px;
    position: relative;
    top: 15vh; /* Positionne la modale a environ 15% de la hauteur de la fenetre */
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.close-modal {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close-modal:hover {
    color: black;
}

.modal-vehicle-info {
    margin: 10px 0;
    display: flex;
    gap: 15px;
    align-items: flex-start;
}

.modal-vehicle-image {
    width: 180px;
    flex-shrink: 0;
}

.modal-vehicle-image img {
    width: 100%;
    height: auto;
    border-radius: 5px;
}

.modal-vehicle-details {
    flex: 1;
}

.modal-vehicle-details h3 {
    font-weight: bold;
    color: #333;
    margin-top: 0;
    font-size: 15px;
}

.modal-vehicle-details p {
    font-size: 0.85em;
    color: #555;
    margin-top: 5px;
    margin-bottom: 5px;
}

.modal-price {
    font-weight: bold;
    color: #333;
    font-size: 16px;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
    margin: 5px 0;
}

.purchase-modal-content h2 {
    font-family: 'Merriweather', serif;
    color: #494825;
    font-size: 1.3em;
    text-align: center;
    margin-bottom: 10px;
    padding-bottom: 5px;
    border-bottom: 1px solid #eee;
}

.modal-checkboxes {
    margin: 10px 0;
}

.modal-checkboxes label {
    display: flex;
    align-items: center;
    gap: 6px;
    margin: 5px 0;
    font-size: 13px;
    color: #333;
}

.modal-checkboxes input[type="checkbox"] {
    width: 13px;
    height: 13px;
    margin: 0;
}

.validate-purchase {
    background-color: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    width: 100%;
    margin-top: 10px;
}

.validate-purchase:hover {
    background-color: #218838;
}

.validate-purchase-disabled {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: not-allowed;
    font-size: 14px;
    width: 100%;
    margin-top: 10px;
}

.login-message {
    color: #dc3545;
    text-align: center;
    margin-top: 10px;
    font-size: 14px;
}

.client-info-section {
    margin-top: 10px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 5px;
    border-left: 3px solid #28a745;
    font-size: 0.85em;
}

.client-info-section h3 {
    margin-top: 0;
    color: #333;
    font-size: 14px;
    margin-bottom: 5px;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 3px;
}

.client-info-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 5px;
}

.client-info-item {
    margin-bottom: 3px;
}

.client-info-label {
    font-weight: bold;
    color: #495057;
    font-size: 12px;
    min-width: 90px;
}

.client-info-value {
    color: #212529;
    font-size: 12px;
}
