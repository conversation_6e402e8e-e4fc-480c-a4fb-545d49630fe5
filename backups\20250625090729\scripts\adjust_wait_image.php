<?php

// Chemin de l'image source
$sourcePath = __DIR__ . '/../public/img/site/wait-for-see.jpg';
$destinationPath = $sourcePath; // On va écraser l'image originale

// Vérifier si l'image existe
if (!file_exists($sourcePath)) {
    die("L'image source n'existe pas.");
}

// Charger l'image source
$sourceImage = imagecreatefromjpeg($sourcePath);
if (!$sourceImage) {
    die("Impossible de charger l'image source.");
}

// Obtenir les dimensions de l'image source
$sourceWidth = imagesx($sourceImage);
$sourceHeight = imagesy($sourceImage);

// Définir les nouvelles dimensions
// On augmente la largeur de 40% (20% de chaque côté)
// On augmente la hauteur de 10%
$newWidth = intval($sourceWidth * 1.4);
$newHeight = intval($sourceHeight * 1.1);

// Créer une nouvelle image
$newImage = imagecreatetruecolor($newWidth, $newHeight);

// Créer une couleur de fond sombre/kaki (proche du style militaire)
$backgroundColor = imagecolorallocate($newImage, 51, 51, 35); // Couleur kaki foncé

// Remplir l'image avec la couleur de fond
imagefill($newImage, 0, 0, $backgroundColor);

// Calculer les positions pour centrer l'image source
$x = ($newWidth - $sourceWidth) / 2;
$y = ($newHeight - $sourceHeight) / 2;

// Copier l'image source dans la nouvelle image
imagecopy($newImage, $sourceImage, $x, $y, 0, 0, $sourceWidth, $sourceHeight);

// Ajouter un effet de dégradé sur les bords
$steps = 50; // Nombre d'étapes pour le dégradé
for ($i = 0; $i < $steps; $i++) {
    $opacity = 127 * ($i / $steps); // Opacité progressive
    $blendColor = imagecolorallocatealpha($newImage, 51, 51, 35, $opacity);
    
    // Dégradé gauche
    imagefilledrectangle($newImage, $i, 0, $i + 1, $newHeight, $blendColor);
    // Dégradé droite
    imagefilledrectangle($newImage, $newWidth - $i - 1, 0, $newWidth - $i, $newHeight, $blendColor);
}

// Sauvegarder l'image
imagejpeg($newImage, $destinationPath, 95);

// Libérer la mémoire
imagedestroy($sourceImage);
imagedestroy($newImage);

echo "L'image a été modifiée avec succès.\n";
?>