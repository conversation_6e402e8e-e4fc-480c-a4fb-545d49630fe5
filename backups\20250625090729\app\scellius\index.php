<?php
// Inclure la configuration
require_once 'scellius_module/config.php';

// Fonction pour générer la signature Scellius V2
function calculate_scellius_signature(array $fields, string $secretKey, string $algo = SCELLIUS_SIGN_ALGO): string
{
    // 1. Trier les champs par ordre alphabétique
    ksort($fields);

    // 2. Concaténer les valeurs des champs avec '+'
    $stringToSign = '';
    foreach ($fields as $key => $value) {
        // Inclure uniquement les champs commençant par 'vads_'
        if (strpos($key, 'vads_') === 0) {
            $stringToSign .= $value . '+';
        }
    }

    // 3. Ajouter la clé secrète à la fin
    $stringToSign .= $secretKey;

    // 4. Calculer le hash (SHA-256 par défaut)
    return base64_encode(hash_hmac(strtolower(str_replace('-', '', $algo)), $stringToSign, $secretKey, true));
}

// Initialisation des variables
$amount_euros = isset($_POST['amount']) ? floatval($_POST['amount']) : 10.00;
$vehicle_title = $_POST['vehicle_title'] ?? '';
$has_certificate = $_POST['has_certificate'] ?? '0';
$needs_transport = $_POST['needs_transport'] ?? '0';
$form_fields = [];
$scellius_url = SCELLIUS_URL;

// Traitement si le formulaire est soumis (pour préparer les données Scellius)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['amount'])) {
    // Convertir le montant en centimes (requis par Scellius)
    $amount_cents = round($amount_euros * 100);

    // Générer un identifiant de transaction unique (simple exemple, à améliorer en production)
    $transaction_id = substr(uniqid(), -6); // 6 derniers caractères pour la démo

    // Obtenir la date/heure actuelle au format requis (UTC)
    $utc_date = gmdate('YmdHis'); // AAAAMMJJHHMMSS

    // Préparer les champs pour Scellius
    $fields = [
        'vads_action_mode'      => 'INTERACTIVE',
        'vads_amount'           => $amount_cents,
        'vads_ctx_mode'         => SCELLIUS_CTX_MODE,
        'vads_currency'         => '978', // 978 pour EUR
        'vads_page_action'      => 'PAYMENT',
        'vads_payment_config'   => 'SINGLE',
        'vads_site_id'          => SCELLIUS_SITE_ID,
        'vads_trans_date'       => $utc_date,
        'vads_trans_id'         => $transaction_id,
        'vads_version'          => SCELLIUS_API_VERSION,
        'vads_url_return'       => SCELLIUS_URL_RETURN,
        'vads_url_notify'       => SCELLIUS_URL_NOTIFY,
        'vads_order_info'       => $vehicle_title,
        'vads_order_info2'      => $vehicle_reference . ($needs_transport === '1' ? "|Transport:Oui" : "|Transport:Non"),
        'vads_order_info3'      => "Certificat: " . ($has_certificate === '1' ? 'Oui' : 'Non')
    ];

    // Calculer la signature
    $signature = calculate_scellius_signature($fields, SCELLIUS_SECRET_KEY);
    $fields['signature'] = $signature;

    $form_fields = $fields;
}

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Paiement Scellius Simple</title>
    <style>
        body { font-family: sans-serif; padding: 20px; }
        label, input { display: block; margin-bottom: 10px; }
        input[type="number"] { width: 100px; }
        button { padding: 10px 15px; cursor: pointer; }
        .scellius-form { margin-top: 20px; border-top: 1px solid #ccc; padding-top: 20px; }
    </style>
</head>
<body>

    <h1>Initiation de Paiement Scellius</h1>

    <?php if (empty($form_fields)): ?>
        <form method="POST" action="">
            <label for="amount">Montant à payer (€) :</label>
            <input type="number" id="amount" name="amount" step="0.01" min="0.01" value="<?php echo htmlspecialchars($amount_euros); ?>" required>
            <label for="vehicle_title">Titre du véhicule :</label>
            <input type="text" id="vehicle_title" name="vehicle_title" value="<?php echo htmlspecialchars($vehicle_title); ?>" required>
            <label for="has_certificate">Certificat :</label>
            <select id="has_certificate" name="has_certificate">
                <option value="1" <?php echo $has_certificate === '1' ? 'selected' : ''; ?>>Oui</option>
                <option value="0" <?php echo $has_certificate === '0' ? 'selected' : ''; ?>>Non</option>
            </select>
            <label for="needs_transport">Transport :</label>
            <select id="needs_transport" name="needs_transport">
                <option value="1" <?php echo $needs_transport === '1' ? 'selected' : ''; ?>>Oui</option>
                <option value="0" <?php echo $needs_transport === '0' ? 'selected' : ''; ?>>Non</option>
            </select>
            <button type="submit">Payer</button>
        </form>
    <?php else: ?>
        <p>Préparation du paiement de <?php echo htmlspecialchars($amount_euros); ?> €...</p>
        <p>Vous allez être redirigé vers la plateforme de paiement.</p>

        <!-- Formulaire à envoyer à Scellius -->
        <form name="scellius_payment_form" action="<?php echo htmlspecialchars($scellius_url); ?>" method="POST" class="scellius-form">
            <?php foreach ($form_fields as $name => $value): ?>
                <input type="hidden" name="<?php echo htmlspecialchars($name); ?>" value="<?php echo htmlspecialchars($value); ?>">
            <?php endforeach; ?>
            <noscript>
                <p>Le JavaScript est désactivé. Cliquez sur le bouton ci-dessous pour continuer.</p>
                <button type="submit">Procéder au paiement</button>
            </noscript>
        </form>

        <!-- Redirection automatique via JavaScript -->
        <script type="text/javascript">
            // Soumet automatiquement le formulaire dès que la page est chargée
            document.addEventListener('DOMContentLoaded', function() {
                document.scellius_payment_form.submit();
            });
        </script>
    <?php endif; ?>

</body>
</html>