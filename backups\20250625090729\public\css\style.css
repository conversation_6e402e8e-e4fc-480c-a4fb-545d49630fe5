﻿*,html{
	margin: 0;
	padding: 0;
}

body {
	background-color: #494825; /*vert fonce*/
	color: #000000;
	background-image: url(../img/fond/fond3.jpg); /* Chemin corrigé */
	background-size: cover;
	background-attachment:fixed;
	font-family: "Trebuchet MS", Arial, Helvetica, sans-serif;
}


/* Footer styles removed */

img {
	border: 0px;
}

h1 {
	/*position:relative;*/
	margin:10px;
	font-size: 24px;
	color:#494825;
	vertical-align:middle;
	font-weight:normal;
	font-variant:small-caps;
	/*text-transform: uppercase;*/
	border-bottom:1px dotted;
}
h1 span {
	position:absolute;
	margin-top:5px;
	margin-left:2px;

}

h2 {
	margin:10px;
	font-size:20px;
	color:#888545;
	vertical-align:middle;
	font-weight:normal;
	font-variant:small-caps;
	text-decoration:underline;

}

a, a:visited {
	text-decoration: none;
	color: #000000;
	background: transparent;
}

a:hover {
	text-decoration: none;
	color: #7FB5D1;
}

.clear{
	clear:both;
	margin-bottom:-1px;
	padding-bottom:1px;
}
#site #middle #titre_genre {
	font-family:'Conv_BERNHC',Sans-Serif;
	width: 100%;
	text-align: center;
	font-size: 48px;
	border-top: 5px solid #000000;
	border-bottom: 5px solid #000000;
	background-color: #666633;
	color: #FFFFFF;
}

#site #middle #produit {
	margin-left: 7px;
	background-color: #e4e4e4;
	height: 105px;
	width: 600px;
	display: block;
	font-family: "Trebuchet MS", Arial, Helvetica, sans-serif;
}
#site #middle #produit #image_produit {

	height: 94px;
	width: 121px;
	float: left;
	padding-top: 11px;
	padding-left: 10px;
}
#site #middle #produit #left_produit {
	width: 177px;
	float: left;
	height: 105px;
}
#site #middle #produit #left_produit #genre_produit {
	height: 40px;
	width: 172px;
	padding-left: 5px;
	padding-top: 12px;
}
#site #middle #produit #left_produit #groupe_produit {
	height: 53px;
	width: 172px;
	padding-left: 5px;
}
#site #middle #produit #middle_produit {

	float: left;
	height: 105px;
	width: 316px;
}
#site #middle #produit #middle_produit #description_produit {
	height: 43px;
	width: 312px;
	padding-left: 4px;
	font-weight: bold;
	font-size: 15px;
}
#site #middle #produit #middle_produit #reference_produit {
	height: 16px;
	width: 312px;
	font-size: 11px;
	padding-left: 4px;
}
#site #middle #produit #middle_produit #qualite_produit {
	font-size: 14px;
	height: 34px;
	width: 124px;
	float: left;
	padding-top: 11px;
	padding-left: 4px;
}
#site #middle #produit #middle_produit #stock_produit {
	height: 40px;
	width: 30px;
	float: left;
	padding-top: 5px;
	padding-left: 8px;
}
#site #middle #produit #middle_produit #quantite_produit {
	font-size: 14px;
	height: 34px;
	width: 125px;
	float: left;
	padding-top: 11px;
}
#site #middle #produit #middle_produit #qté {
	padding-top: 3px;
	height: 34px;
	width: 20px;
	float: left;
}
#site #middle #produit #right_produit {
	height: 105px;
	width: 147px;

	float: left;
}
#site #middle #produit #right_produit #prixttc_produit {
	height: 30px;
	width: 140px;
	font-weight: bold;
	color: #990000;
	font-size: 18px;
	padding-top: 4px;
	padding-left: 7px;
}
#site #middle #produit #right_produit #prixht_produit {

	height: 19px;
	width: 140px;
	font-size: 12px;
	padding-left: 7px;
	font-style: italic;
}
#produit #right_produit #ajouter_produit {
	height: 31px;
	width: 121px;
	background-repeat: no-repeat;
	font-weight: bold;
	margin-top: 10px;
	margin-left: 14px;
}
#site #middle #produit #right_produit #ajouter_produit a {
	box-shadow: 1px 1px 2px 0px rgba(119, 119, 119, 0.8);
	background-image: url(../img/fond_ajouter.png); /* Chemin corrigé */
	height: 27px;
	width: 82px;
	display: block;
	padding-left: 39px;
	background-repeat: no-repeat;
	padding-top: 4px;
	text-decoration: none;
	color: #000;

}
#site #middle #produit #right_produit #ajouter_produit a:hover {
	background-image: url(../img/fond_ajouter_hover.png); /* Chemin corrigé */
	color: #399;
}
#produit #right_produit #bouton_devis {
	height: 31px;
	width: 121px;
	background-repeat: no-repeat;
	font-weight: bold;
	margin-top: 10px;
	margin-left: 14px;
}
#site #middle #produit #right_produit #bouton_devis a {
	box-shadow: 1px 1px 2px 0px rgba(119, 119, 119, 0.8);
	background-image: url(../img/bouton_devis.png); /* Chemin corrigé */
	height: 27px;
	width: 82px;
	display: block;
	padding-left: 39px;
	background-repeat: no-repeat;
	padding-top: 4px;
	text-decoration: none;
	color: #000;

}
#site #middle #produit #right_produit #bouton_devis a:hover {
	background-image: url(../img/bouton_devis.png); /* Chemin corrigé */
	color: #cc0000;
}
#site #middle #produit #separation {
	height: 105px;
	width: 3px;
	float: left;
	background-image: url(../img/separation.png); /* Chemin corrigé */
	background-repeat: no-repeat;
}

#top1 {
	height: 50px;
	width: 920px;
	margin: 0 auto;
	font-family: Army;
	background-color: #cccc66;
}
#top1 .emailtop1 img {
	position: absolute;
}
#top1 .emailtop1 {
	height: 30px;
	width: 250px;
	text-align: center;
	padding-top: 0px;
	float: left;
	margin-top: 3px;
	margin-left: 0px;
	margin-right: 0px;

}
#top1 .adressetop1 {
	height: 30px;
	text-align: center;
	width: 200px;
	padding-top: 0px;
	float: left;
	margin-left: 0px;
	margin-right: 0px;
	margin-top: 3px;

}
#top1 #google_translate_element {
	height: 40px;
	width: 180px;
	margin-top:0px;
	margin-left:0px;
	float: left;
}
#top2 {
	height: 50px;
	width: 970px;
	margin: 0 auto;
	background-color: #666633;
	box-shadow: 8px 0px 8px -3px #000000, -8px 0px 8px -3px #000000;
}

#top2 .searchtop2 {
	float: left;
	height: 43px;
	width: 360px;
	margin-left: 250px;
	margin-right: 20px;
	padding-top: 7px;
}
.inputnewstyle {
  background: linear-gradient(to bottom, #f7f7f8 0%,#ffffff 100%);
  border-radius: 3px;
  border: none;
  box-shadow: 0 1px 2px rgba(0,0,0,0.2) inset, 0 -1px 0 rgba(0,0,0,0.05) inset;
  transition: all 0.2s linear;
  font-family: "Helvetica Neue", sans-serif;
  font-size: 13px;
  color: #222222;
  height: 33px;
  width: 200px;
  padding-left: 5px;
  font-style: italic;
}
.inputnewstyle:focus{
    box-shadow: 0 1px 0 #2392F3 inset, 0 -1px 0 #2392F3 inset, 1px 0 0 #2392F3 inset, -1px 0 0 #2392F3 inset, 0 0 4px rgba(35,146,243,0.5);
    outline: none;
    background: #FFF;
	font-style: normal;
}
.inputsearch {
  float: left;
  background: url(../img/icone_search.png) 12px 11px no-repeat, linear-gradient(to bottom, #f7f7f8 0%,#ffffff 100%); /* Chemin corrigé */
  border-radius: 3px;
  border: none;
  box-shadow: 0 1px 2px rgba(0,0,0,0.2) inset, 0 -1px 0 rgba(0,0,0,0.05) inset;
  transition: all 0.2s linear;
  font-family: "Helvetica Neue", sans-serif;
  font-size: 13px;
  color: #222222;
  position: relative;
  height: 33px;
  width: 200px;
  padding-left: 35px;
  font-style: italic;
}
.inputsearch:focus{
    box-shadow: 0 1px 0 #2392F3 inset, 0 -1px 0 #2392F3 inset, 1px 0 0 #2392F3 inset, -1px 0 0 #2392F3 inset, 0 0 4px rgba(35,146,243,0.5);
    outline: none;
    background: url(../img/icone_search.png) 12px 11px no-repeat, #FFF; /* Chemin corrigé */
	font-style: normal;
}
.submitsearch {
	float: left;
	border-radius: 3px;
	border: none;
	box-shadow: 0 1px 2px rgba(0,0,0,0.2) inset, 0 -1px 0 rgba(0,0,0,0.05) inset;
	font-family: "Helvetica Neue", sans-serif;
	font-size: 18px;
	color: #222222;
	position: relative;
	height: 33px;
	width: 100px;
	padding-left: 10px;
	padding-right: 96px;
	margin-left: 5px;
	background-color: #F60;
	cursor:pointer;
}
.submitsearch:hover {
	background:-webkit-gradient(linear, left top, left bottom, color-stop(0.05, #ccc2a6), color-stop(1, #eae0c2));
	background:-moz-linear-gradient(top, #ccc2a6 5%, #eae0c2 100%);
	background:-webkit-linear-gradient(top, #ccc2a6 5%, #eae0c2 100%);
	background:-o-linear-gradient(top, #ccc2a6 5%, #eae0c2 100%);
	background:-ms-linear-gradient(top, #ccc2a6 5%, #eae0c2 100%);
	background:linear-gradient(to bottom, #ccc2a6 5%, #eae0c2 100%);
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#ccc2a6', endColorstr='#eae0c2',GradientType=0);
	background-color:#ccc2a6;
	color: #222222;
}
.submitsearch:active {
	position:relative;
	top:1px;
}

.submitnewstyle {
	padding-right: 5px;
	padding-left: 5px;
	width: 40px;
	background-color: #2A88AD;
	color: white;
	border-radius: 3px;
	border: none;
	box-shadow: 0 1px 2px rgba(0,0,0,0.2) inset, 0 -1px 0 rgba(0,0,0,0.05) inset;
	font-family: "Helvetica Neue", sans-serif;
	font-size: 18px;
	position: relative;
	height: 33px;
	width: 40px;
	margin-left: 5px;
	cursor:pointer;
}
.submitnewstyle:hover {
	background:-webkit-gradient(linear, left top, left bottom, color-stop(0.05, #ccc2a6), color-stop(1, #eae0c2));
	background:-moz-linear-gradient(top, #ccc2a6 5%, #eae0c2 100%);
	background:-webkit-linear-gradient(top, #ccc2a6 5%, #eae0c2 100%);
	background:-o-linear-gradient(top, #ccc2a6 5%, #eae0c2 100%);
	background:-ms-linear-gradient(top, #ccc2a6 5%, #eae0c2 100%);
	background:linear-gradient(to bottom, #ccc2a6 5%, #eae0c2 100%);
	filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#ccc2a6', endColorstr='#eae0c2',GradientType=0);
	background-color:#ccc2a6;
	color: #222222;
}
.submitnewstyle:active {
	position:relative;
	top:1px;
}
#top2 .suiveznous {
	float: left;
	font-family: Army;
	color: #FFF;
	margin-top: 15px;
	font-size: 18px;
	margin-left: 10px;
	text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}
#top2 img {
	float: left;
	margin-left: 5px;
	margin-top: 5px;
}

#site {
	text-align: left;
	background-color: #c8c665;
	box-shadow: 8px 0px 8px -3px #000000, -8px 0px 8px -3px #000000;
	width: 920px;
	margin: 0 auto;
	position: relative;
}

#popupadd {
	text-align:center;
	background-color:#cec763;
	width:500px;
	height:200px;
	top:200px;
	left:220px;
	z-index:42;
	border:dashed;
	position:absolute;
}
#popupdevis {
	text-align:center;
	background-color:#cec763;
	width:500px;
	height:auto;
	top:200px;
	left:220px;
	z-index:42;
	border:dashed;
	position:absolute;
}
#popupdevis #close {
	position:absolute;
	right:5px;
	top:5px;
}
#popupadd #close {
	position:absolute;
	right:5px;
	top:5px;
}
#top {
	height: 150px;
	width: 970px;
	margin: 0 auto;
	background-image: url(../img/fond_top.png); /* Chemin corrigé */
	background-repeat: repeat-x;
	position: relative;
	box-shadow: 8px 0px 8px -3px #000000, -8px 0px 8px -3px #000000;
}
#top #logo {
	height: 100px;
	width: 180px;
	float: left;
	margin-left: 10px;
	margin-top: 0px;
}
#top #logo #help1 {
	font-family: Army;
	font-size: 18px;
	color: #FFF;
	text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
	margin-top: 10px;
	margin-left: 0px;
	height: 30px;
	width: 180px;
	background-color: #666633;
	border-radius: 5px;
	padding-top: 5px;
	padding-left: 0px;
}
#top #logo #help1 a {
	color: #FFF;
	text-decoration: none;
	margin-left: 5px;
	margin-right: 5px;
}
#top #logo #help1 a:hover {
	color: #F60;
}
#top #mainmenu_bord {
	height: 10px;
	width: 780px;
	float: left;
	margin-top: 0px;
	margin-left: 0px;
}
#top #mainmenu {
	height: 80px;
	width: 780px;
	float: left;
	margin-top: 0px;
	margin-left: 0px;
}
#top #menu {
	height: 60px;
	width: 780px;
	float: left;
	margin-top: 0px;
	margin-left: 0px;
	background-color: #666633;
}
#top #menu #user {
	height: 60px;
	width: 250px;
	float: left;
	margin-left: 10px;
	font-size: 12px;
	color: #FFF;
}
#top #menu #user a {
	color: #FFF;
	text-decoration: none;
}
#top #menu #user a:hover {
	color: #F60;
}
#top #menu #user .inputlogin {
	float: left;
	background: url(../img/icone_user.png) 12px 11px no-repeat, linear-gradient(to bottom, #f7f7f8 0%,#ffffff 100%); /* Chemin corrigé */
	border-radius: 3px;
	border: none;
	box-shadow: 0 1px 2px rgba(0,0,0,0.2) inset, 0 -1px 0 rgba(0,0,0,0.05) inset;
	transition: all 0.2s linear;
	font-family: "Helvetica Neue", sans-serif;
	font-size: 11px;
	color: #222222;
	position: relative;
	height: 20px;
	width: 150px;
	padding-left: 35px;
	font-style: italic;
}
#top #menu #user .inputlogin:focus {
	box-shadow: 0 1px 0 #2392F3 inset, 0 -1px 0 #2392F3 inset, 1px 0 0 #2392F3 inset, -1px 0 0 #2392F3 inset, 0 0 4px rgba(35,146,243,0.5);
	outline: none;
	background: url(../img/icone_user.png) 12px 11px no-repeat, #FFF; /* Chemin corrigé */
	font-style: normal;
}
#top #menu #user .inputpassword {
	float: left;
	background: url(../img/icone_password.png) 12px 11px no-repeat, linear-gradient(to bottom, #f7f7f8 0%,#ffffff 100%); /* Chemin corrigé */
	border-radius: 3px;
	border: none;
	box-shadow: 0 1px 2px rgba(0,0,0,0.2) inset, 0 -1px 0 rgba(0,0,0,0.05) inset;
	transition: all 0.2s linear;
	font-family: "Helvetica Neue", sans-serif;
	font-size: 11px;
	color: #222222;
	position: relative;
	height: 20px;
	width: 150px;
	padding-left: 35px;
	font-style: italic;
}
#top #menu #user .inputpassword:focus {
	box-shadow: 0 1px 0 #2392F3 inset, 0 -1px 0 #2392F3 inset, 1px 0 0 #2392F3 inset, -1px 0 0 #2392F3 inset, 0 0 4px rgba(35,146,243,0.5);
	outline: none;
	background: url(../img/icone_password.png) 12px 11px no-repeat, #FFF; /* Chemin corrigé */
	font-style: normal;
}
#top #menu #panier {
	height: 60px;
	width: 450px;
	float: left;
	margin-left: 10px;
	color: #FFF;
	font-size: 12px;
}
#top #menu #panier a {
	color: #FFF;
	text-decoration: none;
}
#top #menu #panier a:hover {
	color: #F60;
}
#top #menu #panier .panier {
	background-image: url(../img/panier.png); /* Chemin corrigé */
	background-repeat: no-repeat;
	height: 40px;
	width: 40px;
	display: block;
	margin-top: 10px;
}
#top #menu #panier #articlepanier {
	margin-top: 10px;
	margin-left: 10px;
}
#top #menu #panier .btn_panier {
	margin-top: 5px;
	margin-left: 10px;
}
#top #menu #panier .btn_panier span {
	margin-right: 10px;
}
#top #menu #panier .btn_panier span img {
	vertical-align: middle;
}
#top #topdesc {
	height: 100px;
	width: 180px;
	float: left;
	margin-left: 0px;
	margin-top: 0px;
	font-family: Army;
	font-size: 18px;
	color: #FFF;
	text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
	text-align: center;
}
#top #topdesc a {
	color: #FFF;
	text-decoration: none;
}
#top #topdesc a:hover {
	color: #F60;
}
#middle {
	width: 920px;
	margin: 0 auto;
	background-color: #c8c665;
	padding-bottom: 10px;
	padding-top: 10px;
}
#middle #left {
	width: 150px;
	float: left;
	margin-left: 10px;
	margin-right: 10px;
}
#middle #left #menu_left {
	width: 150px;
	background-color: #666633;
	border-radius: 5px;
	padding-bottom: 10px;
	margin-bottom: 10px;
}
#middle #left #menu_left #titre_menu_left {
	font-family: Army;
	font-size: 18px;
	color: #FFF;
	text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
	text-align: center;
	padding-top: 5px;
	padding-bottom: 5px;
}
#middle #left #menu_left ul {
	list-style-type: none;
	margin: 0px;
	padding: 0px;
}
#middle #left #menu_left ul li {
	margin-left: 10px;
	margin-right: 10px;
	margin-top: 5px;
	padding-left: 5px;
	border-bottom-width: 1px;
	border-bottom-style: dotted;
	border-bottom-color: #FFF;
}
#middle #left #menu_left ul li a {
	color: #FFF;
	text-decoration: none;
	font-size: 12px;
}
#middle #left #menu_left ul li a:hover {
	color: #F60;
}
#middle #left #menu_left ul li ul {
	margin-left: 10px;
}
#middle #left #menu_left ul li ul li {
	border-bottom-style: none;
	margin-top: 0px;
	margin-left: 0px;
	margin-right: 0px;
	padding-left: 0px;
}
#middle #left #menu_left ul li ul li a {
	font-size: 11px;
}
#middle #left #pub_left {
	width: 150px;
	margin-top: 10px;
}
#middle #center {
	width: 740px;
	float: left;
	background-color: #e4e4e4;
	border-radius: 5px;
	padding-bottom: 10px;
	min-height: 500px;
}
#middle #center #ariane {
	height: 20px;
	width: 720px;
	margin-left: 10px;
	margin-top: 5px;
	font-size: 11px;
	border-bottom-width: 1px;
	border-bottom-style: dotted;
	border-bottom-color: #000;
}
#middle #center #ariane a {
	color: #000;
	text-decoration: none;
}
#middle #center #ariane a:hover {
	color: #F60;
}
#middle #center #contenu {
	width: 720px;
	margin-left: 10px;
	margin-top: 10px;
}
/* Bottom styles removed */

/* Style pour les tableaux */
.tab_result {
	border-collapse: collapse;
	border: 1px solid #000000;
	width: 100%;
	margin-top: 10px;
	margin-bottom: 10px;
	font-size: 12px;
}
.tab_result th {
	background-color: #666633;
	color: #FFF;
	font-weight: bold;
	padding: 5px;
	border: 1px solid #000000;
}
.tab_result td {
	padding: 5px;
	border: 1px solid #000000;
}
.tab_result tr:nth-child(even) {
	background-color: #f2f2f2;
}
.tab_result tr:hover {
	background-color: #ddd;
}

/* Style pour les formulaires */
.form_style {
	width: 100%;
	margin-top: 10px;
	margin-bottom: 10px;
	font-size: 12px;
}
.form_style label {
	display: block;
	margin-bottom: 5px;
	font-weight: bold;
}
.form_style input[type="text"], .form_style input[type="password"], .form_style input[type="email"], .form_style select, .form_style textarea {
	width: 98%;
	padding: 5px;
	margin-bottom: 10px;
	border: 1px solid #ccc;
	border-radius: 3px;
}
.form_style input[type="submit"], .form_style input[type="button"] {
	background-color: #666633;
	color: #FFF;
	padding: 8px 15px;
	border: none;
	border-radius: 3px;
	cursor: pointer;
	font-size: 14px;
}
.form_style input[type="submit"]:hover, .form_style input[type="button"]:hover {
	background-color: #494825;
}

/* Style pour la pagination */
.pagination {
	text-align: center;
	margin-top: 20px;
}
.pagination a, .pagination strong {
	padding: 5px 10px;
	margin: 0 2px;
	border: 1px solid #ccc;
	text-decoration: none;
	color: #333;
	border-radius: 3px;
}
.pagination strong {
	background-color: #666633;
	color: #FFF;
	border-color: #666633;
}
.pagination a:hover {
	background-color: #ddd;
}

/* Style pour les messages d'erreur/succès */
.message {
	padding: 10px;
	margin-bottom: 15px;
	border-radius: 3px;
}
.message.error {
	background-color: #f2dede;
	color: #a94442;
	border: 1px solid #ebccd1;
}
.message.success {
	background-color: #dff0d8;
	color: #3c763d;
	border: 1px solid #d6e9c6;
}

/* Styles spécifiques pour la page "autres" (ajoutés précédemment) */
.form-style-10 { /* Exemple */
    /* ... */
}
.inner-wrap2 {
    border: 1px solid #ccc;
    padding: 10px;
    margin: 10px;
    text-align: center;
    cursor: pointer;
    display: inline-block; /* Ou utiliser flexbox/grid */
    vertical-align: top;
    width: 240px; /* Correspond à la largeur de l'image */
    box-sizing: border-box;
    background-color: #fff; /* Ajout fond blanc */
    box-shadow: 2px 2px 5px rgba(0,0,0,0.1); /* Ajout ombre légère */
    transition: transform 0.2s ease-in-out; /* Ajout transition */
}
.inner-wrap2:hover {
    transform: translateY(-3px); /* Effet de surélévation au survol */
    box-shadow: 3px 3px 8px rgba(0,0,0,0.2);
}
.inner-wrap2 img {
    max-width: 100%;
    height: auto;
    margin-bottom: 10px; /* Espace sous l'image */
}
.title_autre {
    font-weight: bold;
    margin-top: 5px;
    color: #333; /* Couleur titre */
}
.subtitle_autre {
    font-size: 0.9em;
    color: #555;
}
.section {
    font-size: 1.5em;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
    color: #494825; /* Couleur titre section */
    text-align: center; /* Centrer titre section */
    font-family: Army, sans-serif; /* Police spécifique si disponible */
}
.section span {
    color: #04B45F; /* Couleur exemple */
    margin-right: 5px;
}
/* Ajustements pour l'affichage en grille simple */
.grid-container {
    display: flex;
    flex-wrap: wrap;
    justify-content: center; /* Ou space-around, etc. */
    gap: 20px; /* Espace entre les éléments augmenté */
    padding: 10px; /* Padding autour de la grille */
}

/* Styles pour la galerie d'images des véhicules */
.vehicle-image-gallery .card {
    margin-bottom: 1rem;
    transition: transform 0.2s ease-in-out;
    height: 100%;
}

.vehicle-image-gallery .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.vehicle-image-gallery .card-img-top {
    height: 200px;
    object-fit: cover;
    width: 100%;
}

.vehicle-image-gallery .card-body {
    padding: 0.75rem;
    text-align: center;
}

/* Style pour le formulaire d'ajout d'image */
.image-upload-form {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.25rem;
    border: 1px dashed #dee2e6;
}

.image-upload-form:hover {
    border-color: #6c757d;
    background-color: #fff;
}

/* Responsive */
@media (max-width: 768px) {
    .vehicle-image-gallery .col-md-3 {
        flex: 0 0 50%;
        max-width: 50%;
    }
}

@media (max-width: 576px) {
    .vehicle-image-gallery .col-md-3 {
        flex: 0 0 100%;
        max-width: 100%;
    }
}

/* Responsive (exemple simple) */
@media (max-width: 768px) {
    #site, #top, #top1, #top2, #middle {
        width: 100%;
        box-shadow: none;
    }
    #middle #left {
        display: none; /* Cacher menu gauche sur petits écrans */
    }
    #middle #center {
        width: 100%;
        margin-left: 0;
    }
    #top #mainmenu, #top #menu {
        width: auto;
        float: none;
    }
    #top2 .searchtop2 {
        margin-left: 10px;
        width: auto;
    }
    .grid-container {
        justify-content: center;
    }
    .inner-wrap2 {
        width: 45%; /* Deux éléments par ligne sur tablette */
    }
}

@media (max-width: 480px) {
    .inner-wrap2 {
        width: 90%; /* Un élément par ligne sur mobile */
    }
    #top #logo, #top #topdesc {
        float: none;
        text-align: center;
        width: 100%;
        margin-left: 0;
    }
    #top #menu #user, #top #menu #panier {
        float: none;
        width: 100%;
        text-align: center;
        margin-left: 0;
    }
    #top #menu #user form {
        margin-left: 0;
        display: inline-block;
    }
}

/* --- Fin des styles ajoutés/modifiés --- */

/* Le reste du fichier CSS original (potentiellement très long) */
/* ... (supposons que le reste du fichier suit ici) ... */

/* Exemple de fin de fichier */
/* Footer styles removed */

/* Styles pour subCatMenu.css (potentiellement inclus ou référencé) */
.subcatmenu {
    /* ... */
}

/* Styles pour style_bourse.css */
.bourse-table {
    /* ... */
}

/* Styles pour style_pagination.css */
/* (Déjà inclus des styles de pagination génériques plus haut) */

/* Styles pour error.css */
.error-message {
    /* ... */
}

/* Styles pour style_menu_facture.css */
.menu-facture {
    /* ... */
}

/* Styles pour style_facture.css */
.facture-details {
    /* ... */
}

/* Styles pour style_vercorps_list.css */
.vercorps-item {
    /* ... */
}

/* Styles pour style_form_client.css */
.form-client {
    /* ... */
}

/* Styles pour style_table_client.css */
.table-client {
    /* ... */
}

/* Styles pour police/army/army.css */
@font-face {
    font-family: 'Army';
    /* src: url('../fonts/army.woff2') format('woff2'), ... */ /* Chemin à vérifier/corriger */
}

/* Styles pour police/BERNHC/fonts.css */
@font-face {
    font-family: 'Conv_BERNHC';
    /* src: url('../fonts/bernhc.woff2') format('woff2'), ... */ /* Chemin à vérifier/corriger */
}

/* Styles pour validationEngine.jquery.css */
.validation-advice {
    /* ... */
}

/* Styles pour template.css */
.template-header {
    /* ... */
}

/* Styles pour police/bitter/bitter.css */
@font-face {
    font-family: 'Bitter';
    /* src: url('../fonts/bitter-regular.woff2') format('woff2'), ... */ /* Chemin à vérifier/corriger */
}

/* Styles pour dock.css */
.dock {
    /* ... */
}

/* Styles pour style_popup.css */
.popup-overlay {
    /* ... */
}

/* Styles pour jsSimpleDatePickr.css */
.datepicker {
    /* ... */
}

/* Styles pour jscroll.0.3.css */
.jscroller {
    /* ... */
}

/* Styles pour bjqs.css */
.bjqs {
    /* ... */
}

/* Styles pour jquery.lightbox-0.3.css */
#lightbox {
    /* ... */
}

/* Styles pour lytebox.css */
#lytebox {
    /* ... */
}

/* Styles pour style_promo.css */
.promo-banner {
    /* ... */
}

/* Styles pour style_bonne_affaire.css */
.bonne-affaire {
    /* ... */
}

/* Styles pour style_nouveaute.css */
.nouveaute-item {
    /* ... */
}

/* Styles pour style_description.css */
.description-produit {
    /* ... */
}

/* Styles pour action man.css */
.action-man {
    /* ... */
}
