<?php
// Vérifier si APP_PATH est défini
if (!defined('APP_PATH')) {
    define('APP_PATH', dirname(__DIR__));
}

require_once APP_PATH . '/config/database.php';

class VehiculeModel {
    public $conn; // Changé de private à public pour permettre l'accès direct

    public function __construct() {
        $this->conn = getDbConnection();

        // Vérifier si la connexion est établie
        if ($this->conn->connect_error) {
            error_log("Erreur de connexion à la base de données des véhicules: " . $this->conn->connect_error);
        } else {
            error_log("Connexion à la base de données des véhicules établie avec succès");
        }
    }

    public function getAllVehicules($marque = null, $search = null, $annee = null, $hideVendus = false) {
        $sql = "SELECT * FROM vehicules";
        $conditions = [];
        $params = [];
        $types = "";

        // Ajouter la condition pour la marque si spécifiée
        if ($marque) {
            $conditions[] = "marque = ?";
            $params[] = $marque;
            $types .= "s";
        }

        // Ajouter la condition pour la recherche si spécifiée
        if ($search) {
            $conditions[] = "(libelle LIKE ? OR descriptif LIKE ?)";
            $searchParam = "%$search%";
            $params[] = $searchParam;
            $params[] = $searchParam;
            $types .= "ss";
        }

        // Ajouter la condition pour l'année si spécifiée
        if ($annee && $annee !== 'all') {
            $conditions[] = "annee = ?";
            $params[] = $annee;
            $types .= "s";
        }

        // Ajouter la condition pour masquer les véhicules vendus si demandé
        if ($hideVendus) {
            $conditions[] = "(statut IS NULL OR statut != 'Vendu')";
        }

        // Construire la clause WHERE si des conditions existent
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }

        // Préparer et exécuter la requête
        $stmt = $this->conn->prepare($sql);

        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }

        $stmt->execute();
        $result = $stmt->get_result();
        $vehicules = [];

        while ($row = $result->fetch_assoc()) {
            $vehicules[] = $row;
        }

        return $vehicules;
    }

    /**
     * Récupère toutes les années distinctes des véhicules
     * @return array Liste des années disponibles
     */
    public function getYears() {
        $sql = "SELECT DISTINCT annee FROM vehicules WHERE annee IS NOT NULL AND annee != '' ORDER BY annee";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->get_result();
        $years = [];

        while ($row = $result->fetch_assoc()) {
            $years[] = $row['annee'];
        }

        return $years;
    }

    /**
     * Récupère tous les modèles distincts des véhicules
     * @return array Liste des modèles disponibles
     */
    public function getModels() {
        $sql = "SELECT DISTINCT libelle FROM vehicules WHERE libelle IS NOT NULL AND libelle != '' ORDER BY libelle";
        $stmt = $this->conn->prepare($sql);
        $stmt->execute();
        $result = $stmt->get_result();
        $models = [];

        while ($row = $result->fetch_assoc()) {
            $models[] = $row['libelle'];
        }

        return $models;
    }

    public function getVehiculeByReference($reference) {
        $sql = "SELECT * FROM vehicules WHERE reference = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("s", $reference);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            return $result->fetch_assoc();
        }

        return null;
    }

    public function getVehiculeById($id) {
        $sql = "SELECT * FROM vehicules WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("i", $id);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            return $result->fetch_assoc();
        }

        return null;
    }

    public function getVehiculeImages($reference) {
        if (!defined('BASE_PATH')) {
            define('BASE_PATH', dirname(dirname(__DIR__)));
        }

        $directory = BASE_PATH . '/public/img/';
        $allImages = [];
        $debugInfo = [];

        // Fonction pour ajouter des informations de débogage
        $addDebug = function($pattern, $files, $source) use (&$debugInfo) {
            $debugInfo[] = [
                'pattern' => $pattern,
                'count' => count($files),
                'source' => $source
            ];
        };

        // Fonction pour vérifier si un fichier existe manuellement
        $fileExists = function($path) {
            return file_exists($path);
        };

        // Méthode 1: Utiliser glob pour trouver les images
        try {
            // Format 1: DODGE_1 (1).jpg, DODGE_1 (2).jpg, etc.
            $pattern = $directory . strtoupper($reference) . ' (*).{jpg,jpeg,png,gif}';
            $newFormatImages = glob($pattern, GLOB_BRACE) ?: [];
            if (!empty($newFormatImages)) {
                $allImages = array_merge($allImages, $newFormatImages);
                $addDebug($pattern, $newFormatImages, 'glob_format1');
            }

            // Si aucune image trouvée, essayer sans les parenthèses
            if (empty($allImages)) {
                $pattern = $directory . strtoupper($reference) . '_*.{jpg,jpeg,png,gif}';
                $oldFormatImages = glob($pattern, GLOB_BRACE) ?: [];
                if (!empty($oldFormatImages)) {
                    $allImages = array_merge($allImages, $oldFormatImages);
                    $addDebug($pattern, $oldFormatImages, 'glob_format2');
                }
            }

            // Si toujours rien, chercher l'image principale
            if (empty($allImages)) {
                $pattern = $directory . strtoupper($reference) . '.{jpg,jpeg,png,gif}';
                $mainImage = glob($pattern, GLOB_BRACE) ?: [];
                if (!empty($mainImage)) {
                    $allImages = array_merge($allImages, $mainImage);
                    $addDebug($pattern, $mainImage, 'glob_main');
                }
            }

            // Si toujours rien, chercher avec des underscores remplacés par des espaces
            if (empty($allImages)) {
                $searchRef = str_replace('_', ' ', $reference);
                $pattern = $directory . strtoupper($searchRef) . '.{jpg,jpeg,png,gif}';
                $spaceFormatImages = glob($pattern, GLOB_BRACE) ?: [];
                if (!empty($spaceFormatImages)) {
                    $allImages = array_merge($allImages, $spaceFormatImages);
                    $addDebug($pattern, $spaceFormatImages, 'glob_space');
                }
            }

            // Si toujours rien, chercher dans le répertoire img/autres/
            if (empty($allImages) && is_dir($directory . 'autres/')) {
                $autresDir = $directory . 'autres/';

                // Essayer avec le format original
                $pattern = $autresDir . strtoupper($reference) . '.{jpg,jpeg,png,gif}';
                $autresImages = glob($pattern, GLOB_BRACE) ?: [];
                if (!empty($autresImages)) {
                    $allImages = array_merge($allImages, $autresImages);
                    $addDebug($pattern, $autresImages, 'glob_autres');
                } else {
                    // Essayer avec des espaces
                    $searchRef = str_replace('_', ' ', $reference);
                    $pattern = $autresDir . strtoupper($searchRef) . '.{jpg,jpeg,png,gif}';
                    $autresSpaceImages = glob($pattern, GLOB_BRACE) ?: [];
                    if (!empty($autresSpaceImages)) {
                        $allImages = array_merge($allImages, $autresSpaceImages);
                        $addDebug($pattern, $autresSpaceImages, 'glob_autres_space');
                    }
                }
            }
        } catch (Exception $e) {
            // En cas d'erreur avec glob, continuer avec la méthode manuelle
            $debugInfo[] = ['error' => $e->getMessage()];
        }

        // Méthode 2: Si glob ne fonctionne pas ou ne trouve rien, vérifier manuellement les fichiers courants
        if (empty($allImages)) {
            $extensions = ['jpg', 'jpeg', 'png', 'gif'];
            $formats = [
                strtoupper($reference) . '.%s',
                strtoupper($reference) . ' (1).%s',
                strtoupper($reference) . '_001.%s',
                strtoupper(str_replace('_', ' ', $reference)) . '.%s'
            ];

            foreach ($formats as $format) {
                foreach ($extensions as $ext) {
                    $filename = sprintf($format, $ext);
                    $path = $directory . $filename;

                    if ($fileExists($path)) {
                        $allImages[] = $path;
                        $addDebug('manual_check', [$path], 'manual_' . $filename);
                    }
                }
            }
        }

        // Si toujours aucune image trouvée, créer une image par défaut
        if (empty($allImages)) {
            // Utiliser une image par défaut qui existe certainement
            $defaultImage = $directory . 'image_not_found.png';
            if ($fileExists($defaultImage)) {
                $allImages[] = $defaultImage;
                $addDebug('default_image', [$defaultImage], 'fallback');
            }
        }

        // Trier les images par leur numéro
        usort($allImages, function($a, $b) {
            // Extraire les numéros entre parenthèses
            preg_match('/\((\d+)\)/', $a, $matchesA);
            preg_match('/\((\d+)\)/', $b, $matchesB);

            if (empty($matchesA) && empty($matchesB)) {
                return strcmp($a, $b);
            }

            $numA = isset($matchesA[1]) ? intval($matchesA[1]) : 0;
            $numB = isset($matchesB[1]) ? intval($matchesB[1]) : 0;

            return $numA - $numB;
        });

        // Convertir les chemins absolus en chemins relatifs en gardant le préfixe "public"
        $relativeImages = array_map(function($path) {
            $relativePath = str_replace(BASE_PATH . '/', '', $path);
            return $relativePath; // Garder le préfixe "public" dans le chemin
        }, $allImages);

        // Stocker les informations de débogage dans une propriété statique pour pouvoir les récupérer plus tard
        static::$lastDebugInfo = [
            'reference' => $reference,
            'count' => count($relativeImages),
            'debug' => $debugInfo,
            'images' => $relativeImages
        ];

        return $relativeImages;
    }

    // Propriété statique pour stocker les informations de débogage
    public static $lastDebugInfo = [];

    public function getVehiculesWithPagination($page = 1, $perPage = 20) {
        $offset = ($page - 1) * $perPage;
        $sql = "SELECT * FROM vehicules ORDER BY reference ASC LIMIT ? OFFSET ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("ii", $perPage, $offset);
        $stmt->execute();
        $result = $stmt->get_result();
        return $result->fetch_all(MYSQLI_ASSOC);
    }

    /**
     * Récupère les véhicules filtrés avec pagination
     * @param int $page Numéro de la page
     * @param int $perPage Nombre d'éléments par page
     * @param string|null $marque Marque du véhicule
     * @param string|null $search Terme de recherche
     * @param string|null $annee Modèle du véhicule (libellé)
     * @param bool $hideVendus Masquer les véhicules vendus
     * @return array Liste des véhicules filtrés et paginés
     */
    public function getVehiculesWithFiltersAndPagination($page = 1, $perPage = 5, $marque = null, $search = null, $annee = null, $hideVendus = false) {
        $offset = ($page - 1) * $perPage;
        $sql = "SELECT * FROM vehicules";
        $conditions = [];
        $params = [];
        $types = "";

        // Ajouter la condition pour la marque si spécifiée
        if ($marque) {
            $conditions[] = "marque = ?";
            $params[] = $marque;
            $types .= "s";
        }

        // Ajouter la condition pour la recherche si spécifiée
        if ($search) {
            $conditions[] = "(reference LIKE ? OR libelle LIKE ? OR descriptif LIKE ?)";
            $searchParam = "%$search%";
            $params[] = $searchParam;
            $params[] = $searchParam;
            $params[] = $searchParam;
            $types .= "sss";
        }

        // Ajouter la condition pour le modèle (libellé) si spécifié
        if ($annee && $annee !== 'all') {
            $conditions[] = "libelle = ?";
            $params[] = $annee;
            $types .= "s";
        }

        // Ajouter la condition pour masquer les véhicules vendus si demandé
        if ($hideVendus) {
            $conditions[] = "(statut IS NULL OR statut != 'Vendu')";
        }

        // Construire la clause WHERE si des conditions existent
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }

        // Ajouter l'ordre et la pagination
        $sql .= " ORDER BY reference ASC LIMIT ? OFFSET ?";
        $params[] = $perPage;
        $params[] = $offset;
        $types .= "ii";

        // Préparer et exécuter la requête
        $stmt = $this->conn->prepare($sql);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        return $result->fetch_all(MYSQLI_ASSOC);
    }

    /**
     * Compte le nombre total de véhicules avec les filtres appliqués
     * @param string|null $marque Marque du véhicule
     * @param string|null $search Terme de recherche
     * @param string|null $annee Modèle du véhicule (libellé)
     * @param bool $hideVendus Masquer les véhicules vendus
     * @return int Nombre total de véhicules
     */
    public function getTotalVehiculesWithFilters($marque = null, $search = null, $annee = null, $hideVendus = false) {
        $sql = "SELECT COUNT(*) as total FROM vehicules";
        $conditions = [];
        $params = [];
        $types = "";

        // Ajouter la condition pour la marque si spécifiée
        if ($marque) {
            $conditions[] = "marque = ?";
            $params[] = $marque;
            $types .= "s";
        }

        // Ajouter la condition pour la recherche si spécifiée
        if ($search) {
            $conditions[] = "(reference LIKE ? OR libelle LIKE ? OR descriptif LIKE ?)";
            $searchParam = "%$search%";
            $params[] = $searchParam;
            $params[] = $searchParam;
            $params[] = $searchParam;
            $types .= "sss";
        }

        // Ajouter la condition pour le modèle (libellé) si spécifié
        if ($annee && $annee !== 'all') {
            $conditions[] = "libelle = ?";
            $params[] = $annee;
            $types .= "s";
        }

        // Ajouter la condition pour masquer les véhicules vendus si demandé
        if ($hideVendus) {
            $conditions[] = "(statut IS NULL OR statut != 'Vendu')";
        }

        // Construire la clause WHERE si des conditions existent
        if (!empty($conditions)) {
            $sql .= " WHERE " . implode(" AND ", $conditions);
        }

        // Préparer et exécuter la requête
        $stmt = $this->conn->prepare($sql);
        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();
        return $row['total'];
    }

    public function getTotalVehicules() {
        $sql = "SELECT COUNT(*) as total FROM vehicules";
        $result = $this->conn->query($sql);
        $row = $result->fetch_assoc();
        return $row['total'];
    }

    public function deleteVehicule($id) {
        $sql = "DELETE FROM vehicules WHERE id = ?";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("i", $id);
        return $stmt->execute();
    }

    public function updateVehicule($id, $data) {
        // Journaliser les données reçues pour le débogage
        error_log("Mise à jour du véhicule ID $id avec les données: " . json_encode($data));

        // Vérifier si la connexion est toujours valide
        if ($this->conn->ping()) {
            error_log("La connexion à la base de données est active");
        } else {
            error_log("La connexion à la base de données n'est plus active, tentative de reconnexion");
            $this->conn = getDbConnection();

            if ($this->conn->connect_error) {
                error_log("Échec de la reconnexion: " . $this->conn->connect_error);
                return false;
            }
        }

        // Vérifier si le champ dateheureachat existe dans la table
        try {
            $checkDateHeureAchatSql = "SHOW COLUMNS FROM vehicules LIKE 'dateheureachat'";
            $checkDateHeureAchatResult = $this->conn->query($checkDateHeureAchatSql);

            if ($checkDateHeureAchatResult === false) {
                error_log("Erreur lors de la vérification du champ dateheureachat: " . $this->conn->error);
                return false;
            }

            $dateheureachatExists = $checkDateHeureAchatResult->num_rows > 0;
            error_log("Le champ dateheureachat existe: " . ($dateheureachatExists ? 'Oui' : 'Non'));

            // Si le statut est 'Vendu', on s'assure que dateheureachat contient l'heure du règlement
            if (isset($data['statut']) && $data['statut'] === 'Vendu' && $dateheureachatExists) {
                error_log("Statut Vendu détecté - Mise à jour de dateheureachat avec l'heure actuelle");
                $data['dateheureachat'] = date('Y-m-d H:i:s');
            }

            // Vérifier si la référence existe déjà pour un autre véhicule
            if (isset($data['reference'])) {
                $checkRefSql = "SELECT id FROM vehicules WHERE reference = ? AND id != ?";
                $checkRefStmt = $this->conn->prepare($checkRefSql);
                $checkRefStmt->bind_param("si", $data['reference'], $id);
                $checkRefStmt->execute();
                $checkRefResult = $checkRefStmt->get_result();

                if ($checkRefResult->num_rows > 0) {
                    error_log("ERREUR: La référence " . $data['reference'] . " existe déjà pour un autre véhicule");
                    return false;
                }
            }

            // Utiliser une méthode alternative pour la mise à jour
            // Construire la requête SQL dynamiquement
            $updateFields = array();
            $types = "";
            $params = array();

            // Ajouter les champs à mettre à jour
            if (isset($data['reference'])) {
                $updateFields[] = "reference = ?";
                $types .= "s";
                $params[] = $data['reference'];
            }

            if (isset($data['libelle'])) {
                $updateFields[] = "libelle = ?";
                $types .= "s";
                $params[] = $data['libelle'];
            }

            if (isset($data['descriptif'])) {
                $updateFields[] = "descriptif = ?";
                $types .= "s";
                $params[] = $data['descriptif'];
            }

            if (isset($data['annee'])) {
                $updateFields[] = "annee = ?";
                $types .= "s";
                $params[] = $data['annee'];
            }

            if (isset($data['prix'])) {
                $updateFields[] = "prix = ?";
                $types .= "d";
                $params[] = $data['prix'];
            }

            if (isset($data['client'])) {
                $updateFields[] = "client = ?";
                $types .= "s";
                $params[] = $data['client'];
            }

            if (isset($data['client_adresse'])) {
                $updateFields[] = "client_adresse = ?";
                $types .= "s";
                $params[] = $data['client_adresse'];
            }

            if (isset($data['statut'])) {
                $updateFields[] = "statut = ?";
                $types .= "s";
                $params[] = $data['statut'];
            }

            if (isset($data['rgltreference'])) {
                $updateFields[] = "rgltreference = ?";
                $types .= "s";
                $params[] = $data['rgltreference'];
            }

            if (isset($data['certificat'])) {
                $updateFields[] = "certificat = ?";
                $types .= "i";
                $params[] = $data['certificat'];
            }

            if (isset($data['libelle_certificat'])) {
                $updateFields[] = "libelle_certificat = ?";
                $types .= "s";
                $params[] = $data['libelle_certificat'];
            }

            if (isset($data['aidetransport'])) {
                $updateFields[] = "aidetransport = ?";
                $types .= "i";
                $params[] = $data['aidetransport'];
            }

            if ($dateheureachatExists && isset($data['dateheureachat'])) {
                $updateFields[] = "dateheureachat = ?";
                $types .= "s";
                $params[] = $data['dateheureachat'];
            }

            // Ajouter l'ID à la fin pour la clause WHERE
            $types .= "i";
            $params[] = $id;

            // Construire la requête SQL complète
            $sql = "UPDATE vehicules SET " . implode(", ", $updateFields) . " WHERE id = ?";

            error_log("Requête SQL: " . $sql);
            error_log("Types de paramètres: " . $types);

            // Préparer et exécuter la requête
            $stmt = $this->conn->prepare($sql);

            if ($stmt === false) {
                error_log("Erreur lors de la préparation de la requête: " . $this->conn->error);
                return false;
            }

            // Créer un tableau de références pour bind_param
            $bindParams = array();
            $bindParams[] = &$types;

            for ($i = 0; $i < count($params); $i++) {
                $bindParams[] = &$params[$i];
            }

            // Appeler bind_param avec les références
            call_user_func_array(array($stmt, 'bind_param'), $bindParams);

            // Exécuter la requête
            $result = $stmt->execute();

            // Si l'exécution échoue, journaliser l'erreur
            if (!$result) {
                error_log("Erreur lors de la mise à jour du véhicule ID $id: " . $stmt->error . " / " . $this->conn->error);
            } else {
                error_log("Mise à jour du véhicule ID $id réussie. Lignes affectées: " . $stmt->affected_rows);

                // Vérifier si des lignes ont été affectées
                if ($stmt->affected_rows === 0) {
                    error_log("Aucune ligne n'a été modifiée. Vérifier si les données sont identiques à celles déjà en base.");

                    // Récupérer les données actuelles du véhicule pour comparaison
                    $currentSql = "SELECT * FROM vehicules WHERE id = ?";
                    $currentStmt = $this->conn->prepare($currentSql);
                    $currentStmt->bind_param("i", $id);
                    $currentStmt->execute();
                    $currentResult = $currentStmt->get_result();
                    $currentVehicule = $currentResult->fetch_assoc();

                    if ($currentVehicule) {
                        error_log("Données actuelles du véhicule: " . json_encode($currentVehicule));
                    }
                }
            }

            return $result;
        } catch (Exception $e) {
            error_log("Exception lors de la mise à jour du véhicule ID $id: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Met à jour un véhicule en utilisant sa référence comme critère
     * @param string $reference Référence du véhicule
     * @param array $data Données à mettre à jour
     * @return bool True si la mise à jour a réussi, False sinon
     */
    public function updateVehiculeByReference($reference, $data) {
        // Journaliser les données reçues pour le débogage
        error_log("Mise à jour du véhicule avec référence $reference avec les données: " . json_encode($data));

        // Vérifier si le champ dateheureachat existe dans la table
        $checkDateHeureAchatSql = "SHOW COLUMNS FROM vehicules LIKE 'dateheureachat'";
        $checkDateHeureAchatResult = $this->conn->query($checkDateHeureAchatSql);
        $dateheureachatExists = $checkDateHeureAchatResult->num_rows > 0;
        error_log("Le champ dateheureachat existe: " . ($dateheureachatExists ? 'Oui' : 'Non'));

        // Si le statut est 'Vendu', on s'assure que dateheureachat contient l'heure du règlement
        if (isset($data['statut']) && $data['statut'] === 'Vendu' && $dateheureachatExists) {
            error_log("Statut Vendu détecté - Mise à jour de dateheureachat avec l'heure actuelle");
            $data['dateheureachat'] = date('Y-m-d H:i:s');
        }

        $sql = "UPDATE vehicules SET
                libelle = ?,
                descriptif = ?,
                annee = ?,
                prix = ?,
                client = ?,
                client_adresse = ?,
                statut = ?,
                rgltreference = ?,
                certificat = ?,
                libelle_certificat = ?,
                aidetransport = ?,
                dateheureachat = ?
                WHERE reference = ?";

        // Préparer les variables pour bind_param
        $libelle = $data['libelle'];
        $descriptif = $data['descriptif'];
        $annee = $data['annee'];
        $prix = $data['prix'];
        $client = $data['client'];
        $client_adresse = $data['client_adresse'];
        $statut = $data['statut'];
        $rgltreference = $data['rgltreference'];
        $certificat = $data['certificat'];
        $libelle_certificat = $data['libelle_certificat'];
        $aidetransport = isset($data['aidetransport']) ? $data['aidetransport'] : 0;
        $dateheureachat = $data['dateheureachat'];

        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("sssdssssiisss",
            $libelle,
            $descriptif,
            $annee,
            $prix,
            $client,
            $client_adresse,
            $statut,
            $rgltreference,
            $certificat,
            $libelle_certificat,
            $aidetransport,
            $dateheureachat,
            $reference
        );

        error_log("Paramètres de la requête: dateheureachat=" . $data['dateheureachat']);

        // Exécuter la requête et retourner le résultat
        $result = $stmt->execute();

        // Si l'exécution échoue, journaliser l'erreur
        if (!$result) {
            error_log("Erreur lors de la mise à jour du véhicule avec référence $reference: " . $this->conn->error);
        }

        return $result;
    }

    /**
     * Met à jour le statut d'un véhicule et enregistre la date/heure de début du processus d'achat
     * Note: dateheureachat sera mis à jour à nouveau avec l'heure du règlement lorsque le paiement sera validé
     * @param int $id ID du véhicule
     * @param string $email Email du client
     * @return bool True si la mise à jour a réussi, False sinon
     */
    public function startPurchaseProcess($id, $email) {
        // Vérifier d'abord si le véhicule est disponible
        $vehicule = $this->getVehiculeById($id);
        if (!$vehicule) {
            error_log("startPurchaseProcess: Véhicule avec ID $id non trouvé");
            return false;
        }

        // Vérifier le statut du véhicule
        if (!empty($vehicule['statut']) && $vehicule['statut'] !== 'Disponible' && ($vehicule['statut'] !== 'Achat en cours' || $vehicule['client'] !== $email)) {
            error_log("startPurchaseProcess: Véhicule avec ID $id non disponible, statut: " . $vehicule['statut'] . ", client: " . $vehicule['client']);
            return false;
        }

        error_log("startPurchaseProcess: Mise à jour du statut pour le véhicule ID $id, référence: " . $vehicule['reference'] . ", libellé: " . $vehicule['libelle'] . ", client: $email");

        $sql = "UPDATE vehicules SET
                client = ?,
                statut = 'Achat en cours',
                dateheureachat = NOW() /* Enregistre l'heure de début du processus d'achat */
                WHERE id = ?";

        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("si", $email, $id);
        $result = $stmt->execute();

        if ($result) {
            error_log("startPurchaseProcess: Statut mis à jour avec succès pour le véhicule ID $id");
        } else {
            error_log("startPurchaseProcess: Erreur lors de la mise à jour du statut pour le véhicule ID $id: " . $this->conn->error);
        }

        return $result;
    }

    /**
     * Met à jour le statut d'un véhicule par sa référence et enregistre la date/heure de début du processus d'achat
     * Note: dateheureachat sera mis à jour à nouveau avec l'heure du règlement lorsque le paiement sera validé
     * @param string $reference Référence du véhicule
     * @param string $email Email du client
     * @return bool True si la mise à jour a réussi, False sinon
     */
    public function startPurchaseProcessByReference($reference, $email) {
        // Vérifier d'abord si le véhicule est disponible
        $vehicule = $this->getVehiculeByReference($reference);
        if (!$vehicule) {
            error_log("startPurchaseProcessByReference: Véhicule avec référence $reference non trouvé");
            return false;
        }

        // Vérifier le statut du véhicule
        if (!empty($vehicule['statut']) && $vehicule['statut'] !== 'Disponible' && ($vehicule['statut'] !== 'Achat en cours' || $vehicule['client'] !== $email)) {
            error_log("startPurchaseProcessByReference: Véhicule avec référence $reference non disponible, statut: " . $vehicule['statut'] . ", client: " . $vehicule['client']);
            return false;
        }

        error_log("startPurchaseProcessByReference: Mise à jour du statut pour le véhicule référence $reference, ID: " . $vehicule['id'] . ", libellé: " . $vehicule['libelle'] . ", client: $email");

        $sql = "UPDATE vehicules SET
                client = ?,
                statut = 'Achat en cours',
                dateheureachat = NOW() /* Enregistre l'heure de début du processus d'achat */
                WHERE reference = ?";

        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("ss", $email, $reference);
        $result = $stmt->execute();

        if ($result) {
            error_log("startPurchaseProcessByReference: Statut mis à jour avec succès pour le véhicule référence $reference");
        } else {
            error_log("startPurchaseProcessByReference: Erreur lors de la mise à jour du statut pour le véhicule référence $reference: " . $this->conn->error);
        }

        return $result;
    }

    /**
     * Réinitialise le statut d'un véhicule à "Disponible" en cas d'échec de paiement
     * @param int $id ID du véhicule ou null
     * @param string $title Titre du véhicule ou null
     * @param string $email Email du client ou null
     * @param string $reference Référence du véhicule ou null
     * @return bool True si la mise à jour a réussi, False sinon
     */
    public function resetVehiculeStatus($id = null, $title = null, $email = null, $reference = null) {
        // Au moins un paramètre doit être fourni
        if ($id === null && $title === null && $email === null && $reference === null) {
            return false;
        }

        $conditions = [];
        $params = [];
        $types = "";

        // Construire la requête en fonction des paramètres fournis
        if ($id !== null) {
            $conditions[] = "id = ?";
            $params[] = $id;
            $types .= "i";
        }

        if ($title !== null) {
            $conditions[] = "libelle = ?";
            $params[] = $title;
            $types .= "s";
        }

        if ($email !== null) {
            $conditions[] = "client = ?";
            $params[] = $email;
            $types .= "s";
        }

        if ($reference !== null) {
            $conditions[] = "reference = ?";
            $params[] = $reference;
            $types .= "s";
        }

        // Ajouter la condition pour ne réinitialiser que les véhicules en "Achat en cours"
        $conditions[] = "statut = 'Achat en cours'";

        $sql = "UPDATE vehicules SET
                statut = 'Disponible',
                client = '',
                dateheureachat = NULL
                WHERE " . implode(" AND ", $conditions);

        $stmt = $this->conn->prepare($sql);

        if (!empty($params)) {
            $stmt->bind_param($types, ...$params);
        }

        return $stmt->execute();
    }

    /**
     * Réinitialise le statut d'un véhicule à "Disponible" en cas d'échec de paiement, en utilisant sa référence
     * @param string $reference Référence du véhicule
     * @return bool True si la mise à jour a réussi, False sinon
     */
    public function resetVehiculeStatusByReference($reference) {
        if (empty($reference)) {
            return false;
        }

        $sql = "UPDATE vehicules SET
                statut = 'Disponible',
                client = '',
                dateheureachat = NULL
                WHERE reference = ? AND statut = 'Achat en cours'";

        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("s", $reference);

        return $stmt->execute();
    }

    /**
     * Vérifie si un client a déjà acheté un véhicule
     * @param string $email Email du client
     * @return bool True si le client a déjà acheté un véhicule, False sinon
     */
    public function clientHasVehicle($email) {
        if (empty($email)) {
            return false;
        }

        // Vérifier uniquement les véhicules avec statut 'Vendu'
        // Les véhicules en 'Achat en cours' ne sont pas considérés comme achetés
        $sql = "SELECT COUNT(*) as count FROM vehicules WHERE client = ? AND statut = 'Vendu'";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        $row = $result->fetch_assoc();

        return $row['count'] > 0;
    }

    /**
     * Vérifie si un client a un véhicule en cours d'achat
     * @param string $email Email du client
     * @return array|null Données du véhicule en cours d'achat ou null si aucun
     */
    public function getClientPendingVehicle($email) {
        if (empty($email)) {
            return null;
        }

        $sql = "SELECT * FROM vehicules WHERE client = ? AND statut = 'Achat en cours' LIMIT 1";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            return $result->fetch_assoc();
        }

        return null;
    }

    /**
     * Récupère l'ID du véhicule à partir de son titre (libellé)
     * @param string $title Titre du véhicule
     * @return int|null ID du véhicule ou null si non trouvé
     */
    public function getVehiculeIdByTitle($title) {
        if (empty($title)) {
            return null;
        }

        // Récupérer le véhicule disponible ou sans statut avec ce libellé
        $sql = "SELECT id FROM vehicules WHERE libelle = ? AND (statut IS NULL OR statut = 'Disponible') LIMIT 1";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("s", $title);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            error_log("Véhicule trouvé avec libellé '$title', ID: " . $row['id'] . ", statut: Disponible");
            return $row['id'];
        }

        // Si aucun véhicule disponible n'est trouvé, essayer de récupérer n'importe quel véhicule avec ce libellé
        $sql = "SELECT id, statut FROM vehicules WHERE libelle = ? LIMIT 1";
        $stmt = $this->conn->prepare($sql);
        $stmt->bind_param("s", $title);
        $stmt->execute();
        $result = $stmt->get_result();

        if ($result->num_rows > 0) {
            $row = $result->fetch_assoc();
            error_log("Véhicule trouvé avec libellé '$title', ID: " . $row['id'] . ", statut: " . ($row['statut'] ?? 'NULL'));
            return $row['id'];
        }

        error_log("Aucun véhicule trouvé avec libellé '$title'");
        return null;
    }

    /**
     * Ajoute ou met à jour un véhicule en fonction de sa référence
     * @param array $data Données du véhicule
     * @return array Résultat de l'opération avec les clés 'success', 'action' et 'message'
     */
    public function addVehicule($data) {
        // Vérifier si la référence est fournie
        if (empty($data['reference'])) {
            return [
                'success' => false,
                'message' => 'La référence du véhicule est obligatoire.'
            ];
        }

        // Vérifier si le véhicule existe déjà
        $existingVehicule = $this->getVehiculeByReference($data['reference']);

        // Vérifier si le champ dateheureachat existe dans la table
        $checkDateHeureAchatSql = "SHOW COLUMNS FROM vehicules LIKE 'dateheureachat'";
        $checkDateHeureAchatResult = $this->conn->query($checkDateHeureAchatSql);
        $dateheureachatExists = $checkDateHeureAchatResult->num_rows > 0;

        // Initialiser dateheureachat si le champ existe
        if ($dateheureachatExists && !isset($data['dateheureachat'])) {
            $data['dateheureachat'] = null;
        }

        // Si le véhicule existe, le mettre à jour
        if ($existingVehicule) {
            try {
                // Mettre à jour le véhicule existant
                $result = $this->updateVehiculeByReference($data['reference'], $data);

                if ($result) {
                    return [
                        'success' => true,
                        'action' => 'update',
                        'message' => 'Véhicule mis à jour avec succès.'
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'Erreur lors de la mise à jour du véhicule: ' . $this->conn->error
                    ];
                }
            } catch (Exception $e) {
                return [
                    'success' => false,
                    'message' => 'Exception lors de la mise à jour du véhicule: ' . $e->getMessage()
                ];
            }
        } else {
            // Sinon, insérer un nouveau véhicule
            try {
                // Préparer la requête d'insertion
                if ($dateheureachatExists) {
                    $sql = "INSERT INTO vehicules (
                        reference, libelle, descriptif, annee, prix,
                        client, client_adresse, statut, rgltreference,
                        certificat, libelle_certificat, aidetransport, categorie, dateheureachat
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                    // Préparer les variables pour bind_param
                    $reference = $data['reference'];
                    $libelle = $data['libelle'];
                    $descriptif = $data['descriptif'];
                    $annee = $data['annee'];
                    $prix = $data['prix'];
                    $client = isset($data['client']) ? $data['client'] : '';
                    $client_adresse = isset($data['client_adresse']) ? $data['client_adresse'] : '';
                    $statut = isset($data['statut']) ? $data['statut'] : 'Disponible';
                    $rgltreference = isset($data['rgltreference']) ? $data['rgltreference'] : '';
                    $certificat = isset($data['certificat']) ? $data['certificat'] : 0;
                    $libelle_certificat = isset($data['libelle_certificat']) ? $data['libelle_certificat'] : '';
                    $aidetransport = isset($data['aidetransport']) ? $data['aidetransport'] : 0;
                    $categorie = isset($data['categorie']) ? $data['categorie'] : 'AUTRE VEHICULE';
                    $dateheureachat = $data['dateheureachat'];

                    $stmt = $this->conn->prepare($sql);
                    $stmt->bind_param("ssssdssssisiss",
                        $reference,
                        $libelle,
                        $descriptif,
                        $annee,
                        $prix,
                        $client,
                        $client_adresse,
                        $statut,
                        $rgltreference,
                        $certificat,
                        $libelle_certificat,
                        $aidetransport,
                        $categorie,
                        $dateheureachat
                    );
                } else {
                    $sql = "INSERT INTO vehicules (
                        reference, libelle, descriptif, annee, prix,
                        client, client_adresse, statut, rgltreference,
                        certificat, libelle_certificat, aidetransport, categorie
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

                    // Préparer les variables pour bind_param
                    $reference = $data['reference'];
                    $libelle = $data['libelle'];
                    $descriptif = $data['descriptif'];
                    $annee = $data['annee'];
                    $prix = $data['prix'];
                    $client = isset($data['client']) ? $data['client'] : '';
                    $client_adresse = isset($data['client_adresse']) ? $data['client_adresse'] : '';
                    $statut = isset($data['statut']) ? $data['statut'] : 'Disponible';
                    $rgltreference = isset($data['rgltreference']) ? $data['rgltreference'] : '';
                    $certificat = isset($data['certificat']) ? $data['certificat'] : 0;
                    $libelle_certificat = isset($data['libelle_certificat']) ? $data['libelle_certificat'] : '';
                    $aidetransport = isset($data['aidetransport']) ? $data['aidetransport'] : 0;
                    $categorie = isset($data['categorie']) ? $data['categorie'] : 'AUTRE VEHICULE';

                    $stmt = $this->conn->prepare($sql);
                    $stmt->bind_param("ssssdssssisss",
                        $reference,
                        $libelle,
                        $descriptif,
                        $annee,
                        $prix,
                        $client,
                        $client_adresse,
                        $statut,
                        $rgltreference,
                        $certificat,
                        $libelle_certificat,
                        $aidetransport,
                        $categorie
                    );
                }

                $result = $stmt->execute();

                if ($result) {
                    return [
                        'success' => true,
                        'action' => 'insert',
                        'message' => 'Véhicule ajouté avec succès.'
                    ];
                } else {
                    return [
                        'success' => false,
                        'message' => 'Erreur lors de l\'ajout du véhicule: ' . $this->conn->error
                    ];
                }
            } catch (Exception $e) {
                return [
                    'success' => false,
                    'message' => 'Exception lors de l\'ajout du véhicule: ' . $e->getMessage()
                ];
            }
        }
    }
}
?>
