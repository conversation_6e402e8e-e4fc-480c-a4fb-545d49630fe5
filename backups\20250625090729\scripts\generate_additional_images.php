<?php
// Script pour générer des images supplémentaires pour les tests de la galerie

// Charger la configuration de la base de données
require_once __DIR__ . '/../app/config/database.php';
require_once __DIR__ . '/../app/models/VehiculeModel.php';

// Répertoire où les images seront stockées
$targetDir = __DIR__ . '/../public/img/autres/';

// Vérifier si le répertoire existe, sinon le créer
if (!is_dir($targetDir)) {
    mkdir($targetDir, 0777, true);
}

// Récupérer tous les véhicules
$vehiculeModel = new VehiculeModel();
$vehicules = $vehiculeModel->getAllVehicules();

// Pour chaque véhicule, créer des images supplémentaires
foreach ($vehicules as $vehicule) {
    $reference = $vehicule['reference'];
    $searchRef = str_replace('_', ' ', $reference);
    
    // Vérifier si l'image principale existe
    $mainImagePath = $targetDir . $searchRef . '.png';
    if (!file_exists($mainImagePath)) {
        // Utiliser une image par défaut
        $sourceImage = __DIR__ . '/../public/img/autres/HALF TRACK.png';
        if (file_exists($sourceImage)) {
            copy($sourceImage, $mainImagePath);
            echo "Image principale créée pour " . $vehicule['libelle'] . " à " . $mainImagePath . "\n";
        }
    }
    
    // Créer 3 images supplémentaires avec des suffixes
    for ($i = 1; $i <= 3; $i++) {
        $additionalImagePath = $targetDir . $searchRef . '_' . $i . '.png';
        if (!file_exists($additionalImagePath)) {
            // Utiliser l'image principale comme source
            if (file_exists($mainImagePath)) {
                copy($mainImagePath, $additionalImagePath);
                echo "Image supplémentaire " . $i . " créée pour " . $vehicule['libelle'] . " à " . $additionalImagePath . "\n";
            }
        }
    }
}

echo "Génération des images supplémentaires terminée.\n";
?>
