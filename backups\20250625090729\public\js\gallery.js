// Variables globales pour la galerie
let currentImageIndex = 0;
let vehicleImages = [];

// Récupérer BASE_URL depuis la page ou utiliser une valeur par défaut
const BASE_URL = (typeof window.BASE_URL !== 'undefined') ? window.BASE_URL : '/';

// Fonction pour initialiser la galerie
function initGallery() {
    // Initialiser les éléments du modal
    const modal = document.getElementById('imageGalleryModal');
    const closeBtn = document.querySelector('.close');
    const prevBtn = document.getElementById('prevImage');
    const nextBtn = document.getElementById('nextImage');

    if (!modal || !closeBtn || !prevBtn || !nextBtn) {
        console.error('Erreur: Éléments du modal non trouvés');
        return;
    }

    // Fermer le modal quand on clique sur le bouton de fermeture
    closeBtn.addEventListener('click', function() {
        modal.style.display = 'none';
    });

    // Fermer le modal quand on clique en dehors de l'image
    window.addEventListener('click', function(event) {
        if (event.target === modal) {
            modal.style.display = 'none';
        }
    });

    // Navigation avec les touches du clavier
    window.addEventListener('keydown', function(event) {
        if (modal.style.display === 'block') {
            if (event.key === 'ArrowLeft') {
                navigateGallery(-1);
            } else if (event.key === 'ArrowRight') {
                navigateGallery(1);
            } else if (event.key === 'Escape') {
                modal.style.display = 'none';
            }
        }
    });

    // Événements pour les boutons de navigation
    prevBtn.addEventListener('click', function() {
        navigateGallery(-1);
    });

    nextBtn.addEventListener('click', function() {
        navigateGallery(1);
    });
}

// Fonction pour ouvrir la galerie
function openGallery(imgElement) {
    if (!imgElement) {
        console.error('Erreur: Élément image non fourni');
        return;
    }

    const reference = imgElement.getAttribute('data-reference');
    if (!reference) {
        console.error('Erreur: Référence non trouvée dans l\'attribut data-reference');
        return;
    }

    currentImageIndex = 0;

    // Réinitialiser les images
    vehicleImages = [];

    // Afficher un indicateur de chargement
    const modal = document.getElementById('imageGalleryModal');
    if (modal) {
        modal.style.display = 'block';

        const modalImg = document.getElementById('modalImage');
        if (modalImg) {
            // Sauvegarder l'image actuelle comme fallback
            modalImg.setAttribute('data-fallback', imgElement.src);

            // Afficher un message de chargement
            modalImg.style.opacity = '0.5';

            // Afficher le compteur comme "Chargement..."
            const counter = document.getElementById('imageCounter');
            if (counter) {
                counter.textContent = 'Chargement...';
            }
        }
    }

    // Appel AJAX pour récupérer les images du véhicule
    console.log(`Récupération des images pour la référence: ${reference}`);

    // Définir un timeout pour l'appel AJAX
    const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Timeout')), 8000); // Augmenter le timeout à 8 secondes
    });

    // Fonction pour traiter les erreurs et utiliser l'image de secours
    const handleError = (error) => {
        console.error('Erreur lors de la récupération des images:', error);

        // En cas d'erreur, utiliser l'image actuelle
        vehicleImages = [imgElement.src];

        // Afficher l'image
        displayImage(0);

        // Afficher un message d'erreur dans la console
        console.warn('Utilisation de l\'image de secours en raison d\'une erreur:', imgElement.src);
    };

    // Fonction pour traiter la réponse de l'API
    const processApiResponse = (data) => {
        console.log('Réponse API reçue:', data);

        // Vérifier si la réponse contient une erreur
        if (data && data.error) {
            console.error('Erreur API:', data.error);
            throw new Error(data.error);
        }

        // Extraire les images de la réponse
        let images = [];

        if (data && data.images && Array.isArray(data.images)) {
            // Format avec debug info
            images = data.images;
            if (data.debug) {
                console.log('Debug info:', data.debug);
            }
        } else if (Array.isArray(data)) {
            // Format simple (tableau d'URLs)
            images = data;
        } else if (data) {
            // Format inconnu, essayer de trouver des images
            console.warn('Format de réponse inconnu:', data);
            if (typeof data === 'object') {
                // Parcourir toutes les propriétés pour trouver un tableau
                for (const key in data) {
                    if (Array.isArray(data[key])) {
                        images = data[key];
                        console.log('Tableau trouvé dans la propriété:', key);
                        break;
                    }
                }
            }
        }

        console.log('Nombre d\'images trouvées:', images.length);

        // Si aucune image n'est trouvée, utiliser l'image actuelle
        if (!images || images.length === 0) {
            console.log('Aucune image trouvée, utilisation de l\'image actuelle:', imgElement.src);
            images = [imgElement.src];
        }

        // Mettre à jour la variable globale
        vehicleImages = images;

        // Précharger les images
        preloadImages(vehicleImages);

        // Afficher la première image
        displayImage(currentImageIndex);
    };

    // Essayer d'abord avec fetch
    Promise.race([
        fetch(`${BASE_URL}api/vehicule-images.php?reference=${encodeURIComponent(reference)}`),
        timeoutPromise
    ])
    .then(response => {
        if (!response.ok) {
            throw new Error(`Erreur HTTP: ${response.status}`);
        }
        return response.json();
    })
    .then(processApiResponse)
    .catch(error => {
        console.warn('Erreur avec fetch, tentative avec XMLHttpRequest:', error);

        // Si fetch échoue, essayer avec XMLHttpRequest
        const xhr = new XMLHttpRequest();
        xhr.open('GET', `${BASE_URL}api/vehicule-images.php?reference=${encodeURIComponent(reference)}`, true);
        xhr.timeout = 8000; // 8 secondes

        xhr.onload = function() {
            if (xhr.status >= 200 && xhr.status < 300) {
                try {
                    const data = JSON.parse(xhr.responseText);
                    processApiResponse(data);
                } catch (e) {
                    console.error('Erreur de parsing JSON:', e);
                    handleError(e);
                }
            } else {
                handleError(new Error(`Erreur HTTP: ${xhr.status}`));
            }
        };

        xhr.onerror = function() {
            handleError(new Error('Erreur réseau avec XMLHttpRequest'));
        };

        xhr.ontimeout = function() {
            handleError(new Error('Timeout avec XMLHttpRequest'));
        };

        xhr.send();
    });
}

// Fonction pour précharger les images
function preloadImages(urls) {
    urls.forEach(url => {
        const img = new Image();
        img.src = url;
    });
}

// Fonction pour naviguer dans la galerie
function navigateGallery(direction) {
    if (!Array.isArray(vehicleImages) || vehicleImages.length === 0) return;

    let newIndex = currentImageIndex + direction;

    if (newIndex < 0) {
        newIndex = vehicleImages.length - 1;
    } else if (newIndex >= vehicleImages.length) {
        newIndex = 0;
    }

    displayImage(newIndex);
}

// Fonction pour afficher une image dans le modal
function displayImage(index) {
    const modalImg = document.getElementById('modalImage');
    const counter = document.getElementById('imageCounter');

    if (!modalImg || !counter) {
        console.error('Erreur: Éléments du modal non trouvés');
        return;
    }

    // Restaurer l'opacité normale
    modalImg.style.opacity = '1';

    // Vérifier si des images sont disponibles
    if (!Array.isArray(vehicleImages) || vehicleImages.length === 0) {
        console.error('Erreur: Aucune image disponible à afficher');

        // Utiliser l'image de secours si disponible
        const fallbackSrc = modalImg.getAttribute('data-fallback');
        if (fallbackSrc) {
            modalImg.src = fallbackSrc;
            counter.textContent = '1 / 1';
            console.log('Utilisation de l\'image de secours');
            return;
        }

        // Si pas d'image de secours, utiliser l'image par défaut
        modalImg.src = BASE_URL + 'public/img/image_not_found.png';
        counter.textContent = '0 / 0';
        return;
    }

    // S'assurer que l'index est valide
    if (index < 0) index = vehicleImages.length - 1;
    if (index >= vehicleImages.length) index = 0;

    // Mettre à jour l'index courant
    currentImageIndex = index;

    console.log(`Affichage de l'image ${index + 1}/${vehicleImages.length}:`, vehicleImages[index]);

    // Définir la source de l'image avec gestion d'erreur
    modalImg.onerror = function() {
        console.error('Erreur de chargement de l\'image:', this.src);

        // Si c'est la seule image et qu'elle échoue, utiliser l'image de secours
        if (vehicleImages.length === 1) {
            const fallbackSrc = modalImg.getAttribute('data-fallback');
            if (fallbackSrc) {
                this.src = fallbackSrc;
                console.log('Utilisation de l\'image de secours après échec');
            } else {
                this.src = BASE_URL + 'public/img/image_not_found.png';
            }
        } else {
            // Si plusieurs images, essayer la suivante
            this.src = BASE_URL + 'public/img/image_not_found.png';

            // Supprimer l'image défectueuse du tableau
            vehicleImages.splice(currentImageIndex, 1);

            // Mettre à jour le compteur
            if (vehicleImages.length > 0) {
                // Ajuster l'index si nécessaire
                if (currentImageIndex >= vehicleImages.length) {
                    currentImageIndex = 0;
                }
                counter.textContent = `${currentImageIndex + 1} / ${vehicleImages.length}`;
            } else {
                counter.textContent = '0 / 0';
            }
        }
    };

    // Définir la source de l'image
    modalImg.src = vehicleImages[index];

    // Mettre à jour le compteur
    counter.textContent = `${index + 1} / ${vehicleImages.length}`;
}

// Initialiser la galerie lorsque le DOM est chargé
document.addEventListener('DOMContentLoaded', initGallery);
