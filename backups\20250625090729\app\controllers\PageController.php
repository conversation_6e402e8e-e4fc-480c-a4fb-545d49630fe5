<?php

// Charger le modèle VehiculeModel
require_once __DIR__ . '/../models/VehiculeModel.php';

class PageController {

    private $vehiculeModel;

    public function __construct() {
        // Initialisation du modèle
        $this->vehiculeModel = new VehiculeModel();
    }

    /**
     * Méthode pour afficher la liste paginée des véhicules
     */
    public function showVehiculesList() {
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $perPage = 20;

        // Récupérer les véhicules avec pagination
        $vehicules = $this->vehiculeModel->getVehiculesWithPagination($page, $perPage);
        $total = $this->vehiculeModel->getTotalVehicules();
        $totalPages = ceil($total / $perPage);

        // Préparer les données pour la vue
        $data = [
            'title' => 'Liste des Véhicules - Jeep Dodge GMC',
            'description' => 'Liste complète des véhicules',
            'vehicules' => $vehicules,
            'currentPage' => $page,
            'totalPages' => $totalPages,
            'total' => $total
        ];

        // Charger la vue de liste des véhicules
        $this->view('pages/vehicules-liste', $data);
    }

    /**
     * Affiche le formulaire de modification d'un véhicule
     */
    public function showEditVehicule() {
        $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
        $vehicule = $this->vehiculeModel->getVehiculeById($id);

        if (!$vehicule) {
            echo "Le véhicule avec l'ID " . $id . " n'existe pas dans la base de données.";
            exit;
        }

        // Récupérer les images du véhicule
        $images = $this->vehiculeModel->getVehiculeImages($vehicule['reference']);

        $data = [
            'title' => 'Modifier le véhicule - ' . $vehicule['reference'],
            'description' => 'Modification des informations du véhicule',
            'vehicule' => $vehicule,
            'images' => $images
        ];

        $this->view('pages/modifier-vehicule', $data);
    }

    /**
     * Traite la soumission du formulaire de modification
     */
    public function updateVehicule() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: ' . BASE_URL . 'vehicules-liste');
            exit;
        }

        $id = isset($_POST['id']) ? (int)$_POST['id'] : 0;
        $client = $_POST['client'] ?? '';
        $statut = $_POST['statut'] ?? '';

        // Validation: un véhicule avec statut "Disponible" ne peut pas avoir de client renseigné
        if ($statut === 'Disponible' && !empty($client)) {
            header('Location: ' . BASE_URL . 'modifier-vehicule?id=' . $id . '&error=invalid_statut_client&message=' . urlencode('Un véhicule avec statut "Disponible" ne peut pas avoir de client renseigné.'));
            exit;
        }

        // Validation: un véhicule avec statut "Vendu" doit obligatoirement avoir un client renseigné
        if ($statut === 'Vendu' && empty($client)) {
            header('Location: ' . BASE_URL . 'modifier-vehicule?id=' . $id . '&error=invalid_statut_client&message=' . urlencode('Un véhicule avec statut "Vendu" doit obligatoirement avoir un client renseigné.'));
            exit;
        }

        $data = [
            'reference' => $_POST['reference'] ?? '',
            'libelle' => $_POST['libelle'] ?? '',
            'descriptif' => $_POST['descriptif'] ?? '',
            'annee' => $_POST['annee'] ?? '',
            'prix' => (float)($_POST['prix'] ?? 0),
            'client' => $client,
            'client_adresse' => $_POST['client_adresse'] ?? '',
            'statut' => $statut,
            'rgltreference' => $_POST['rgltreference'] ?? '',
            'certificat' => isset($_POST['certificat']) ? 1 : 0,
            'libelle_certificat' => $_POST['libelle_certificat'] ?? '',
            'aidetransport' => isset($_POST['aidetransport']) ? 1 : 0,
            'dateheureachat' => isset($_POST['dateheureachat']) ? $_POST['dateheureachat'] : null
        ];

        if ($this->vehiculeModel->updateVehicule($id, $data)) {
            header('Location: ' . BASE_URL . 'vehicules-liste?success=update');
        } else {
            header('Location: ' . BASE_URL . 'modifier-vehicule?id=' . $id . '&error=update_failed');
        }
        exit;
    }

    /**
     * Affiche le formulaire d'ajout d'un véhicule
     */
    public function showAddVehicule() {
        $data = [
            'title' => 'Ajouter un véhicule - Jeep Dodge GMC',
            'description' => 'Ajouter un nouveau véhicule'
        ];

        $this->view('pages/ajouter-vehicule', $data);
    }

    /**
     * Traite la soumission du formulaire d'ajout de véhicule
     */
    public function addVehicule() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: ' . BASE_URL . 'vehicules-liste');
            exit;
        }

        $client = $_POST['client'] ?? '';
        $statut = $_POST['statut'] ?? 'Disponible';
        $client_adresse = $_POST['client_adresse'] ?? '';

        // Validation: un véhicule avec statut "Disponible" ne peut pas avoir de client renseigné
        if ($statut === 'Disponible' && !empty($client)) {
            header('Location: ' . BASE_URL . 'ajouter-vehicule?error=invalid_statut_client&message=' . urlencode('Un véhicule avec statut "Disponible" ne peut pas avoir de client renseigné.'));
            exit;
        }

        // Validation: un véhicule avec statut "Vendu" doit obligatoirement avoir un client renseigné
        if ($statut === 'Vendu' && empty($client)) {
            header('Location: ' . BASE_URL . 'ajouter-vehicule?error=invalid_statut_client&message=' . urlencode('Un véhicule avec statut "Vendu" doit obligatoirement avoir un client renseigné.'));
            exit;
        }

        $data = [
            'reference' => $_POST['reference'] ?? '',
            'libelle' => $_POST['libelle'] ?? '',
            'descriptif' => $_POST['descriptif'] ?? '',
            'annee' => $_POST['annee'] ?? '',
            'prix' => (float)($_POST['prix'] ?? 0),
            'client' => $client,
            'client_adresse' => $client_adresse,
            'statut' => $statut,
            'rgltreference' => '',
            'certificat' => isset($_POST['certificat']) ? 1 : 0,
            'libelle_certificat' => $_POST['libelle_certificat'] ?? '',
            'aidetransport' => isset($_POST['aidetransport']) ? 1 : 0,
            'categorie' => 'autres'
        ];

        $result = $this->vehiculeModel->addVehicule($data);

        if ($result['success']) {
            header('Location: ' . BASE_URL . 'vehicules-liste?success=add');
        } else {
            header('Location: ' . BASE_URL . 'ajouter-vehicule?error=add_failed&message=' . urlencode($result['message']));
        }
        exit;
    }

    /**
     * Traite la suppression d'un véhicule
     */
    public function deleteVehicule() {
        $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

        if ($this->vehiculeModel->deleteVehicule($id)) {
            header('Location: ' . BASE_URL . 'vehicules-liste?success=delete');
        } else {
            header('Location: ' . BASE_URL . 'vehicules-liste?error=delete_failed');
        }
        exit;
    }

    /**
     * Supprime une image d'un véhicule
     */
    public function deleteImage() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('HTTP/1.1 405 Method Not Allowed');
            exit;
        }

        $imagePath = $_POST['image_path'] ?? '';
        $id = $_POST['vehicule_id'] ?? 0;

        if (empty($imagePath) || !$id) {
            header('Location: ' . BASE_URL . 'modifier-vehicule?id=' . $id . '&error=invalid_image');
            exit;
        }

        // Construire le chemin complet
        $fullPath = dirname(__DIR__, 2) . '/public/' . ltrim($imagePath, '/');

        if (file_exists($fullPath) && unlink($fullPath)) {
            header('Location: ' . BASE_URL . 'modifier-vehicule?id=' . $id . '&success=image_deleted');
        } else {
            header('Location: ' . BASE_URL . 'modifier-vehicule?id=' . $id . '&error=delete_failed');
        }
        exit;
    }

    /**
     * Ajoute une nouvelle image à un véhicule
     */
    public function addImage() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('HTTP/1.1 405 Method Not Allowed');
            exit;
        }

        $id = $_POST['vehicule_id'] ?? 0;
        $vehicule = $this->vehiculeModel->getVehiculeById($id);

        if (!$vehicule) {
            header('Location: ' . BASE_URL . 'vehicules-liste?error=vehicule_not_found');
            exit;
        }

        if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
            header('Location: ' . BASE_URL . 'modifier-vehicule?id=' . $id . '&error=upload_failed');
            exit;
        }

        // Correction du chemin de destination (utilisation de dirname(__DIR__, 2) pour remonter au dossier racine)
        $uploadDir = dirname(__DIR__, 2) . '/public/img/';
        $fileInfo = pathinfo($_FILES['image']['name']);
        $extension = strtolower($fileInfo['extension']);

        // Vérifier l'extension
        if (!in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])) {
            header('Location: ' . BASE_URL . 'modifier-vehicule?id=' . $id . '&error=invalid_format');
            exit;
        }

        // Générer un nom de fichier cohérent avec la galerie
        $reference = strtoupper($vehicule['reference']);

        // Récupérer la liste des images existantes pour déterminer le prochain numéro
        $existingImages = $this->vehiculeModel->getVehiculeImages($vehicule['reference']);
        $counter = 1;

        if (!empty($existingImages)) {
            // Extraire les numéros des fichiers existants
            $numbers = [];
            foreach ($existingImages as $img) {
                if (preg_match('/\((\d+)\)/', $img, $matches)) {
                    $numbers[] = (int)$matches[1];
                }
            }

            if (!empty($numbers)) {
                $counter = max($numbers) + 1;
            }
        }

        $newFilename = $reference . " ({$counter})." . $extension;

        if (move_uploaded_file($_FILES['image']['tmp_name'], $uploadDir . $newFilename)) {
            header('Location: ' . BASE_URL . 'modifier-vehicule?id=' . $id . '&success=image_added');
        } else {
            header('Location: ' . BASE_URL . 'modifier-vehicule?id=' . $id . '&error=save_failed');
        }
        exit;
    }

    /**
     * Génère le bon de commande en PDF et le sauvegarde dans le répertoire private
     */
    public function generateBonCommande() {
        $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
        $vehicule = $this->vehiculeModel->getVehiculeById($id);

        if (!$vehicule) {
            header('Location: ' . BASE_URL . 'vehicules-liste?error=vehicule_not_found');
            exit;
        }

        // Vérifier si TCPDF est disponible
        if (!file_exists(dirname(__DIR__, 2) . '/vendor/tecnickcom/tcpdf/tcpdf.php')) {
            die('La bibliothèque TCPDF n\'est pas disponible. Veuillez l\'installer via Composer.');
        }

        require_once dirname(__DIR__, 2) . '/vendor/tecnickcom/tcpdf/tcpdf.php';

        // Création d'une nouvelle instance de TCPDF
        $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

        // Informations du document
        $pdf->SetCreator(COMPANY_NAME);
        $pdf->SetAuthor(COMPANY_NAME);
        $pdf->SetTitle('Bon de commande - ' . $vehicule['reference']);

        // Supprimer les en-têtes et pieds de page par défaut
        $pdf->setPrintHeader(false);
        $pdf->setPrintFooter(false);

        // Marges
        $pdf->SetMargins(15, 15, 15);

        // Ajout d'une page
        $pdf->AddPage();

        // Définir des couleurs personnalisées
        $headerColor = array(0, 61, 121); // Bleu foncé
        $accentColor = array(200, 30, 30); // Rouge
        $textColor = array(50, 50, 50); // Gris foncé
        $lightGray = array(240, 240, 240); // Gris clair pour les fonds

        // En-tête
        $pdf->SetFillColor($headerColor[0], $headerColor[1], $headerColor[2]);
        $pdf->Rect(0, 0, $pdf->getPageWidth(), 40, 'F');

        // Titre du document
        $pdf->SetY(15);
        $pdf->SetTextColor(255, 255, 255);
        $pdf->SetFont('helvetica', 'B', 24);
        $pdf->Cell(0, 10, 'BON DE COMMANDE', 0, 1, 'C');

        // Informations de l'entreprise
        $pdf->SetY(45);
        $pdf->SetTextColor($headerColor[0], $headerColor[1], $headerColor[2]);
        $pdf->SetFont('helvetica', 'B', 14);
        $pdf->Cell(0, 10, COMPANY_NAME, 0, 1, 'L');

        $pdf->SetTextColor($textColor[0], $textColor[1], $textColor[2]);
        $pdf->SetFont('helvetica', '', 10);
        $pdf->Cell(0, 6, COMPANY_ADDRESS, 0, 1, 'L');
        $pdf->Cell(0, 6, COMPANY_CITY, 0, 1, 'L');
        $pdf->Cell(0, 6, 'Tél: ' . COMPANY_PHONE . ' - Email: ' . COMPANY_EMAIL, 0, 1, 'L');
        $pdf->Cell(0, 6, COMPANY_WEBSITE, 0, 1, 'L');
        $pdf->Cell(0, 6, COMPANY_SIRET, 0, 1, 'L');

        // Slogan
        $pdf->SetY(45);
        $pdf->SetX(120);
        $pdf->SetFont('helvetica', 'I', 10);
        $pdf->SetTextColor($accentColor[0], $accentColor[1], $accentColor[2]);
        $pdf->MultiCell(75, 6, COMPANY_SLOGAN, 0, 'R');

        // Référence et date
        $pdf->SetY(85);
        $pdf->SetFillColor($lightGray[0], $lightGray[1], $lightGray[2]);
        $pdf->SetTextColor($headerColor[0], $headerColor[1], $headerColor[2]);
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 10, 'BON DE COMMANDE N°: ' . $vehicule['reference'] . ' - ' . date('d/m/Y'), 0, 1, 'C', 1);

        $pdf->Ln(10);

        // Informations du client
        $pdf->SetTextColor($headerColor[0], $headerColor[1], $headerColor[2]);
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 10, 'INFORMATIONS CLIENT', 0, 1, 'L');

        $pdf->SetDrawColor(200, 200, 200);
        $pdf->SetTextColor($textColor[0], $textColor[1], $textColor[2]);

        // Tableau des informations client
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(40, 8, 'Client:', 1, 0, 'L', 0);
        $pdf->SetFont('helvetica', '', 10);
        $pdf->Cell(145, 8, $vehicule['client'], 1, 1, 'L', 0);

        if (!empty($vehicule['client_adresse'])) {
            $pdf->SetFont('helvetica', 'B', 10);
            $pdf->Cell(40, 8, 'Adresse:', 1, 0, 'L', 0);
            $pdf->SetFont('helvetica', '', 10);
            $pdf->Cell(145, 8, $vehicule['client_adresse'], 1, 1, 'L', 0);
        }

        $pdf->Ln(10);

        // Détails du véhicule
        $pdf->SetTextColor($headerColor[0], $headerColor[1], $headerColor[2]);
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 10, 'DÉTAILS DU VÉHICULE', 0, 1, 'L');

        // En-tête du tableau
        $pdf->SetFillColor($headerColor[0], $headerColor[1], $headerColor[2]);
        $pdf->SetTextColor(255, 255, 255);
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(95, 8, 'Description', 1, 0, 'C', 1);
        $pdf->Cell(30, 8, 'Année', 1, 0, 'C', 1);
        $pdf->Cell(30, 8, 'Certificat', 1, 0, 'C', 1);
        $pdf->Cell(30, 8, 'Prix', 1, 1, 'C', 1);

        // Contenu du tableau
        $pdf->SetTextColor($textColor[0], $textColor[1], $textColor[2]);
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(95, 8, $vehicule['libelle'], 1, 0, 'L', 0);
        $pdf->SetFont('helvetica', '', 10);
        $pdf->Cell(30, 8, $vehicule['annee'] ?? '-', 1, 0, 'C', 0);
        $pdf->Cell(30, 8, $vehicule['certificat'] ? 'Oui' : 'Non', 1, 0, 'C', 0);

        // Calculer le prix total en tenant compte du certificat
        $prixTotal = $vehicule['prix'];
        if ($vehicule['certificat']) {
            $prixTotal += 200; // Ajouter 200€ pour le certificat
        }

        $pdf->SetTextColor($accentColor[0], $accentColor[1], $accentColor[2]);
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(30, 8, number_format($prixTotal, 2, ',', ' ') . ' €', 1, 1, 'R', 0);

        // Description détaillée
        $pdf->SetTextColor($textColor[0], $textColor[1], $textColor[2]);
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(40, 8, 'Description:', 1, 0, 'L', 0);
        $pdf->SetFont('helvetica', '', 10);
        $pdf->Cell(145, 8, $vehicule['descriptif'], 1, 1, 'L', 0);

        if ($vehicule['certificat'] && !empty($vehicule['libelle_certificat'])) {
            $pdf->SetFont('helvetica', 'B', 10);
            $pdf->Cell(40, 8, 'Certificat:', 1, 0, 'L', 0);
            $pdf->SetFont('helvetica', '', 10);
            $pdf->Cell(145, 8, $vehicule['libelle_certificat'], 1, 1, 'L', 0);
        }

        // Afficher l'information d'aide au transport si demandée
        if (isset($vehicule['aidetransport']) && $vehicule['aidetransport']) {
            $pdf->SetFont('helvetica', 'B', 10);
            $pdf->Cell(40, 8, 'Aide au transport:', 1, 0, 'L', 0);
            $pdf->SetFont('helvetica', '', 10);
            $pdf->Cell(145, 8, 'Demandée', 1, 1, 'L', 0);
        }

        // Informations de règlement
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(40, 8, 'Règlement:', 1, 0, 'L', 0);
        $pdf->SetFont('helvetica', '', 10);

        // Afficher les informations de règlement disponibles
        $infoReglement = [];

        if (isset($vehicule['rgltdateheure']) && !empty($vehicule['rgltdateheure'])) {
            $infoReglement[] = 'Date/Heure: ' . $vehicule['rgltdateheure'];
        }

        if (isset($vehicule['rgltreference']) && !empty($vehicule['rgltreference'])) {
            $infoReglement[] = 'Référence: ' . $vehicule['rgltreference'];
        }

        // Si aucune information de règlement n'est disponible, afficher le statut
        if (empty($infoReglement) && isset($vehicule['statut']) && !empty($vehicule['statut'])) {
            $infoReglement[] = 'Statut: ' . $vehicule['statut'];
        }

        // Si aucune information n'est disponible
        if (empty($infoReglement)) {
            $infoReglementText = 'Aucune information de règlement disponible';
        } else {
            $infoReglementText = implode(' - ', $infoReglement);
        }

        $pdf->Cell(145, 8, $infoReglementText, 1, 1, 'L', 0);

        // Récapitulatif financier
        $pdf->Ln(10);
        $pdf->SetTextColor($headerColor[0], $headerColor[1], $headerColor[2]);
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(0, 10, 'RÉCAPITULATIF', 0, 1, 'L');

        // Tableau récapitulatif
        // Calculer le prix total en tenant compte du certificat
        $prixTotal = $vehicule['prix'];
        if ($vehicule['certificat']) {
            $prixTotal += 200; // Ajouter 200€ pour le certificat
        }

        // Calculer le prix HT et la TVA
        $prixHT = $prixTotal / 1.2;
        $tva = $prixTotal - $prixHT;

        $pdf->SetFillColor($lightGray[0], $lightGray[1], $lightGray[2]);
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(155, 8, 'Total HT:', 1, 0, 'R', 1);
        $pdf->SetTextColor($accentColor[0], $accentColor[1], $accentColor[2]);
        $pdf->Cell(30, 8, number_format($prixHT, 2, ',', ' ') . ' €', 1, 1, 'R', 1);

        $pdf->SetTextColor($headerColor[0], $headerColor[1], $headerColor[2]);
        $pdf->Cell(155, 8, 'TVA (20%):', 1, 0, 'R', 0);
        $pdf->SetTextColor($accentColor[0], $accentColor[1], $accentColor[2]);
        $pdf->Cell(30, 8, number_format($tva, 2, ',', ' ') . ' €', 1, 1, 'R', 0);

        $pdf->SetTextColor($headerColor[0], $headerColor[1], $headerColor[2]);
        $pdf->SetFont('helvetica', 'B', 12);
        $pdf->Cell(155, 10, 'TOTAL TTC:', 1, 0, 'R', 1);
        $pdf->SetTextColor($accentColor[0], $accentColor[1], $accentColor[2]);
        $pdf->Cell(30, 10, number_format($prixTotal, 2, ',', ' ') . ' €', 1, 1, 'R', 1);

        // Date et signatures
        $pdf->Ln(20);
        $pdf->SetTextColor($textColor[0], $textColor[1], $textColor[2]);
        $pdf->SetFont('helvetica', '', 10);
        $pdf->Cell(0, 10, 'Fait à ' . COMPANY_CITY . ', le ' . date('d/m/Y'), 0, 1, 'R');

        $pdf->Ln(5);
        $pdf->SetDrawColor(200, 200, 200);

        // Zone de signatures
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(92, 10, 'Signature du vendeur:', 'T', 0, 'C');
        $pdf->Cell(5, 10, '', 0, 0);
        $pdf->Cell(92, 10, 'Signature du client:', 'T', 1, 'C');
        $pdf->Ln(25);

        // Conditions générales
        $pdf->SetFillColor($lightGray[0], $lightGray[1], $lightGray[2]);
        $pdf->SetTextColor($headerColor[0], $headerColor[1], $headerColor[2]);
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(0, 8, 'CONDITIONS GÉNÉRALES DE VENTE', 0, 1, 'L', 1);

        $pdf->SetTextColor($textColor[0], $textColor[1], $textColor[2]);
        $pdf->SetFont('helvetica', '', 8);
        $pdf->MultiCell(0, 5, "1. Le véhicule est vendu en l'état.\n2. Le prix indiqué est ferme et définitif.\n3. La vente est effective après paiement intégral du prix.\n4. Le transfert de propriété s'effectue après encaissement complet du règlement.\n5. Aucun retour ni échange n'est possible après la vente.", 0, 'L');

        // Pied de page
        $pdf->SetY(-30);
        $pdf->SetFillColor($headerColor[0], $headerColor[1], $headerColor[2]);
        $pdf->Rect(0, $pdf->GetY(), $pdf->getPageWidth(), 30, 'F');

        $pdf->SetY(-25);
        $pdf->SetTextColor(255, 255, 255);
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(0, 6, COMPANY_NAME, 0, 1, 'C');

        $pdf->SetFont('helvetica', '', 8);
        $pdf->Cell(0, 6, COMPANY_ADDRESS . ' - ' . COMPANY_CITY . ' - ' . COMPANY_PHONE, 0, 1, 'C');
        $pdf->Cell(0, 6, COMPANY_WEBSITE . ' - ' . COMPANY_EMAIL . ' - ' . COMPANY_SIRET, 0, 1, 'C');

        // Créer le répertoire private s'il n'existe pas
        $privateDir = dirname(__DIR__, 2) . '/private/';
        if (!is_dir($privateDir)) {
            mkdir($privateDir, 0755, true);
        }

        // Générer un nom de fichier unique avec la date
        $filename = 'bon_commande_' . $vehicule['reference'] . '_' . date('Ymd_His') . '.pdf';
        $filePath = $privateDir . $filename;

        // Sauvegarder le PDF dans le répertoire private
        $pdf->Output($filePath, 'F');

        // Envoyer le bon de commande par email
        $emailSent = false;
        if (!empty($vehicule['client']) && filter_var($vehicule['client'], FILTER_VALIDATE_EMAIL)) {
            $emailSent = $this->sendBonCommandeByEmail($vehicule['client'], $filePath, $vehicule['reference']);

            // Stocker l'information dans la session pour afficher un message de confirmation
            if (!session_id()) {
                session_start();
            }
            $_SESSION['bon_commande_email_sent'] = $emailSent;
            $_SESSION['bon_commande_email'] = $vehicule['client'];
        }

        // Afficher le PDF dans le navigateur
        $pdf->Output('bon_commande_' . $vehicule['reference'] . '.pdf', 'I');
        exit;
    }

    /**
     * Envoie le bon de commande par email au client
     * @param string $clientEmail L'adresse email du client
     * @param string $pdfPath Le chemin vers le fichier PDF
     * @param string $reference La référence du véhicule
     * @return bool True si l'email a été envoyé avec succès, False sinon
     */
    private function sendBonCommandeByEmail($clientEmail, $pdfPath, $reference) {
        // Vérifier que le fichier existe
        if (!file_exists($pdfPath)) {
            return false;
        }

        // Générer un identifiant unique pour la frontière du message
        $boundary = md5(time());

        // En-têtes de l'email
        $headers = 'From: ' . MAIL_FROM_NAME . ' <' . MAIL_FROM . ">\r\n";
        $headers .= "MIME-Version: 1.0\r\n";
        $headers .= "Content-Type: multipart/mixed; boundary=\"$boundary\"\r\n";

        // Sujet de l'email
        $subject = MAIL_SUBJECT_BON_COMMANDE . ' - ' . $reference;

        // Corps du message
        $message = "--$boundary\r\n";
        $message .= "Content-Type: text/plain; charset=UTF-8\r\n";
        $message .= "Content-Transfer-Encoding: 8bit\r\n\r\n";
        $message .= MAIL_BODY_BON_COMMANDE . "\r\n";

        // Pièce jointe
        $attachment = file_get_contents($pdfPath);
        $attachment = chunk_split(base64_encode($attachment));

        $message .= "--$boundary\r\n";
        $message .= "Content-Type: application/pdf; name=\"bon_commande_$reference.pdf\"\r\n";
        $message .= "Content-Transfer-Encoding: base64\r\n";
        $message .= "Content-Disposition: attachment; filename=\"bon_commande_$reference.pdf\"\r\n\r\n";
        $message .= $attachment . "\r\n";
        $message .= "--$boundary--";

        // Envoyer l'email au client
        $clientResult = mail($clientEmail, $subject, $message, $headers);

        // Envoyer l'email à <EMAIL>
        $contactEmail = '<EMAIL>';
        $contactResult = mail($contactEmail, $subject, $message, $headers);

        return $clientResult && $contactResult;
    }

    /**
     * Méthode pour afficher la page "autres".
     * Récupère les véhicules en fonction des filtres appliqués avec pagination.
     */
    public function show() {
        // Récupérer les paramètres de filtrage et de pagination
        $search = isset($_GET['search']) ? $_GET['search'] : null;
        $annee = isset($_GET['annee']) ? $_GET['annee'] : null;
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $perPage = VEHICLES_PER_PAGE; // Nombre de véhicules par page défini dans config.php

        // Récupérer le paramètre pour masquer les véhicules vendus
        // Par défaut, la case est cochée (hideVendus = true)
        $hideVendus = !isset($_GET['showVendus']);

        // S'assurer que la page est au moins 1
        if ($page < 1) {
            $page = 1;
        }

        // Récupérer les véhicules filtrés avec pagination
        $vehicules = $this->vehiculeModel->getVehiculesWithFiltersAndPagination($page, $perPage, null, $search, $annee, $hideVendus);

        // Compter le nombre total de véhicules avec les filtres appliqués
        $totalVehicules = $this->vehiculeModel->getTotalVehiculesWithFilters(null, $search, $annee, $hideVendus);

        // Calculer le nombre total de pages
        $totalPages = ceil($totalVehicules / $perPage);

        // Récupérer tous les modèles disponibles pour le filtre
        $models = $this->vehiculeModel->getModels();

        // Préparer les données pour la vue
        $data = [
            'title' => 'Véhicules - Jeep Dodge GMC',
            'description' => 'Spécialiste pièces détachées et véhicules américains WWII',
            'vehicules' => $vehicules,
            'models' => $models,
            'current_search' => $search,
            'current_year' => $annee,
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_vehicules' => $totalVehicules,
            'per_page' => $perPage,
            'hide_vendus' => $hideVendus
        ];

        // Charger la vue correspondante
        $this->view('pages/autres', $data);
    }

    /**
     * Méthode pour afficher la page d'attente.
     */
    public function showAttente() {
        // Préparer les données pour la vue
        $data = [
            'title' => 'Bientôt disponible - Jeep Dodge GMC',
            'description' => 'La liste des véhicules sera disponible le 8 mai 2025'
        ];

        // Charger la vue d'attente
        $this->view('pages/attente', $data);
    }

    /**
     * Affiche le formulaire d'importation de véhicules
     */
    public function showImportVehicules() {
        $data = [
            'title' => 'Importation de véhicules - Jeep Dodge GMC',
            'description' => 'Importer des véhicules depuis un fichier CSV'
        ];

        $this->view('pages/import-vehicules', $data);
    }

    /**
     * Traite l'importation de véhicules depuis un texte CSV
     */
    public function importVehiculesFromText() {
        // Activer la journalisation des erreurs
        ini_set('display_errors', 1);
        error_reporting(E_ALL);

        // Journaliser le début de l'importation
        error_log("Début de l'importation CSV des véhicules depuis un texte");

        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            header('Location: ' . BASE_URL . 'import-vehicules');
            exit;
        }

        // Vérifier si des données CSV ont été soumises
        if (!isset($_POST['csv_data']) || empty($_POST['csv_data'])) {
            $data = [
                'title' => 'Importation de véhicules - Jeep Dodge GMC',
                'description' => 'Importer des véhicules depuis un fichier CSV',
                'error' => true,
                'message' => 'Aucune donnée CSV n\'a été fournie.'
            ];

            $this->view('pages/import-vehicules', $data);
            return;
        }

        // Récupérer les paramètres
        $csvData = $_POST['csv_data'];
        $delimiter = $_POST['delimiter'] ?? ';';

        // Gérer le cas spécial de la tabulation
        if ($delimiter === '\t') {
            $delimiter = "\t";
        }
        error_log("Délimiteur utilisé: " . ($delimiter === "\t" ? "tabulation" : $delimiter));

        $skipHeader = isset($_POST['skip_header']) && $_POST['skip_header'] == '1';
        error_log("Ignorer l'en-tête: " . ($skipHeader ? "Oui" : "Non"));

        // Statistiques d'importation
        $stats = [
            'total' => 0,
            'inserted' => 0,
            'updated' => 0,
            'errors' => 0,
            'skipped' => 0
        ];

        // Erreurs détaillées
        $errors = [];

        // Diviser le texte en lignes
        $lines = explode("\n", $csvData);

        // Ignorer l'en-tête si demandé
        if ($skipHeader && count($lines) > 0) {
            array_shift($lines);
            $stats['skipped']++;
        }

        // Traiter chaque ligne
        $lineNumber = $skipHeader ? 2 : 1;

        foreach ($lines as $line) {
            $line = trim($line);

            // Ignorer les lignes vides
            if (empty($line)) {
                continue;
            }

            $stats['total']++;
            error_log("Traitement de la ligne $lineNumber: " . $line);

            // Diviser la ligne en champs
            $row = str_getcsv($line, $delimiter);

            // Vérifier si la ligne a suffisamment de colonnes
            if (count($row) < 7) {
                $errorMsg = "Erreur à la ligne $lineNumber: Nombre de colonnes insuffisant (" . count($row) . " colonnes trouvées, 7 attendues).";
                error_log($errorMsg);
                $errors[] = $errorMsg;
                $stats['errors']++;
                $lineNumber++;
                continue;
            }

            // Préparer les données du véhicule
            // Concaténer la colonne 3 (descriptif) et la colonne 5 (anciennement année) pour le descriptif
            $descriptif = trim($row[2]);
            $colonne5 = trim($row[4]);

            // Ajouter un espace entre les deux valeurs si nécessaire
            if (!empty($descriptif) && !empty($colonne5)) {
                $descriptifComplet = $descriptif . ' ' . $colonne5;
            } else {
                $descriptifComplet = $descriptif . $colonne5; // Si l'une des valeurs est vide, pas besoin d'espace
            }

            $vehiculeData = [
                'reference' => trim($row[0]),
                'libelle' => trim($row[1]),
                'descriptif' => $descriptifComplet,
                'libelle_certificat' => trim($row[3]),
                'annee' => '', // L'année n'est plus alimentée par la colonne 5
                'certificat' => (strtolower(trim($row[5])) === 'n') ? 0 : 1,
                'prix' => floatval(str_replace(',', '.', trim($row[6]))),
                'statut' => 'Disponible',
                'categorie' => 'AUTRE VEHICULE',
                'client' => '',
                'client_adresse' => '',
                'rgltreference' => '',
                'dateheureachat' => null
            ];

            error_log("Descriptif concaténé: '$descriptifComplet' (colonne 3: '$descriptif', colonne 5: '$colonne5')");

            // Vérifier si la référence est vide
            if (empty($vehiculeData['reference'])) {
                $errors[] = "Erreur à la ligne $lineNumber: Référence vide.";
                $stats['errors']++;
                $lineNumber++;
                continue;
            }

            // Ajouter ou mettre à jour le véhicule
            try {
                error_log("Tentative d'ajout/mise à jour du véhicule: " . json_encode($vehiculeData));
                $result = $this->vehiculeModel->addVehicule($vehiculeData);

                if ($result['success']) {
                    if ($result['action'] === 'insert') {
                        $stats['inserted']++;
                        error_log("Véhicule inséré avec succès: " . $vehiculeData['reference']);
                    } else {
                        $stats['updated']++;
                        error_log("Véhicule mis à jour avec succès: " . $vehiculeData['reference']);
                    }
                } else {
                    $errorMsg = "Erreur à la ligne $lineNumber: {$result['message']}";
                    $errors[] = $errorMsg;
                    $stats['errors']++;
                    error_log($errorMsg);
                }
            } catch (Exception $e) {
                $errorMsg = "Exception à la ligne $lineNumber: " . $e->getMessage();
                $errors[] = $errorMsg;
                $stats['errors']++;
                error_log($errorMsg);
            }

            $lineNumber++;
        }

        // Préparer les données pour la vue
        $data = [
            'title' => 'Importation de véhicules - Jeep Dodge GMC',
            'description' => 'Importer des véhicules depuis un fichier CSV',
            'success' => $stats['errors'] === 0,
            'message' => $stats['errors'] === 0
                ? 'Importation réussie.'
                : 'L\'importation a rencontré des erreurs.',
            'stats' => $stats,
            'errors' => $errors
        ];

        $this->view('pages/import-vehicules', $data);
    }

    /**
     * Méthode helper pour charger une vue.
     * @param string $view Le chemin de la vue (sans .php) depuis le dossier views.
     * @param array $data Les données à passer à la vue.
     */
    public function view($view, $data = []) {
        // Construire le chemin complet vers la vue
        $viewPath = __DIR__ . '/../views/' . $view . '.php';

        // Vérifier si le fichier de vue existe
        if (file_exists($viewPath)) {
            // Extraire les données pour les rendre disponibles dans la vue
            extract($data);

            // Inclure la vue
            require_once $viewPath;
        } else {
            // Gérer l'erreur si la vue n'existe pas
            die('La vue ' . $view . ' n\'existe pas. Chemin: ' . $viewPath);
        }
    }
}