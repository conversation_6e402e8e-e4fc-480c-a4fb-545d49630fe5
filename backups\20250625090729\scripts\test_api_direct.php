<?php
// Définir les en-têtes pour éviter les problèmes de cache
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
header('Content-Type: text/html; charset=utf-8');

// Démarrer la session
session_start();

// Inclure la configuration
require_once __DIR__ . '/../app/config/config.php';
require_once __DIR__ . '/../app/config/database.php';

// Afficher les informations de session et cookie
echo "<h1>Test de l'API get_client_info.php</h1>";

echo "<h2>Informations de session</h2>";
echo "<pre>";
if (isset($_SESSION['USER'])) {
    echo "Session USER existe:\n";
    print_r($_SESSION['USER']);
} else {
    echo "Session USER n'existe pas\n";
}
echo "</pre>";

echo "<h2>Informations de cookie</h2>";
echo "<pre>";
if (isset($_COOKIE['USERLOG'])) {
    echo "Cookie USERLOG existe:\n";
    $userlog = json_decode($_COOKIE['USERLOG'], true);
    print_r($userlog);
} else {
    echo "Cookie USERLOG n'existe pas\n";
}
echo "</pre>";

// Si l'utilisateur n'est pas connecté, proposer un formulaire de connexion
if (!isset($_SESSION['USER']) && !isset($_COOKIE['USERLOG'])) {
    echo "<h2>Connexion de test</h2>";
    echo "<form method='post' action=''>";
    echo "<label>Email: <input type='email' name='email' required></label><br>";
    echo "<label>Mot de passe: <input type='password' name='password' required></label><br>";
    echo "<button type='submit' name='login'>Se connecter</button>";
    echo "</form>";
    
    // Traiter le formulaire de connexion
    if (isset($_POST['login'])) {
        $email = $_POST['email'];
        $password = $_POST['password'];
        
        // Connexion à la base de données
        $conn = new mysqli(DB_HOST, DB_USER, DB_PASS, DB_NAME);
        if ($conn->connect_error) {
            die("Erreur de connexion à la base de données: " . $conn->connect_error);
        }
        
        // Vérifier les identifiants
        $stmt = $conn->prepare("SELECT * FROM clients WHERE emailclient = ? LIMIT 1");
        $stmt->bind_param("s", $email);
        $stmt->execute();
        $result = $stmt->get_result();
        
        if ($result->num_rows > 0) {
            $client = $result->fetch_assoc();
            $hashed_password = md5($password); // MD5 est utilisé dans ce projet
            
            if ($hashed_password === $client['passwdclient']) {
                // Créer la session et le cookie
                $_SESSION['USER'] = [
                    'email' => $client['emailclient'],
                    'nom' => $client['nomclient'],
                    'prenom' => $client['prenomclient'],
                    'isadmin' => $client['isadmin']
                ];
                
                $cookie_value = json_encode($_SESSION['USER']);
                setcookie('USERLOG', $cookie_value, time() + 3600 * 24 * 30, '/');
                
                echo "<p style='color: green;'>Connexion réussie! Rechargez la page pour voir les informations de session.</p>";
            } else {
                echo "<p style='color: red;'>Mot de passe incorrect.</p>";
            }
        } else {
            echo "<p style='color: red;'>Email non trouvé.</p>";
        }
        
        $conn->close();
    }
} else {
    // Si l'utilisateur est connecté, afficher un bouton pour tester l'API
    echo "<h2>Tester l'API</h2>";
    echo "<button onclick='testApi()'>Tester l'API get_client_info.php</button>";
    echo "<div id='apiResult' style='margin-top: 20px; padding: 10px; border: 1px solid #ccc;'></div>";
    
    echo "<script>
    function testApi() {
        document.getElementById('apiResult').innerHTML = 'Chargement...';
        
        fetch('../app/ajax/get_client_info.php')
            .then(response => {
                document.getElementById('apiResult').innerHTML += '<p>Statut de la réponse: ' + response.status + '</p>';
                return response.json();
            })
            .then(data => {
                document.getElementById('apiResult').innerHTML += '<p>Données reçues:</p><pre>' + JSON.stringify(data, null, 2) + '</pre>';
            })
            .catch(error => {
                document.getElementById('apiResult').innerHTML += '<p style=\"color: red;\">Erreur: ' + error.message + '</p>';
            });
    }
    </script>";
    
    // Afficher un bouton pour se déconnecter
    echo "<h2>Déconnexion</h2>";
    echo "<form method='post' action=''>";
    echo "<button type='submit' name='logout'>Se déconnecter</button>";
    echo "</form>";
    
    // Traiter la déconnexion
    if (isset($_POST['logout'])) {
        // Supprimer la session
        unset($_SESSION['USER']);
        
        // Supprimer le cookie
        setcookie('USERLOG', '', time() - 3600, '/');
        
        echo "<p style='color: green;'>Déconnexion réussie! Rechargez la page.</p>";
    }
}
?>
