<?php
// D<PERSON>marrer la session
session_start();

// Charger la configuration
require_once __DIR__ . '/../app/config/config.php';
require_once __DIR__ . '/../app/config/database.php';

// Simuler un utilisateur connecté si aucun n'est présent
if (!isset($_SESSION['USER']) && !isset($_COOKIE['USERLOG'])) {
    // Créer un utilisateur de test
    $_SESSION['USER'] = [
        'email' => '<EMAIL>',
        'nom' => 'Utilisateur',
        'prenom' => 'Test',
        'role' => 'client'
    ];

    echo "<div style='background-color: #d4edda; color: #155724; padding: 10px; margin-bottom: 20px; border-radius: 4px;'>
        Utilisateur de test créé pour la démonstration.
    </div>";
}

// Connexion à la base de données des clients
$conn = getClientsDbConnection();

// Récupérer l'email de l'utilisateur (clé principale)
$email = $_SESSION['USER']['email'];

// Créer un client de test si nécessaire
$stmt = $conn->prepare("SELECT * FROM clients WHERE emailclient = ? LIMIT 1");
$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows === 0) {
    // Créer un client de test
    $emailclient = $email;
    $passwdclient = md5('test123');
    $nomclient = 'Utilisateur';
    $prenomclient = 'Test';
    $isadmin = 0;
    $datecreate = date('Y-m-d');

    $stmt = $conn->prepare("INSERT INTO clients (emailclient, passwdclient, nomclient, prenomclient, isadmin, datecreate) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->bind_param("ssssis", $emailclient, $passwdclient, $nomclient, $prenomclient, $isadmin, $datecreate);
    $stmt->execute();

    echo "<div style='background-color: #d4edda; color: #155724; padding: 10px; margin-bottom: 20px; border-radius: 4px;'>
        Client de test créé dans la base de données.
    </div>";

    // Créer une adresse de facturation de test
    $stmt = $conn->prepare("INSERT INTO adresses_facturation (af_emailclient, af_raisonsocial, af_nomrue, af_codepostal, af_ville, af_pays) VALUES (?, ?, ?, ?, ?, ?)");
    $af_raisonsocial = 'Utilisateur Test';
    $af_nomrue = '123 Rue de Test';
    $af_codepostal = '75000';
    $af_ville = 'Paris';
    $af_pays = 'France';
    $stmt->bind_param("ssssss", $emailclient, $af_raisonsocial, $af_nomrue, $af_codepostal, $af_ville, $af_pays);
    $stmt->execute();

    echo "<div style='background-color: #d4edda; color: #155724; padding: 10px; margin-bottom: 20px; border-radius: 4px;'>
        Adresse de facturation de test créée dans la base de données.
    </div>";
}

// Récupérer les informations du client
$stmt = $conn->prepare("SELECT * FROM clients WHERE emailclient = ? LIMIT 1");
$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();
$client = $result->fetch_assoc();

// Récupérer l'adresse de facturation
$stmt = $conn->prepare("SELECT * FROM adresses_facturation WHERE af_emailclient = ? LIMIT 1");
$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();
$adresse = $result->fetch_assoc();

// Afficher la page de profil
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mon Profil - Jeep Dodge GMC</title>
    <meta name="description" content="Gérez votre profil utilisateur">

    <!-- Liens Google Fonts pour les polices de substitution -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Bitter:ital,wght@0,400;0,700;1,400&amp;family=Merriweather:wght@400;700&amp;family=Oswald:wght@400;700&amp;display=swap" rel="stylesheet">

    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .profile-container {
            background-color: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            margin: 20px auto;
            max-width: 800px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .profile-header {
            border-bottom: 2px solid #eee;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }

        .profile-header h1 {
            color: #333;
            font-family: 'Merriweather', serif;
            font-size: 24px;
            margin-bottom: 5px;
        }

        .profile-header p {
            color: #666;
            font-size: 14px;
            margin-top: 0;
        }

        .profile-info {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .info-group {
            margin-bottom: 15px;
        }

        .info-label {
            font-weight: bold;
            color: #555;
            margin-bottom: 5px;
            display: block;
        }

        .info-value {
            padding: 8px 12px;
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            color: #333;
        }

        .profile-actions {
            margin-top: 30px;
            display: flex;
            justify-content: flex-end;
            gap: 10px;
        }

        .profile-button {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
            text-decoration: none;
            display: inline-block;
        }

        .edit-button {
            background-color: #4CAF50;
            color: white;
        }

        .edit-button:hover {
            background-color: #45a049;
        }

        .logout-button {
            background-color: #f44336;
            color: white;
        }

        .logout-button:hover {
            background-color: #d32f2f;
        }

        h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
            grid-column: 1 / -1;
        }
    </style>
</head>
<body>
    <div class="profile-container">
        <div class="profile-header">
            <h1>Mon Profil</h1>
            <p>Gérez vos informations personnelles</p>
        </div>

        <div class="profile-info">
            <h3>Informations personnelles</h3>

            <div class="info-group">
                <span class="info-label">Prénom</span>
                <div class="info-value"><?= htmlspecialchars($client['prenomclient']) ?></div>
            </div>

            <div class="info-group">
                <span class="info-label">Nom</span>
                <div class="info-value"><?= htmlspecialchars($client['nomclient']) ?></div>
            </div>

            <div class="info-group">
                <span class="info-label">Email</span>
                <div class="info-value"><?= htmlspecialchars($client['emailclient']) ?></div>
            </div>

            <div class="info-group">
                <span class="info-label">Téléphone</span>
                <div class="info-value"><?= htmlspecialchars($client['telclient'] ?: 'Non renseigné') ?></div>
            </div>

            <div class="info-group">
                <span class="info-label">Rôle</span>
                <div class="info-value"><?= $client['isadmin'] ? 'Administrateur' : 'Client' ?></div>
            </div>

            <div class="info-group">
                <span class="info-label">Date d'inscription</span>
                <div class="info-value"><?= htmlspecialchars($client['datecreate']) ?></div>
            </div>

            <h3>Adresse de facturation</h3>

            <?php if ($adresse): ?>
                <div class="info-group">
                    <span class="info-label">Raison sociale / Nom complet</span>
                    <div class="info-value"><?= htmlspecialchars($adresse['af_raisonsocial']) ?></div>
                </div>

                <div class="info-group">
                    <span class="info-label">Adresse</span>
                    <div class="info-value"><?= htmlspecialchars($adresse['af_nomrue']) ?></div>
                </div>

                <div class="info-group">
                    <span class="info-label">Code postal</span>
                    <div class="info-value"><?= htmlspecialchars($adresse['af_codepostal']) ?></div>
                </div>

                <div class="info-group">
                    <span class="info-label">Ville</span>
                    <div class="info-value"><?= htmlspecialchars($adresse['af_ville']) ?></div>
                </div>

                <div class="info-group">
                    <span class="info-label">Pays</span>
                    <div class="info-value"><?= htmlspecialchars($adresse['af_pays']) ?></div>
                </div>
            <?php else: ?>
                <div class="info-group" style="grid-column: 1 / -1;">
                    <div class="info-value" style="background-color: #f8d7da; color: #721c24; border-color: #f5c6cb;">
                        Aucune adresse de facturation n'est enregistrée pour ce compte.
                    </div>
                </div>
            <?php endif; ?>
        </div>

        <div class="profile-actions">
            <a href="<?= BASE_URL ?>" class="profile-button edit-button">Retour à l'accueil</a>
            <a href="<?= BASE_URL ?>utilisateur/deconnexion" class="profile-button logout-button">Déconnexion</a>
        </div>
    </div>
</body>
</html>
<?php
// Fermer la connexion
$conn->close();
?>
