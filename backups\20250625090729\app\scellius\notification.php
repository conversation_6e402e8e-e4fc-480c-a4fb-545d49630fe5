<?php
// --- Notification Serveur-à-Serveur Scellius ---

// Activer le log d'erreurs pour ce script (utile pour le débogage)
ini_set('log_errors', 1);

// Définir le chemin de base manuellement pour éviter les problèmes sur cPanel
$basePath = dirname(dirname(__DIR__));
$logFile = $basePath . '/logs/scellius_notification.log';

// Spécifier un fichier de log dédié pour Scellius
ini_set('error_log', $logFile);

// Fonction pour journaliser les informations de débogage
function logScellius($message, $data = null, $addSeparator = false) {
    global $logFile;

    // Créer une ligne de séparation pour mieux visualiser les transactions
    $separator = str_repeat('-', 80);

    $logMessage = date('Y-m-d H:i:s') . " - " . $message;
    if ($data !== null) {
        $logMessage .= " - " . json_encode($data);
    }

    // Utiliser error_log standard
    error_log($logMessage);

    // Écrire également dans notre fichier de log personnalisé
    if (is_writable(dirname($logFile))) {
        // Ajouter un séparateur si demandé
        if ($addSeparator) {
            file_put_contents($logFile, PHP_EOL . $separator . PHP_EOL, FILE_APPEND);
        }
        file_put_contents($logFile, $logMessage . PHP_EOL, FILE_APPEND);
    }
}

// Inclure les configurations
require_once 'scellius_module/config.php';
require_once __DIR__ . '/../config/config.php'; // Inclure le fichier de configuration principal

// Inclure le modèle VehiculeModel et la configuration de la base de données des clients
require_once __DIR__ . '/../models/VehiculeModel.php';
require_once __DIR__ . '/../config/database_clients.php';

// Inclure TCPDF pour la génération du bon de commande
if (file_exists(dirname(__DIR__, 2) . '/vendor/tecnickcom/tcpdf/tcpdf.php')) {
    require_once dirname(__DIR__, 2) . '/vendor/tecnickcom/tcpdf/tcpdf.php';
}

// Fonction pour recalculer la signature (identique aux autres fichiers)
function calculate_scellius_signature(array $fields, string $secretKey, string $algo = SCELLIUS_SIGN_ALGO): string
{
    ksort($fields);
    $stringToSign = '';
    foreach ($fields as $key => $value) {
        if (strpos($key, 'vads_') === 0) {
            $stringToSign .= $value . '+';
        }
    }
    $stringToSign .= $secretKey;
    return base64_encode(hash_hmac(strtolower(str_replace('-', '', $algo)), $stringToSign, $secretKey, true));
}

// --- Traitement de la requête POST ---

// Ajouter un séparateur au début de chaque nouvelle requête pour faciliter la lecture des logs
logScellius("DÉBUT NOUVELLE TRANSACTION SCELLIUS", ['timestamp' => time()], true);

// Vérifier si c'est une requête POST et si la signature est présente
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['signature'])) {
    $received_data = $_POST;
    $received_signature = $received_data['signature'];
    unset($received_data['signature']); // Retirer la signature pour le calcul

    // Calculer la signature attendue
    $expected_signature = calculate_scellius_signature($received_data, SCELLIUS_SECRET_KEY);

    // Comparer les signatures
    if ($expected_signature === $received_signature) {
        // Signature VALIDE - Traiter la notification
        $trans_id = $received_data['vads_trans_id'] ?? 'N/A';
        $order_id = $received_data['vads_order_id'] ?? 'N/A'; // Si vous l'avez envoyé
        $status = $received_data['vads_trans_status'] ?? 'UNKNOWN';

        logScellius("Signature OK pour trans_id: $trans_id, order_id: $order_id, status: $status");
        logScellius("Données complètes reçues", $received_data);

        // --- LOGIQUE MÉTIER CRITIQUE ---
        // C'est ici que vous devez agir en fonction du statut
        // Assurez-vous que cette logique est IDEMPOTENTE

        if (in_array($status, ['AUTHORISED', 'CAPTURED'])) {
            // Paiement confirmé !
            logScellius("Paiement CONFIRME pour trans_id: $trans_id, order_id: $order_id");

            // Récupérer les informations du client et du véhicule
            $clientEmail = $received_data['vads_cust_email'] ?? null;

            // Extraire la référence du véhicule et l'information d'aide au transport du champ vads_order_info2
            $orderInfo2 = $received_data['vads_order_info2'] ?? '';
            $orderInfo2Parts = explode('|', $orderInfo2);

            // La référence du véhicule est la première partie
            $vehicleReference = $orderInfo2Parts[0] ?? null;

            // L'information d'aide au transport est dans la deuxième partie (si elle existe)
            $transportInfo = isset($orderInfo2Parts[1]) ? $orderInfo2Parts[1] : '';
            $needsTransport = (strpos($transportInfo, 'Transport:Oui') !== false);

            logScellius("Informations extraites", [
                'clientEmail' => $clientEmail,
                'vehicleReference' => $vehicleReference,
                'transportInfo' => $transportInfo,
                'needsTransport' => $needsTransport ? 'Oui' : 'Non'
            ]);

            if (!$clientEmail || !$vehicleReference) {
                logScellius("ERREUR: Informations client ou véhicule manquantes", [
                    'clientEmail' => $clientEmail,
                    'vehicleReference' => $vehicleReference
                ]);

                // Essayer de récupérer les informations d'autres champs si disponibles
                if (!$clientEmail) {
                    $clientEmail = $received_data['vads_cust_id'] ?? null;
                    logScellius("Tentative de récupération de l'email depuis vads_cust_id", ['clientEmail' => $clientEmail]);
                }

                if (!$vehicleReference) {
                    $vehicleReference = $received_data['vads_order_id'] ?? null;
                    logScellius("Tentative de récupération de la référence depuis vads_order_id", ['vehicleReference' => $vehicleReference]);
                }

                // Vérifier à nouveau si nous avons les informations nécessaires
                if (!$clientEmail || !$vehicleReference) {
                    logScellius("ERREUR CRITIQUE: Impossible de récupérer les informations client ou véhicule", null, true);
                    exit;
                }
            }

            try {
                // Initialiser le modèle de véhicule
                $vehiculeModel = new VehiculeModel();
                logScellius("Modèle VehiculeModel initialisé");

                // Vérifier si le client a déjà acheté un véhicule
                $clientHasVehicle = $vehiculeModel->clientHasVehicle($clientEmail);
                logScellius("Vérification si le client a déjà un véhicule", ['clientHasVehicle' => $clientHasVehicle]);

                if ($clientHasVehicle) {
                    logScellius("ERREUR: Le client $clientEmail a déjà acheté un véhicule. Paiement ignoré.", null, true);
                    exit;
                }

                // Récupérer le véhicule directement par sa référence
                logScellius("Recherche du véhicule par référence", [
                    'vehicleReference' => $vehicleReference
                ]);

                // Récupérer les données du véhicule par sa référence
                $vehicule = $vehiculeModel->getVehiculeByReference($vehicleReference);

                if (!$vehicule) {
                    logScellius("ERREUR: Véhicule non trouvé pour la référence: $vehicleReference", null, true);
                    exit;
                }

                // Récupérer l'ID du véhicule à partir des données
                $vehiculeId = $vehicule['id'];
                logScellius("Véhicule trouvé", [
                    'vehiculeId' => $vehiculeId,
                    'reference' => $vehicleReference,
                    'libelle' => $vehicule['libelle']
                ]);

                // Préparer les données pour la mise à jour
                $currentDateTime = date('Y-m-d H:i:s');
                logScellius("Préparation des données avec l'heure actuelle: $currentDateTime");

                // Récupérer l'adresse du client depuis la base de données des clients
                $clientAddress = '';
                try {
                    // Connexion à la base de données des clients
                    $clientsDb = getClientsDbConnection();
                    logScellius("Connexion à la base de données des clients établie");

                    // Requête pour récupérer l'adresse de facturation
                    $stmt = $clientsDb->prepare("SELECT * FROM adresses_facturation WHERE af_emailclient = ? LIMIT 1");
                    if (!$stmt) {
                        throw new Exception($clientsDb->error);
                    }

                    $stmt->bind_param("s", $clientEmail);
                    $stmt->execute();
                    $adresseResult = $stmt->get_result();

                    if ($adresseResult->num_rows > 0) {
                        $adresse = $adresseResult->fetch_assoc();

                        // Formater l'adresse
                        $adresseParts = [];
                        if (!empty($adresse['af_raisonsocial'])) $adresseParts[] = $adresse['af_raisonsocial'];
                        if (!empty($adresse['af_nomrue'])) $adresseParts[] = $adresse['af_nomrue'];
                        if (!empty($adresse['af_codepostal']) || !empty($adresse['af_ville'])) {
                            $cpVille = trim($adresse['af_codepostal'] . ' ' . $adresse['af_ville']);
                            if (!empty($cpVille)) $adresseParts[] = $cpVille;
                        }
                        if (!empty($adresse['af_pays'])) $adresseParts[] = $adresse['af_pays'];

                        $clientAddress = implode(', ', $adresseParts);
                        logScellius("Adresse du client récupérée", ['adresse' => $clientAddress]);
                    } else {
                        logScellius("Aucune adresse trouvée pour le client $clientEmail");
                    }

                    // Fermer la connexion à la base de données des clients
                    $clientsDb->close();

                } catch (Exception $e) {
                    logScellius("Erreur lors de la récupération de l'adresse du client", [
                        'message' => $e->getMessage(),
                        'file' => $e->getFile(),
                        'line' => $e->getLine()
                    ]);
                    // Continuer même si l'adresse n'a pas pu être récupérée
                }

                // Vérifier si le certificat est demandé dans les données reçues
                $certificatInfo = $received_data['vads_order_info3'] ?? '';
                $hasCertificate = (strpos($certificatInfo, 'Certificat: Oui') !== false);

                logScellius("Vérification du certificat", [
                    'certificatInfo' => $certificatInfo,
                    'hasCertificate' => $hasCertificate ? 'Oui' : 'Non'
                ]);

                // L'information d'aide au transport a déjà été extraite du champ vads_order_info2
                // Forcer la valeur de aidetransport à 1 si Transport:Oui est détecté
                if ($needsTransport) {
                    // Journaliser cette action spécifique
                    logScellius("AIDE AU TRANSPORT DEMANDÉE - Forçage de la valeur à 1", [
                        'transportInfo' => $transportInfo
                    ]);
                }

                logScellius("Vérification de l'aide au transport", [
                    'transportInfo' => $transportInfo,
                    'needsTransport' => $needsTransport ? 'Oui' : 'Non'
                ]);

                // Forcer la valeur de aidetransport à 1 si Transport: Oui est détecté
                $aidetransportValue = $needsTransport ? 1 : 0;

                // Journaliser la valeur de aidetransport avant de créer le tableau de données
                logScellius("Valeur de aidetransport avant création du tableau de données", [
                    'aidetransportValue' => $aidetransportValue,
                    'needsTransport' => $needsTransport ? 'Oui' : 'Non'
                ]);

                $data = [
                    'reference' => '', // Sera rempli par les données existantes
                    'libelle' => '', // Sera rempli par les données existantes
                    'descriptif' => '', // Sera rempli par les données existantes
                    'annee' => '', // Sera rempli par les données existantes
                    'prix' => 0, // Sera rempli par les données existantes
                    'client' => $clientEmail,
                    'client_adresse' => $clientAddress, // Adresse récupérée depuis la base de données des clients
                    'statut' => 'Vendu',
                    'rgltreference' => $trans_id,
                    'certificat' => $hasCertificate ? 1 : 0, // Mettre à jour en fonction des données reçues
                    'libelle_certificat' => '', // Sera rempli par les données existantes
                    'aidetransport' => $aidetransportValue, // Utiliser la valeur forcée
                    'dateheureachat' => $currentDateTime // Date et heure du règlement validé
                ];

                // Les données du véhicule ont déjà été récupérées par référence
                logScellius("Utilisation des données du véhicule déjà récupérées", ['vehicule' => $vehicule]);

                // Vérifier que les données du véhicule sont valides
                if ($vehicule) {
                    // Récupérer la référence du véhicule
                    $vehiculeReference = $vehicule['reference'];
                    logScellius("Référence du véhicule récupérée", ['reference' => $vehiculeReference]);

                    // Conserver les données existantes
                    $data['libelle'] = $vehicule['libelle'];
                    $data['descriptif'] = $vehicule['descriptif'];
                    $data['annee'] = $vehicule['annee'];
                    $data['prix'] = $vehicule['prix'];
                    // Ne pas écraser l'adresse du client si elle a été récupérée
                    if (empty($data['client_adresse'])) {
                        $data['client_adresse'] = $vehicule['client_adresse'];
                    }
                    // Ne pas écraser la valeur du certificat définie précédemment
                    // $data['certificat'] est déjà défini en fonction des données reçues de Scellius
                    $data['libelle_certificat'] = $vehicule['libelle_certificat'];

                    logScellius("Valeur finale du certificat", [
                        'certificat' => $data['certificat'] ? 'Oui' : 'Non'
                    ]);

                    // Vérifier la valeur de dateheureachat
                    logScellius("Valeurs actuelles dans la base de données", [
                        'dateheureachat' => $vehicule['dateheureachat'] ?? 'non défini'
                    ]);

                    // Vérifier le statut actuel du véhicule
                    $currentStatus = $vehicule['statut'] ?? '';
                    logScellius("Statut actuel du véhicule", ['currentStatus' => $currentStatus]);

                    // Si le véhicule est déjà vendu, ne pas le mettre à jour
                    if ($currentStatus === 'Vendu') {
                        logScellius("Le véhicule est déjà marqué comme vendu, pas de mise à jour nécessaire");
                    } else {
                        // Mettre à jour le véhicule par sa référence
                        logScellius("Tentative de mise à jour du véhicule par référence", [
                            'reference' => $vehiculeReference,
                            'data' => $data
                        ]);

                        // Vérifier spécifiquement la valeur de dateheureachat
                        logScellius("Valeurs à mettre à jour", [
                            'dateheureachat' => $data['dateheureachat']
                        ]);

                        // Log des données avant la mise à jour
                        logScellius("Données à mettre à jour", [
                            'client' => $data['client'],
                            'client_adresse' => $data['client_adresse'],
                            'statut' => $data['statut'],
                            'rgltreference' => $data['rgltreference'],
                            'dateheureachat' => $data['dateheureachat']
                        ]);

                        if ($vehiculeModel->updateVehiculeByReference($vehiculeReference, $data)) {
                            logScellius("SUCCÈS: Véhicule avec référence $vehiculeReference mis à jour avec le client $clientEmail et le statut 'Vendu'");

                            // Générer et envoyer le bon de commande par email
                            try {
                                // Récupérer les données complètes du véhicule après mise à jour
                                $updatedVehicule = $vehiculeModel->getVehiculeByReference($vehiculeReference);

                                if ($updatedVehicule) {
                                    // Générer le bon de commande
                                    $pdfInfo = generateBonCommandePDF($updatedVehicule);

                                    if ($pdfInfo) {
                                        // Envoyer le bon de commande par email
                                        $emailSent = sendBonCommandeByEmail($clientEmail, $pdfInfo['path'], $vehiculeReference);
                                        logScellius("Envoi du bon de commande par email", [
                                            'success' => $emailSent,
                                            'clientEmail' => $clientEmail,
                                            'pdfPath' => $pdfInfo['path']
                                        ], true);
                                    } else {
                                        logScellius("ERREUR: Échec de la génération du bon de commande", null, true);
                                    }
                                } else {
                                    logScellius("ERREUR: Impossible de récupérer les données du véhicule après mise à jour", null, true);
                                }
                            } catch (Exception $e) {
                                logScellius("EXCEPTION lors de la génération/envoi du bon de commande", [
                                    'message' => $e->getMessage(),
                                    'file' => $e->getFile(),
                                    'line' => $e->getLine()
                                ], true);
                            }
                        } else {
                            // Récupérer l'erreur MySQL si disponible
                            $error = $vehiculeModel->conn->error;
                            logScellius("ERREUR: Échec de la mise à jour du véhicule avec référence $vehiculeReference", ['mysql_error' => $error]);

                            // Tentative directe avec une requête SQL simple
                            // Mettre à jour dateheureachat avec l'heure du règlement validé
                            $currentDateTime = date('Y-m-d H:i:s');
                            logScellius("Mise à jour directe avec l'heure actuelle: $currentDateTime");

                            $sql = "UPDATE vehicules SET
                                    statut = 'Vendu',
                                    client = ?,
                                    client_adresse = ?,
                                    rgltreference = ?,
                                    dateheureachat = ?,
                                    certificat = ?,
                                    aidetransport = ?
                                    WHERE reference = ?";
                            $stmt = $vehiculeModel->conn->prepare($sql);
                            $certificatValue = $data['certificat']; // Utiliser la valeur du certificat définie précédemment
                            $transportValue = $needsTransport ? 1 : 0; // Forcer la valeur en fonction de needsTransport

                            // Journaliser les valeurs avant la mise à jour directe
                            logScellius("Valeurs pour la mise à jour directe", [
                                'certificatValue' => $certificatValue,
                                'transportValue' => $transportValue,
                                'needsTransport' => $needsTransport ? 'Oui' : 'Non'
                            ]);

                            $stmt->bind_param("ssssiii", $clientEmail, $clientAddress, $trans_id, $currentDateTime, $certificatValue, $transportValue, $vehiculeReference);

                            if ($stmt->execute()) {
                                logScellius("SUCCÈS: Mise à jour alternative réussie pour le véhicule avec référence $vehiculeReference");

                                // Générer et envoyer le bon de commande par email
                                try {
                                    // Récupérer les données complètes du véhicule après mise à jour
                                    $updatedVehicule = $vehiculeModel->getVehiculeByReference($vehiculeReference);

                                    if ($updatedVehicule) {
                                        // Générer le bon de commande
                                        $pdfInfo = generateBonCommandePDF($updatedVehicule);

                                        if ($pdfInfo) {
                                            // Envoyer le bon de commande par email
                                            $emailSent = sendBonCommandeByEmail($clientEmail, $pdfInfo['path'], $vehiculeReference);
                                            logScellius("Envoi du bon de commande par email (méthode alternative)", [
                                                'success' => $emailSent,
                                                'clientEmail' => $clientEmail,
                                                'pdfPath' => $pdfInfo['path']
                                            ], true);
                                        } else {
                                            logScellius("ERREUR: Échec de la génération du bon de commande (méthode alternative)", null, true);
                                        }
                                    } else {
                                        logScellius("ERREUR: Impossible de récupérer les données du véhicule après mise à jour (méthode alternative)", null, true);
                                    }
                                } catch (Exception $e) {
                                    logScellius("EXCEPTION lors de la génération/envoi du bon de commande (méthode alternative)", [
                                        'message' => $e->getMessage(),
                                        'file' => $e->getFile(),
                                        'line' => $e->getLine()
                                    ], true);
                                }
                            } else {
                                logScellius("ERREUR CRITIQUE: Échec de la mise à jour alternative", ['mysql_error' => $vehiculeModel->conn->error], true);
                            }
                        }
                    }
                } else {
                    logScellius("ERREUR: Véhicule ID $vehiculeId non trouvé lors de la récupération des données", null, true);
                }
            } catch (Exception $e) {
                logScellius("EXCEPTION lors du traitement du paiement", [
                    'message' => $e->getMessage(),
                    'file' => $e->getFile(),
                    'line' => $e->getLine()
                ], true);
            }

        } elseif (in_array($status, ['REFUSED', 'CANCELLED', 'EXPIRED', 'ABANDONED'])) {
            // Paiement refusé ou annulé
            logScellius("Paiement échoué (statut: $status) pour trans_id: $trans_id, order_id: $order_id");

            // Récupérer les informations du véhicule
            $orderInfo2 = $received_data['vads_order_info2'] ?? '';
            $orderInfo2Parts = explode('|', $orderInfo2);
            $vehicleReference = $orderInfo2Parts[0] ?? null;
            $clientEmail = $received_data['vads_cust_email'] ?? null;

            if ($vehicleReference || $clientEmail) {
                // Initialiser le modèle de véhicule
                $vehiculeModel = new VehiculeModel();

                // Réinitialiser le statut du véhicule
                if ($vehicleReference) {
                    // Si nous avons la référence, utiliser la méthode dédiée
                    if ($vehiculeModel->resetVehiculeStatusByReference($vehicleReference)) {
                        logScellius("SUCCÈS: Statut du véhicule réinitialisé à 'Disponible' pour la référence: $vehicleReference", null, true);
                    } else {
                        logScellius("ERREUR: Échec de la réinitialisation du statut du véhicule pour la référence: $vehicleReference");
                    }
                } else if ($clientEmail) {
                    // Sinon, essayer avec l'email du client
                    if ($vehiculeModel->resetVehiculeStatus(null, null, $clientEmail)) {
                        logScellius("SUCCÈS: Statut du véhicule réinitialisé à 'Disponible' pour le client: $clientEmail", null, true);
                    } else {
                        logScellius("ERREUR: Échec de la réinitialisation du statut du véhicule pour le client: $clientEmail");
                    }
                }
            } else {
                logScellius("ERREUR: Informations véhicule/client manquantes pour réinitialiser le statut");
            }

        } else {
            // Autres statuts (WAITING...)
            logScellius("Statut non traité '$status' pour trans_id: $trans_id, order_id: $order_id");

            // Pour les statuts temporaires comme WAITING, on ne fait rien pour l'instant
            // Si le paiement est finalement accepté ou refusé, on recevra une autre notification
        }

        // --- Réponse au serveur Scellius ---
        // La documentation Scellius V2 spécifie généralement ce qu'il faut renvoyer.
        // Souvent, un simple HTTP 200 OK suffit pour acquitter la notification.
        // Si un contenu spécifique est attendu, adaptez la réponse.
        // header("HTTP/1.1 200 OK");
        // echo "OK"; // Ou le texte attendu par Scellius si différent

        // Pour l'instant, on ne renvoie rien explicitement, PHP renverra 200 OK par défaut si pas d'erreur.

    } else {
        // Signature INVALIDE - Risque de fraude !
        $trans_id = $received_data['vads_trans_id'] ?? 'N/A';
        logScellius("ERREUR: Signature INVALIDE reçue pour trans_id: $trans_id. Attendu: $expected_signature, Reçu: $received_signature", $received_data, true); // Ajouter un séparateur pour marquer clairement cette erreur

        // Répondre par une erreur pour indiquer un problème au serveur Scellius ?
        // header("HTTP/1.1 403 Forbidden"); // Ou 500 Internal Server Error
        // echo "Signature verification failed.";
        // Consultez la doc Scellius pour la meilleure pratique en cas d'échec de signature.
    }

} else {
    // Requête invalide (pas POST ou signature manquante)
    logScellius("ERREUR: Requête invalide reçue", [
        'METHOD' => $_SERVER['REQUEST_METHOD'],
        'Signature_set' => isset($_POST['signature'])
    ], true); // Ajouter un séparateur pour marquer clairement cette erreur
    header("HTTP/1.1 400 Bad Request");
    echo "Invalid request.";
}

/**
 * Génère un bon de commande en PDF pour un véhicule
 * @param array $vehicule Les données du véhicule
 * @return array Tableau contenant le chemin du fichier PDF et le nom du fichier
 */
function generateBonCommandePDF($vehicule) {
    // Vérifier si TCPDF est disponible
    if (!class_exists('TCPDF')) {
        logScellius("ERREUR: La bibliothèque TCPDF n'est pas disponible pour générer le bon de commande", null, true);
        return null;
    }

    // Création d'une nouvelle instance de TCPDF
    $pdf = new TCPDF(PDF_PAGE_ORIENTATION, PDF_UNIT, PDF_PAGE_FORMAT, true, 'UTF-8', false);

    // Informations du document
    $pdf->SetCreator(COMPANY_NAME);
    $pdf->SetAuthor(COMPANY_NAME);
    $pdf->SetTitle('Bon de commande - ' . $vehicule['reference']);

    // Supprimer les en-têtes et pieds de page par défaut
    $pdf->setPrintHeader(false);
    $pdf->setPrintFooter(false);

    // Marges
    $pdf->SetMargins(15, 15, 15);

    // Ajout d'une page
    $pdf->AddPage();

    // Définir des couleurs personnalisées
    $headerColor = array(0, 61, 121); // Bleu foncé
    $accentColor = array(200, 30, 30); // Rouge
    $textColor = array(50, 50, 50); // Gris foncé
    $lightGray = array(240, 240, 240); // Gris clair pour les fonds

    // En-tête
    $pdf->SetFillColor($headerColor[0], $headerColor[1], $headerColor[2]);
    $pdf->Rect(0, 0, $pdf->getPageWidth(), 40, 'F');

    // Titre du document
    $pdf->SetY(15);
    $pdf->SetTextColor(255, 255, 255);
    $pdf->SetFont('helvetica', 'B', 24);
    $pdf->Cell(0, 10, 'BON DE COMMANDE', 0, 1, 'C');

    // Informations de l'entreprise
    $pdf->SetY(45);
    $pdf->SetTextColor($headerColor[0], $headerColor[1], $headerColor[2]);
    $pdf->SetFont('helvetica', 'B', 14);
    $pdf->Cell(0, 10, COMPANY_NAME, 0, 1, 'L');

    $pdf->SetTextColor($textColor[0], $textColor[1], $textColor[2]);
    $pdf->SetFont('helvetica', '', 10);
    $pdf->Cell(0, 6, COMPANY_ADDRESS, 0, 1, 'L');
    $pdf->Cell(0, 6, COMPANY_CITY, 0, 1, 'L');
    $pdf->Cell(0, 6, 'Tél: ' . COMPANY_PHONE . ' - Email: ' . COMPANY_EMAIL, 0, 1, 'L');
    $pdf->Cell(0, 6, COMPANY_WEBSITE, 0, 1, 'L');
    $pdf->Cell(0, 6, COMPANY_SIRET, 0, 1, 'L');

    // Slogan
    $pdf->SetY(45);
    $pdf->SetX(120);
    $pdf->SetFont('helvetica', 'I', 10);
    $pdf->SetTextColor($accentColor[0], $accentColor[1], $accentColor[2]);
    $pdf->MultiCell(75, 6, COMPANY_SLOGAN, 0, 'R');

    // Référence et date
    $pdf->SetY(85);
    $pdf->SetFillColor($lightGray[0], $lightGray[1], $lightGray[2]);
    $pdf->SetTextColor($headerColor[0], $headerColor[1], $headerColor[2]);
    $pdf->SetFont('helvetica', 'B', 12);
    $pdf->Cell(0, 10, 'BON DE COMMANDE N°: ' . $vehicule['reference'] . ' - ' . date('d/m/Y'), 0, 1, 'C', 1);

    $pdf->Ln(10);

    // Informations du client
    $pdf->SetTextColor($headerColor[0], $headerColor[1], $headerColor[2]);
    $pdf->SetFont('helvetica', 'B', 12);
    $pdf->Cell(0, 10, 'INFORMATIONS CLIENT', 0, 1, 'L');

    $pdf->SetDrawColor(200, 200, 200);
    $pdf->SetTextColor($textColor[0], $textColor[1], $textColor[2]);

    // Tableau des informations client
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->Cell(40, 8, 'Client:', 1, 0, 'L', 0);
    $pdf->SetFont('helvetica', '', 10);
    $pdf->Cell(145, 8, $vehicule['client'], 1, 1, 'L', 0);

    if (!empty($vehicule['client_adresse'])) {
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(40, 8, 'Adresse:', 1, 0, 'L', 0);
        $pdf->SetFont('helvetica', '', 10);
        $pdf->Cell(145, 8, $vehicule['client_adresse'], 1, 1, 'L', 0);
    }

    $pdf->Ln(10);

    // Détails du véhicule
    $pdf->SetTextColor($headerColor[0], $headerColor[1], $headerColor[2]);
    $pdf->SetFont('helvetica', 'B', 12);
    $pdf->Cell(0, 10, 'DÉTAILS DU VÉHICULE', 0, 1, 'L');

    // En-tête du tableau
    $pdf->SetFillColor($headerColor[0], $headerColor[1], $headerColor[2]);
    $pdf->SetTextColor(255, 255, 255);
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->Cell(95, 8, 'Description', 1, 0, 'C', 1);
    $pdf->Cell(30, 8, 'Année', 1, 0, 'C', 1);
    $pdf->Cell(30, 8, 'Certificat', 1, 0, 'C', 1);
    $pdf->Cell(30, 8, 'Prix', 1, 1, 'C', 1);

    // Contenu du tableau
    $pdf->SetTextColor($textColor[0], $textColor[1], $textColor[2]);
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->Cell(95, 8, $vehicule['libelle'], 1, 0, 'L', 0);
    $pdf->SetFont('helvetica', '', 10);
    $pdf->Cell(30, 8, $vehicule['annee'] ?? '-', 1, 0, 'C', 0);
    $pdf->Cell(30, 8, $vehicule['certificat'] ? 'Oui' : 'Non', 1, 0, 'C', 0);

    // Calculer le prix total en tenant compte du certificat
    $prixTotal = $vehicule['prix'];
    if ($vehicule['certificat']) {
        $prixTotal += 200; // Ajouter 200€ pour le certificat
    }

    $pdf->SetTextColor($accentColor[0], $accentColor[1], $accentColor[2]);
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->Cell(30, 8, number_format($prixTotal, 2, ',', ' ') . ' €', 1, 1, 'R', 0);

    // Description détaillée
    $pdf->SetTextColor($textColor[0], $textColor[1], $textColor[2]);
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->Cell(40, 8, 'Description:', 1, 0, 'L', 0);
    $pdf->SetFont('helvetica', '', 10);
    $pdf->Cell(145, 8, $vehicule['descriptif'], 1, 1, 'L', 0);

    if ($vehicule['certificat'] && !empty($vehicule['libelle_certificat'])) {
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(40, 8, 'Certificat:', 1, 0, 'L', 0);
        $pdf->SetFont('helvetica', '', 10);
        $pdf->Cell(145, 8, $vehicule['libelle_certificat'], 1, 1, 'L', 0);
    }

    // Afficher l'information d'aide au transport si demandée
    if (isset($vehicule['aidetransport']) && $vehicule['aidetransport']) {
        $pdf->SetFont('helvetica', 'B', 10);
        $pdf->Cell(40, 8, 'Aide au transport:', 1, 0, 'L', 0);
        $pdf->SetFont('helvetica', '', 10);
        $pdf->Cell(145, 8, 'Demandée', 1, 1, 'L', 0);
    }

    // Informations de règlement
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->Cell(40, 8, 'Informations Règlement:', 1, 0, 'L', 0);
    $pdf->SetFont('helvetica', '', 10);

    // Afficher les informations de règlement disponibles
    $infoReglement = [];

    if (isset($vehicule['rgltreference']) && !empty($vehicule['rgltreference'])) {
        $infoReglement[] = 'Référence: ' . $vehicule['rgltreference'];
    }

    // Si aucune information de règlement n'est disponible, afficher le statut
    if (empty($infoReglement) && isset($vehicule['statut']) && !empty($vehicule['statut'])) {
        $infoReglement[] = 'Statut: ' . $vehicule['statut'];
    }

    // Si aucune information n'est disponible
    if (empty($infoReglement)) {
        $infoReglementText = 'Aucune information de règlement disponible';
    } else {
        $infoReglementText = implode(' - ', $infoReglement);
    }

    $pdf->Cell(145, 8, $infoReglementText, 1, 1, 'L', 0);

    // Récapitulatif financier
    $pdf->Ln(10);
    $pdf->SetTextColor($headerColor[0], $headerColor[1], $headerColor[2]);
    $pdf->SetFont('helvetica', 'B', 12);
    $pdf->Cell(0, 10, 'RÉCAPITULATIF', 0, 1, 'L');

    // Tableau récapitulatif
    // Calculer le prix total en tenant compte du certificat
    $prixTotal = $vehicule['prix'];
    if ($vehicule['certificat']) {
        $prixTotal += 200; // Ajouter 200€ pour le certificat
    }

    // Calculer le prix HT et la TVA
    $prixHT = $prixTotal / 1.2;
    $tva = $prixTotal - $prixHT;

    $pdf->SetFillColor($lightGray[0], $lightGray[1], $lightGray[2]);
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->Cell(155, 8, 'Total HT:', 1, 0, 'R', 1);
    $pdf->SetTextColor($accentColor[0], $accentColor[1], $accentColor[2]);
    $pdf->Cell(30, 8, number_format($prixHT, 2, ',', ' ') . ' €', 1, 1, 'R', 1);

    $pdf->SetTextColor($headerColor[0], $headerColor[1], $headerColor[2]);
    $pdf->Cell(155, 8, 'TVA (20%):', 1, 0, 'R', 0);
    $pdf->SetTextColor($accentColor[0], $accentColor[1], $accentColor[2]);
    $pdf->Cell(30, 8, number_format($tva, 2, ',', ' ') . ' €', 1, 1, 'R', 0);

    $pdf->SetTextColor($headerColor[0], $headerColor[1], $headerColor[2]);
    $pdf->SetFont('helvetica', 'B', 12);
    $pdf->Cell(155, 10, 'TOTAL TTC:', 1, 0, 'R', 1);
    $pdf->SetTextColor($accentColor[0], $accentColor[1], $accentColor[2]);
    $pdf->Cell(30, 10, number_format($prixTotal, 2, ',', ' ') . ' €', 1, 1, 'R', 1);

    // Date et signatures
    $pdf->Ln(20);
    $pdf->SetTextColor($textColor[0], $textColor[1], $textColor[2]);
    $pdf->SetFont('helvetica', '', 10);
    $pdf->Cell(0, 10, 'Fait à ' . COMPANY_CITY . ', le ' . date('d/m/Y'), 0, 1, 'R');

    $pdf->Ln(5);
    $pdf->SetDrawColor(200, 200, 200);

    // Zone de signatures
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->Cell(92, 10, 'Signature du vendeur:', 'T', 0, 'C');
    $pdf->Cell(5, 10, '', 0, 0);
    $pdf->Cell(92, 10, 'Signature du client:', 'T', 1, 'C');
    $pdf->Ln(25);

    // Conditions générales
    $pdf->SetFillColor($lightGray[0], $lightGray[1], $lightGray[2]);
    $pdf->SetTextColor($headerColor[0], $headerColor[1], $headerColor[2]);
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->Cell(0, 8, 'CONDITIONS GÉNÉRALES DE VENTE', 0, 1, 'L', 1);

    $pdf->SetTextColor($textColor[0], $textColor[1], $textColor[2]);
    $pdf->SetFont('helvetica', '', 8);
    $pdf->MultiCell(0, 5, "1. Le véhicule est vendu en l'état.\n2. Le prix indiqué est ferme et définitif.\n3. La vente est effective après paiement intégral du prix.\n4. Le transfert de propriété s'effectue après encaissement complet du règlement.\n5. Aucun retour ni échange n'est possible après la vente.", 0, 'L');

    // Pied de page
    $pdf->SetY(-30);
    $pdf->SetFillColor($headerColor[0], $headerColor[1], $headerColor[2]);
    $pdf->Rect(0, $pdf->GetY(), $pdf->getPageWidth(), 30, 'F');

    $pdf->SetY(-25);
    $pdf->SetTextColor(255, 255, 255);
    $pdf->SetFont('helvetica', 'B', 10);
    $pdf->Cell(0, 6, COMPANY_NAME, 0, 1, 'C');

    $pdf->SetFont('helvetica', '', 8);
    $pdf->Cell(0, 6, COMPANY_ADDRESS . ' - ' . COMPANY_CITY . ' - ' . COMPANY_PHONE, 0, 1, 'C');
    $pdf->Cell(0, 6, COMPANY_WEBSITE . ' - ' . COMPANY_EMAIL . ' - ' . COMPANY_SIRET, 0, 1, 'C');

    // Créer le répertoire private s'il n'existe pas
    $privateDir = dirname(__DIR__, 2) . '/private/';
    if (!is_dir($privateDir)) {
        mkdir($privateDir, 0755, true);
    }

    // Générer un nom de fichier unique avec la date
    $filename = 'bon_commande_' . $vehicule['reference'] . '_' . date('Ymd_His') . '.pdf';
    $filePath = $privateDir . $filename;

    // Sauvegarder le PDF dans le répertoire private
    $pdf->Output($filePath, 'F');

    logScellius("Bon de commande généré avec succès", ['filePath' => $filePath]);

    return [
        'path' => $filePath,
        'filename' => $filename
    ];
}

/**
 * Envoie le bon de commande par email
 * @param string $clientEmail Email du client
 * @param string $pdfPath Chemin vers le fichier PDF
 * @param string $reference Référence du véhicule
 * @return bool True si l'email a été envoyé avec succès, False sinon
 */
function sendBonCommandeByEmail($clientEmail, $pdfPath, $reference) {
    // Vérifier que le fichier existe
    if (!file_exists($pdfPath)) {
        logScellius("ERREUR: Le fichier PDF n'existe pas", ['pdfPath' => $pdfPath]);
        return false;
    }

    // Générer un identifiant unique pour la frontière du message
    $boundary = md5(time());

    // En-têtes de l'email
    $headers = 'From: ' . MAIL_FROM_NAME . ' <' . MAIL_FROM . ">\r\n";
    $headers .= "MIME-Version: 1.0\r\n";
    $headers .= "Content-Type: multipart/mixed; boundary=\"$boundary\"\r\n";

    // Sujet de l'email
    $subject = MAIL_SUBJECT_BON_COMMANDE . ' - ' . $reference;

    // Corps du message
    $message = "--$boundary\r\n";
    $message .= "Content-Type: text/plain; charset=UTF-8\r\n";
    $message .= "Content-Transfer-Encoding: 8bit\r\n\r\n";
    $message .= MAIL_BODY_BON_COMMANDE . "\r\n";

    // Pièce jointe
    $attachment = file_get_contents($pdfPath);
    $attachment = chunk_split(base64_encode($attachment));

    $message .= "--$boundary\r\n";
    $message .= "Content-Type: application/pdf; name=\"bon_commande_$reference.pdf\"\r\n";
    $message .= "Content-Transfer-Encoding: base64\r\n";
    $message .= "Content-Disposition: attachment; filename=\"bon_commande_$reference.pdf\"\r\n\r\n";
    $message .= $attachment . "\r\n";
    $message .= "--$boundary--";

    // Envoyer l'email au client
    $clientResult = mail($clientEmail, $subject, $message, $headers);
    logScellius("Email envoyé au client", ['clientEmail' => $clientEmail, 'success' => $clientResult]);

    // Envoyer l'email à <EMAIL>
    $contactEmail = '<EMAIL>';
    $contactResult = mail($contactEmail, $subject, $message, $headers);
    logScellius("Email envoyé à l'adresse de contact", ['contactEmail' => $contactEmail, 'success' => $contactResult]);

    return $clientResult && $contactResult;
}

// Ajouter un séparateur à la fin de chaque transaction
logScellius("FIN DE TRANSACTION SCELLIUS", ['timestamp' => time()], true);

// Terminer le script explicitement
exit;

?>