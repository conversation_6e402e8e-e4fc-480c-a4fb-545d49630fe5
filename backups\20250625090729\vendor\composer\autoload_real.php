<?php

// autoload_real.php @generated by Composer

class ComposerAutoloaderInit007a56ce5d77eeb623b8cae82ac92f7f
{
    private static $loader;

    public static function loadClassLoader($class)
    {
        if ('Composer\Autoload\ClassLoader' === $class) {
            require __DIR__ . '/ClassLoader.php';
        }
    }

    /**
     * @return \Composer\Autoload\ClassLoader
     */
    public static function getLoader()
    {
        if (null !== self::$loader) {
            return self::$loader;
        }

        require __DIR__ . '/platform_check.php';

        spl_autoload_register(array('ComposerAutoloaderInit007a56ce5d77eeb623b8cae82ac92f7f', 'loadClassLoader'), true, true);
        self::$loader = $loader = new \Composer\Autoload\ClassLoader(\dirname(__DIR__));
        spl_autoload_unregister(array('ComposerAutoloaderInit007a56ce5d77eeb623b8cae82ac92f7f', 'loadClassLoader'));

        require __DIR__ . '/autoload_static.php';
        call_user_func(\Composer\Autoload\ComposerStaticInit007a56ce5d77eeb623b8cae82ac92f7f::getInitializer($loader));

        $loader->register(true);

        return $loader;
    }
}
