<?php
// Script de test pour vérifier les images de HALF_TRACK

// Définir le chemin de base
define('BASE_PATH', __DIR__);

// Référence à tester
$reference = 'HALF_TRACK';

// Répertoire des images
$directory = BASE_PATH . '/public/img/';

// Si la référence contient des underscores, les remplacer par des espaces pour la recherche
$searchRef = str_replace('_', ' ', $reference);

echo "Recherche d'images pour la référence: " . $reference . " (searchRef: " . $searchRef . ")\n";
echo "Répertoire de recherche: " . $directory . "\n\n";

// Vérifier si le répertoire existe
if (!is_dir($directory)) {
    echo "Le répertoire n'existe pas: " . $directory . "\n";
    exit;
}

// Lister tous les fichiers dans le répertoire
echo "Tous les fichiers dans le répertoire:\n";
$allFiles = scandir($directory);
foreach ($allFiles as $file) {
    if ($file != '.' && $file != '..') {
        echo "- " . $file . "\n";
    }
}
echo "\n";

// Chercher l'image principale (sans suffixe)
echo "Recherche de l'image principale: " . $searchRef . ".{jpg,jpeg,png,gif}\n";
$mainImage = glob($directory . $searchRef . '.{jpg,jpeg,png,gif}', GLOB_BRACE);
if (!empty($mainImage)) {
    echo "Image principale trouvée:\n";
    foreach ($mainImage as $path) {
        echo "- " . $path . "\n";
    }
} else {
    echo "Aucune image principale trouvée.\n";
}
echo "\n";

// Chercher les images avec suffixe numérique
echo "Recherche d'images avec suffixe: " . $searchRef . "_*.{jpg,jpeg,png,gif}\n";
$suffixImages = glob($directory . $searchRef . '_*.{jpg,jpeg,png,gif}', GLOB_BRACE);
if (!empty($suffixImages)) {
    echo "Images avec suffixe trouvées:\n";
    foreach ($suffixImages as $path) {
        echo "- " . $path . "\n";
    }
} else {
    echo "Aucune image avec suffixe trouvée.\n";
}
echo "\n";

// Chercher avec la référence originale (avec underscores)
echo "Recherche avec la référence originale: " . $reference . ".{jpg,jpeg,png,gif}\n";
$origImage = glob($directory . $reference . '.{jpg,jpeg,png,gif}', GLOB_BRACE);
if (!empty($origImage)) {
    echo "Image originale trouvée:\n";
    foreach ($origImage as $path) {
        echo "- " . $path . "\n";
    }
} else {
    echo "Aucune image originale trouvée.\n";
}
echo "\n";

// Chercher les images avec suffixe numérique (référence originale)
echo "Recherche d'images avec suffixe (référence originale): " . $reference . "_*.{jpg,jpeg,png,gif}\n";
$origSuffixImages = glob($directory . $reference . '_*.{jpg,jpeg,png,gif}', GLOB_BRACE);
if (!empty($origSuffixImages)) {
    echo "Images avec suffixe (référence originale) trouvées:\n";
    foreach ($origSuffixImages as $path) {
        echo "- " . $path . "\n";
    }
} else {
    echo "Aucune image avec suffixe (référence originale) trouvée.\n";
}
echo "\n";

// Recherche plus large
echo "Recherche plus large: *" . $searchRef . "*.{jpg,jpeg,png,gif}\n";
$wideSearch = glob($directory . '*' . $searchRef . '*.{jpg,jpeg,png,gif}', GLOB_BRACE);
if (!empty($wideSearch)) {
    echo "Images trouvées (recherche large):\n";
    foreach ($wideSearch as $path) {
        echo "- " . $path . "\n";
    }
} else {
    echo "Aucune image trouvée (recherche large).\n";
}
echo "\n";

// Recherche insensible à la casse
echo "Recherche insensible à la casse:\n";
$caseInsensitive = [];
foreach ($allFiles as $file) {
    if ($file != '.' && $file != '..' && 
        (stripos($file, $searchRef) !== false || stripos($file, $reference) !== false)) {
        $caseInsensitive[] = $directory . $file;
    }
}
if (!empty($caseInsensitive)) {
    echo "Images trouvées (insensible à la casse):\n";
    foreach ($caseInsensitive as $path) {
        echo "- " . $path . "\n";
    }
} else {
    echo "Aucune image trouvée (insensible à la casse).\n";
}
?>
