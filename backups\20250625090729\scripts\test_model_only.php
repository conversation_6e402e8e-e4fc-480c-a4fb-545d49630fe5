<?php
// Script pour tester uniquement le modèle VehiculeModel sans passer par l'API
// Ce script est utile pour isoler les problèmes liés au modèle

// Définir le chemin de base
define('BASE_PATH', dirname(__DIR__));

// Charger la configuration
require_once BASE_PATH . '/app/config/config.php';

// Charger le modèle VehiculeModel
require_once BASE_PATH . '/app/models/VehiculeModel.php';

// Vérifier si une référence est fournie en argument
$reference = isset($argv[1]) ? $argv[1] : 'HALF_TRACK';

echo "Test du modèle VehiculeModel pour la référence: $reference\n\n";

try {
    // Instancier le modèle
    $vehiculeModel = new VehiculeModel();
    
    // Récupérer les images du véhicule
    $images = $vehiculeModel->getVehiculeImages($reference);
    
    echo "Nombre d'images trouvées: " . count($images) . "\n\n";
    
    if (count($images) > 0) {
        echo "Images trouvées:\n";
        foreach ($images as $index => $image) {
            $absolutePath = BASE_PATH . '/' . $image;
            $exists = file_exists($absolutePath);
            echo ($index + 1) . ". " . $image . " (" . ($exists ? "Existe" : "N'existe pas") . ")\n";
        }
    } else {
        echo "Aucune image trouvée pour la référence: $reference\n";
    }
    
    // Récupérer les informations de débogage
    if (isset(VehiculeModel::$lastDebugInfo) && !empty(VehiculeModel::$lastDebugInfo)) {
        echo "\nInformations de débogage:\n";
        print_r(VehiculeModel::$lastDebugInfo);
    }
    
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
    echo "Fichier: " . $e->getFile() . " (ligne " . $e->getLine() . ")\n";
}
?>
