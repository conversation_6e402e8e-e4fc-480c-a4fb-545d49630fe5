<?php

// Définir le chemin de base s'il n'est pas déjà défini
if (!defined('BASE_PATH')) {
    define('BASE_PATH', dirname(dirname(__DIR__)));
}

// Définir le chemin vers le répertoire app s'il n'est pas déjà défini
if (!defined('APP_PATH')) {
    define('APP_PATH', BASE_PATH . '/app');
}

// Date de fin pour le compte à rebours
define('END_DATE', '2025-05-08 00:00:00');

// Date Heure Début Achat Flash
define('DEBUT_FLASH_DATE', '2025-05-10 10:00:00');
define('FIN_FLASH_DATE', '2025-05-10 17:00:00');

// Définir l'URL de base du projet
define('BASE_URL', '/');

// Informations de l'entreprise pour les documents
define('COMPANY_NAME', 'Surplus Militaires et Industriels');
define('COMPANY_ADDRESS', 'D1092 - La gare');
define('COMPANY_CITY', '38160 La Sône');
define('COMPANY_PHONE', '04 76 64 43 56');
define('COMPANY_EMAIL', '<EMAIL>');
define('COMPANY_WEBSITE', 'www.jeep-dodge-gmc.com');
define('COMPANY_SIRET', 'SIRET: 397 933 763 00013');
define('COMPANY_SLOGAN', 'Spécialiste pièces détachées et véhicules américains WWII');

// Configuration pour l'envoi d'emails
define('MAIL_FROM', '<EMAIL>');
define('MAIL_FROM_NAME', 'SMI - Jeep Dodge GMC');
define('MAIL_SUBJECT_BON_COMMANDE', 'Votre bon de commande');
define('MAIL_BODY_BON_COMMANDE', "Bonjour,\n\nVeuillez trouver ci-joint votre bon de commande.\n\nCordialement,\nL'équipe SMI - Jeep Dodge GMC");

// Configuration de la pagination
define('VEHICLES_PER_PAGE', 10); // Nombre de véhicules affichés par page

?>