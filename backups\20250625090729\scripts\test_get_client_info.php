<?php
// Définir les en-têtes pour éviter les problèmes de cache
header('Cache-Control: no-cache, must-revalidate');
header('Expires: Mon, 26 Jul 1997 05:00:00 GMT');
header('Content-Type: text/html; charset=utf-8');

// Démarrer la session
session_start();

// Inclure les fichiers de configuration
require_once __DIR__ . '/../app/config/config.php';
require_once __DIR__ . '/../app/config/database.php';

// Afficher les informations de session et cookie
echo "<h1>Test de l'API get_client_info.php</h1>";

echo "<h2>Informations de session</h2>";
echo "<pre>";
if (isset($_SESSION['USER'])) {
    echo "Session USER existe:\n";
    print_r($_SESSION['USER']);
} else {
    echo "Session USER n'existe pas\n";
}
echo "</pre>";

echo "<h2>Informations de cookie</h2>";
echo "<pre>";
if (isset($_COOKIE['USERLOG'])) {
    echo "Cookie USERLOG existe:\n";
    $cookieData = json_decode($_COOKIE['USERLOG'], true);
    print_r($cookieData);
} else {
    echo "Cookie USERLOG n'existe pas\n";
}
echo "</pre>";

// Vérifier si l'utilisateur est connecté
$isLoggedIn = isset($_SESSION['USER']) || isset($_COOKIE['USERLOG']);

if (!$isLoggedIn) {
    // Si l'utilisateur n'est pas connecté, afficher un formulaire de connexion
    echo "<h2>Vous n'êtes pas connecté</h2>";
    echo "<p>Connectez-vous pour tester l'API get_client_info.php</p>";

    echo "<form action='../utilisateur/connexion' method='post'>";
    echo "<div>";
    echo "<label for='email'>Email:</label>";
    echo "<input type='email' id='email' name='email' required>";
    echo "</div>";
    echo "<div style='margin-top: 10px;'>";
    echo "<label for='password'>Mot de passe:</label>";
    echo "<input type='password' id='password' name='password' required>";
    echo "</div>";
    echo "<div style='margin-top: 10px;'>";
    echo "<button type='submit'>Se connecter</button>";
    echo "</div>";
    echo "</form>";
} else {
    // Si l'utilisateur est connecté, afficher un bouton pour tester l'API
    echo "<h2>Tester l'API</h2>";
    echo "<button onclick='testApi()'>Tester l'API get_client_info.php</button>";
    echo "<div id='apiResult' style='margin-top: 20px; padding: 10px; border: 1px solid #ccc;'></div>";

    echo "<script>
    function testApi() {
        document.getElementById('apiResult').innerHTML = 'Chargement...';

        fetch('../app/ajax/get_client_info.php')
            .then(response => {
                document.getElementById('apiResult').innerHTML += '<p>Statut de la réponse: ' + response.status + '</p>';
                return response.text().then(text => {
                    try {
                        // Essayer de parser le texte en JSON
                        return {
                            json: JSON.parse(text),
                            text: text
                        };
                    } catch (e) {
                        // Si le parsing échoue, retourner le texte brut
                        return {
                            error: e,
                            text: text
                        };
                    }
                });
            })
            .then(data => {
                if (data.json) {
                    document.getElementById('apiResult').innerHTML += '<p>Données JSON reçues:</p><pre>' + JSON.stringify(data.json, null, 2) + '</pre>';
                } else {
                    document.getElementById('apiResult').innerHTML += '<p>Erreur de parsing JSON:</p><p>' + data.error + '</p>';
                    document.getElementById('apiResult').innerHTML += '<p>Réponse brute:</p><pre>' + data.text + '</pre>';
                }
            })
            .catch(error => {
                document.getElementById('apiResult').innerHTML += '<p style=\"color: red;\">Erreur: ' + error.message + '</p>';
            });
    }
    </script>";
}
?>

<div style="margin-top: 20px;">
    <a href="../">Retour à l'accueil</a>
</div>
