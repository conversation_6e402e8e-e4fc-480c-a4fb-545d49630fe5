<?php
// Démarrer la session si ce n'est pas déjà fait
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Charger la configuration
require_once __DIR__ . '/../app/config/config.php';

// Charger les contrôleurs
require_once __DIR__ . '/../app/controllers/PageController.php';
require_once __DIR__ . '/../app/controllers/UtilisateurController.php';

// Instancier les contrôleurs
$pageController = new PageController();
$utilisateurController = new UtilisateurController();

// Vérifier si la date actuelle est antérieure à END_DATE
$currentDate = new DateTime();
$endDate = new DateTime(END_DATE);
$showAttentePage = ($currentDate < $endDate);

// Récupérer l'URL demandée et la nettoyer
$requestUri = $_SERVER['REQUEST_URI'];
$path = parse_url($requestUri, PHP_URL_PATH);

// Retirer le chemin de base du projet de l'URL (attention à la casse)
$baseUrlPath = trim(BASE_URL, '/');
$cleanPath = preg_replace('#^/' . $baseUrlPath . '#i', '', $path);
$cleanPath = trim($cleanPath, '/');

// Convertir l'URL en minuscule
$cleanPath = strtolower($cleanPath);

// Récupérer le nom de la page avant le "?"
$pathParts = explode('?', $cleanPath);
$pageName = $pathParts[0];

// Vérifier si l'utilisateur est un administrateur
$isAdmin = false;
if (isset($_SESSION['USER']) && isset($_SESSION['USER']['role']) && $_SESSION['USER']['role'] === 'admin') {
    $isAdmin = true;
} elseif (isset($_COOKIE['USERLOG'])) {
    $userInfo = json_decode($_COOKIE['USERLOG'], true);
    if (isset($userInfo['role']) && $userInfo['role'] === 'admin') {
        $isAdmin = true;
    }
}

// Si la date actuelle est antérieure à END_DATE, afficher la page d'attente
// sauf pour certaines routes spécifiques (comme l'administration ou l'authentification)
// ou si l'utilisateur est un administrateur
if ($showAttentePage && !$isAdmin && !in_array($pageName, ['admin', 'admin.php', 'login', 'login.php', 'utilisateur/connexion', 'utilisateur/deconnexion', 'utilisateur/profil', 'utilisateur/mot_de_passe_oublie', 'utilisateur/recuperer_mot_de_passe', 'utilisateur/s_inscrire', 'utilisateur/inscription_traitement'])) {
    $pageController->showAttente();
    exit;
}
// echo $pageName; // Supprimé pour le débogage

// Gérer les différentes routes
switch ($pageName) {
    // Routes pour PageController qui nécessitent des droits d'administrateur
    case 'vehicules-liste':
    case 'vehicules-liste.php':
    case 'modifier-vehicule':
    case 'modifier-vehicule.php':
    case 'ajouter-vehicule':
    case 'ajouter-vehicule.php':
    case 'ajouter-image':
    case 'ajouter-image.php':
    case 'supprimer-image':
    case 'supprimer-image.php':
    case 'supprimer-vehicule':
    case 'supprimer-vehicule.php':
    case 'import-vehicules':
    case 'import-vehicules.php':
    case 'import-vehicules-text':
    case 'import-vehicules-text.php':
        // Vérifier si l'utilisateur est un administrateur
        if (!$isAdmin) {
            // Rediriger vers la page d'accueil si l'utilisateur n'est pas administrateur
            header('Location: ' . BASE_URL);
            exit;
        }

        // Traiter la demande si l'utilisateur est administrateur
        switch ($pageName) {
            case 'vehicules-liste':
            case 'vehicules-liste.php':
                $pageController->showVehiculesList();
                break;

            case 'modifier-vehicule':
            case 'modifier-vehicule.php':
                if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                    $pageController->updateVehicule();
                } else {
                    $pageController->showEditVehicule();
                }
                break;

            case 'ajouter-image':
            case 'ajouter-image.php':
                $pageController->addImage();
                break;

            case 'supprimer-image':
            case 'supprimer-image.php':
                $pageController->deleteImage();
                break;

            case 'supprimer-vehicule':
            case 'supprimer-vehicule.php':
                $pageController->deleteVehicule();
                break;

            case 'import-vehicules':
            case 'import-vehicules.php':
                $pageController->showImportVehicules();
                break;

            case 'ajouter-vehicule':
            case 'ajouter-vehicule.php':
                if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                    $pageController->addVehicule();
                } else {
                    $pageController->showAddVehicule();
                }
                break;

            case 'import-vehicules-text':
            case 'import-vehicules-text.php':
                $pageController->importVehiculesFromText();
                break;
        }
        break;

    case 'generer-bon':
    case 'generer-bon.php':
        // Vérifier si l'utilisateur est connecté (admin ou client)
        if (!isset($_SESSION['USER']) && !isset($_COOKIE['USERLOG'])) {
            // Rediriger vers la page d'accueil si l'utilisateur n'est pas connecté
            header('Location: ' . BASE_URL);
            exit;
        }
        $pageController->generateBonCommande();
        break;

    case 'autres':
    case 'autres.php':
    case '':
    case 'index.php':
        $pageController->show();
        break;

    case 'attente':
    case 'attente.php':
        $pageController->showAttente();
        break;

    // Routes pour UtilisateurController
    case 'utilisateur/connexion':
        $utilisateurController->connexion();
        break;

    case 'utilisateur/deconnexion':
        $utilisateurController->deconnexion();
        break;

    case 'utilisateur/profil':
        $utilisateurController->profil();
        break;

    case 'utilisateur/mot_de_passe_oublie':
        $utilisateurController->mot_de_passe_oublie();
        break;

    case 'utilisateur/recuperer_mot_de_passe':
        $utilisateurController->recuperer_mot_de_passe();
        break;

    case 'utilisateur/s_inscrire':
        $utilisateurController->s_inscrire();
        break;

    case 'utilisateur/inscription_traitement':
        $utilisateurController->inscription_traitement();
        break;

    default:
        echo "Page non trouvée";
        break;
}
