<?php
// Script pour vérifier la structure des répertoires d'images

// Définir le chemin de base
define('BASE_PATH', dirname(__DIR__));

// Répertoires à vérifier
$directories = [
    BASE_PATH . '/public/img/',
    BASE_PATH . '/public/img/autres/',
];

echo "Vérification des répertoires d'images\n";
echo "=====================================\n\n";

// Vérifier chaque répertoire
foreach ($directories as $directory) {
    echo "Répertoire: " . $directory . "\n";
    
    if (is_dir($directory)) {
        echo "  - Existe: Oui\n";
        echo "  - Permissions: " . substr(sprintf('%o', fileperms($directory)), -4) . "\n";
        
        // Lister les fichiers
        $files = scandir($directory);
        $imageCount = 0;
        
        echo "  - Contenu:\n";
        foreach ($files as $file) {
            if ($file != '.' && $file != '..') {
                $filePath = $directory . $file;
                $isDir = is_dir($filePath);
                $fileType = $isDir ? 'Répertoire' : 'Fichier';
                $fileSize = $isDir ? '-' : filesize($filePath) . ' octets';
                
                echo "    * {$fileType}: {$file} ({$fileSize})\n";
                
                if (!$isDir && preg_match('/\.(jpg|jpeg|png|gif)$/i', $file)) {
                    $imageCount++;
                }
            }
        }
        
        echo "  - Nombre d'images: {$imageCount}\n";
    } else {
        echo "  - Existe: Non\n";
    }
    
    echo "\n";
}

// Vérifier la configuration
echo "Configuration\n";
echo "=============\n\n";

// Vérifier BASE_URL
if (file_exists(BASE_PATH . '/app/config/config.php')) {
    include_once BASE_PATH . '/app/config/config.php';
    echo "BASE_URL: " . (defined('BASE_URL') ? BASE_URL : 'Non défini') . "\n";
} else {
    echo "Fichier de configuration non trouvé\n";
}

// Vérifier le document root
echo "DOCUMENT_ROOT: " . $_SERVER['DOCUMENT_ROOT'] . "\n";
echo "Script Path: " . __FILE__ . "\n";
echo "Base Path: " . BASE_PATH . "\n";

// Vérifier les chemins d'accès
echo "\nTest d'accès aux fichiers\n";
echo "=======================\n\n";

// Tester l'accès à quelques images
$testImages = [
    '/public/img/HALF TRACK.png',
    '/public/img/autres/HALF TRACK.png',
];

foreach ($testImages as $imagePath) {
    $fullPath = BASE_PATH . $imagePath;
    echo "Image: " . $imagePath . "\n";
    echo "  - Chemin complet: " . $fullPath . "\n";
    echo "  - Existe: " . (file_exists($fullPath) ? 'Oui' : 'Non') . "\n";
    if (file_exists($fullPath)) {
        echo "  - Taille: " . filesize($fullPath) . " octets\n";
        echo "  - Permissions: " . substr(sprintf('%o', fileperms($fullPath)), -4) . "\n";
    }
    echo "\n";
}

// Tester la fonction glob
echo "Test de la fonction glob\n";
echo "======================\n\n";

$testPatterns = [
    BASE_PATH . '/public/img/HALF*.{jpg,jpeg,png,gif}',
    BASE_PATH . '/public/img/HALF TRACK.{jpg,jpeg,png,gif}',
    BASE_PATH . '/public/img/autres/HALF*.{jpg,jpeg,png,gif}',
    BASE_PATH . '/public/img/autres/HALF TRACK.{jpg,jpeg,png,gif}',
];

foreach ($testPatterns as $pattern) {
    echo "Pattern: " . $pattern . "\n";
    $files = glob($pattern, GLOB_BRACE);
    
    if (!empty($files)) {
        echo "  - Résultats (" . count($files) . "):\n";
        foreach ($files as $file) {
            echo "    * " . $file . "\n";
        }
    } else {
        echo "  - Aucun résultat\n";
    }
    echo "\n";
}

echo "Fin de la vérification\n";
?>
