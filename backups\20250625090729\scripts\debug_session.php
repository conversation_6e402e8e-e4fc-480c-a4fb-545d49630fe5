<?php
// D<PERSON>marrer la session
session_start();

// Afficher le contenu de la session
echo "<h2>Contenu de la session</h2>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

// Afficher le contenu du cookie USERLOG
echo "<h2>Contenu du cookie USERLOG</h2>";
if (isset($_COOKIE['USERLOG'])) {
    echo "<pre>";
    $userlog = json_decode($_COOKIE['USERLOG'], true);
    print_r($userlog);
    echo "</pre>";
} else {
    echo "Le cookie USERLOG n'existe pas.";
}

// Afficher les informations de base de données pour l'utilisateur connecté
if (isset($_SESSION['USER']) || isset($_COOKIE['USERLOG'])) {
    // Récupérer l'email de l'utilisateur (clé principale)
    $email = isset($_SESSION['USER']) ? $_SESSION['USER']['email'] : $userlog['email'];

    echo "<h2>Vérification en base de données</h2>";

    // Charger la configuration de la base de données
    require_once __DIR__ . '/../app/config/database.php';

    // Connexion à la base de données
    $conn = getDbConnection();

    // Vérifier si l'utilisateur existe dans la table clients
    $stmt = $conn->prepare("SELECT * FROM clients WHERE emailclient = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();

    echo "<h3>Résultat de la requête clients</h3>";
    if ($result->num_rows > 0) {
        echo "<pre>";
        print_r($result->fetch_assoc());
        echo "</pre>";
    } else {
        echo "Aucun utilisateur trouvé avec l'email: " . $email;
    }

    // Vérifier si l'adresse existe dans la table adresses_facturation
    $stmt = $conn->prepare("SELECT * FROM adresses_facturation WHERE af_emailclient = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();

    echo "<h3>Résultat de la requête adresses_facturation</h3>";
    if ($result->num_rows > 0) {
        echo "<pre>";
        print_r($result->fetch_assoc());
        echo "</pre>";
    } else {
        echo "Aucune adresse trouvée pour l'email: " . $email;
    }

    // Fermer la connexion
    $conn->close();
} else {
    echo "<h2>Aucun utilisateur connecté</h2>";
}
?>
