<?php
// Script pour vérifier la configuration PHP
// Ce script est conçu pour fonctionner sur cPanel

echo "Vérification de la configuration PHP\n";
echo "=================================\n\n";

echo "Version PHP: " . phpversion() . "\n";
echo "OS: " . PHP_OS . "\n";
echo "SAPI: " . php_sapi_name() . "\n\n";

echo "Chemins:\n";
echo "- Chemin du script: " . __FILE__ . "\n";
echo "- Répertoire du script: " . __DIR__ . "\n";
echo "- Répertoire parent: " . dirname(__DIR__) . "\n\n";

echo "Inclusions:\n";
$configFile = dirname(__DIR__) . '/app/config/config.php';
$databaseFile = dirname(__DIR__) . '/app/config/database.php';
$modelFile = dirname(__DIR__) . '/app/models/VehiculeModel.php';

echo "- Fichier config.php: " . (file_exists($configFile) ? "Existe" : "N'existe pas") . "\n";
echo "- Fichier database.php: " . (file_exists($databaseFile) ? "Existe" : "N'existe pas") . "\n";
echo "- Fichier VehiculeModel.php: " . (file_exists($modelFile) ? "Existe" : "N'existe pas") . "\n\n";

echo "Test d'inclusion:\n";
try {
    require_once $configFile;
    echo "- config.php inclus avec succès\n";
    
    if (defined('BASE_URL')) {
        echo "  - BASE_URL: " . BASE_URL . "\n";
    } else {
        echo "  - BASE_URL non défini\n";
    }
} catch (Exception $e) {
    echo "- Erreur lors de l'inclusion de config.php: " . $e->getMessage() . "\n";
}

try {
    require_once $databaseFile;
    echo "- database.php inclus avec succès\n";
} catch (Exception $e) {
    echo "- Erreur lors de l'inclusion de database.php: " . $e->getMessage() . "\n";
}

try {
    require_once $modelFile;
    echo "- VehiculeModel.php inclus avec succès\n";
} catch (Exception $e) {
    echo "- Erreur lors de l'inclusion de VehiculeModel.php: " . $e->getMessage() . "\n";
}

echo "\nTest de connexion à la base de données:\n";
try {
    $conn = getDbConnection();
    if ($conn) {
        echo "- Connexion à la base de données établie\n";
        
        // Vérifier si la table vehicules existe
        $result = $conn->query("SHOW TABLES LIKE 'vehicules'");
        if ($result->num_rows > 0) {
            echo "- Table vehicules existe\n";
            
            // Vérifier la structure de la table
            $result = $conn->query("DESCRIBE vehicules");
            $columns = [];
            while ($row = $result->fetch_assoc()) {
                $columns[] = $row['Field'];
            }
            
            echo "- Colonnes de la table vehicules: " . implode(", ", $columns) . "\n";
            
            // Vérifier si les colonnes importantes existent
            $importantColumns = ['id', 'statut', 'client', 'rgltreference', 'dateheureachat', 'rgltdateheure'];
            foreach ($importantColumns as $column) {
                echo "  - Colonne $column: " . (in_array($column, $columns) ? "Existe" : "N'existe pas") . "\n";
            }
            
            // Compter le nombre de véhicules
            $result = $conn->query("SELECT COUNT(*) as count FROM vehicules");
            $row = $result->fetch_assoc();
            echo "- Nombre de véhicules dans la base de données: " . $row['count'] . "\n";
        } else {
            echo "- Table vehicules n'existe pas\n";
        }
    } else {
        echo "- Erreur: Impossible de se connecter à la base de données\n";
    }
} catch (Exception $e) {
    echo "- Erreur lors de la connexion à la base de données: " . $e->getMessage() . "\n";
}

echo "\nVérification terminée.\n";
?>
