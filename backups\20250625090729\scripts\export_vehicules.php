<?php
// Script pour exporter la structure et les données de la table vehicules

// Charger les paramètres de connexion depuis le fichier de configuration
require_once __DIR__ . '/../app/config/database.php';

// Paramètres de connexion à la base de données
$host = DB_HOST;
$dbname = DB_NAME;
$username = DB_USER;
$password = DB_PASS;

try {
    // Connexion à la base de données
    $pdo = new PDO("mysql:host=$host;dbname=$dbname", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);

    // Récupérer la structure de la table
    $stmt = $pdo->query('SHOW CREATE TABLE vehicules');
    $row = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "-- Structure de la table vehicules\n";
    echo $row['Create Table'] . ";\n\n";

    // Récupérer les données de la table
    $stmt = $pdo->query('SELECT * FROM vehicules');
    $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

    if (count($rows) > 0) {
        echo "-- Données de la table vehicules\n";
        echo "INSERT INTO vehicules (";

        // Récupérer les noms des colonnes
        $columns = array_keys($rows[0]);
        echo implode(', ', $columns);

        echo ") VALUES\n";

        // Générer les valeurs pour chaque ligne
        $rowValues = [];
        foreach ($rows as $row) {
            $values = [];
            foreach ($row as $value) {
                if ($value === null) {
                    $values[] = 'NULL';
                } elseif (is_numeric($value)) {
                    $values[] = $value;
                } else {
                    $values[] = "'" . addslashes($value) . "'";
                }
            }
            $rowValues[] = "(" . implode(', ', $values) . ")";
        }

        echo implode(",\n", $rowValues) . ";";
    } else {
        echo "-- Aucune donnée dans la table vehicules";
    }

} catch(PDOException $e) {
    echo "Erreur: " . $e->getMessage();
}
?>
