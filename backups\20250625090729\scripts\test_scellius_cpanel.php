<?php
// Script pour tester la notification Scellius sur cPanel
// Ce script simule une notification de paiement réussie

// Définir les chemins manuellement
$basePath = dirname(__DIR__);
$appPath = $basePath . '/app';

// Inclure les fichiers nécessaires
require_once $appPath . '/config/config.php';
require_once $appPath . '/config/database.php';

// Fonction pour afficher un message
function output($message) {
    echo $message . "\n";
}

// Fonction pour journaliser les informations
function logMessage($message, $data = null) {
    global $basePath;
    
    $logFile = $basePath . '/logs/test_scellius.log';
    $logMessage = date('Y-m-d H:i:s') . " - " . $message;
    
    if ($data !== null) {
        $logMessage .= " - " . json_encode($data);
    }
    
    file_put_contents($logFile, $logMessage . PHP_EOL, FILE_APPEND);
}

// Vérifier si un ID de véhicule est fourni en argument
$vehiculeId = isset($argv[1]) ? intval($argv[1]) : null;
$clientEmail = isset($argv[2]) ? $argv[2] : '<EMAIL>';

if (!$vehiculeId) {
    output("Erreur: Veuillez fournir un ID de véhicule en argument");
    output("Usage: php test_scellius_cpanel.php [ID_VEHICULE] [EMAIL_CLIENT]");
    exit(1);
}

output("Test de notification Scellius pour le véhicule ID: $vehiculeId et client: $clientEmail");
logMessage("Démarrage du test pour véhicule ID: $vehiculeId et client: $clientEmail");

// Établir une connexion à la base de données
$conn = getDbConnection();
if (!$conn) {
    output("Erreur: Impossible de se connecter à la base de données");
    logMessage("Erreur: Impossible de se connecter à la base de données");
    exit(1);
}

output("Connexion à la base de données établie");
logMessage("Connexion à la base de données établie");

// Récupérer les informations du véhicule
$sql = "SELECT * FROM vehicules WHERE id = ?";
$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $vehiculeId);
$stmt->execute();
$result = $stmt->get_result();
$vehicule = $result->fetch_assoc();

if (!$vehicule) {
    output("Erreur: Véhicule ID $vehiculeId non trouvé");
    logMessage("Erreur: Véhicule ID $vehiculeId non trouvé");
    exit(1);
}

output("Informations du véhicule:");
output("ID: " . $vehicule['id']);
output("Référence: " . $vehicule['reference']);
output("Libellé: " . $vehicule['libelle']);
output("Statut actuel: " . ($vehicule['statut'] ?? 'Non défini'));
output("Client actuel: " . ($vehicule['client'] ?? 'Aucun'));
output("Référence de paiement: " . ($vehicule['rgltreference'] ?? 'Aucune'));

logMessage("Informations du véhicule récupérées", $vehicule);

// Générer un ID de transaction unique
$transId = 'TEST' . date('YmdHis') . rand(1000, 9999);

// Simuler les données de notification Scellius
$notificationData = [
    'vads_trans_id' => $transId,
    'vads_order_id' => $vehicule['reference'],
    'vads_trans_status' => 'AUTHORISED',
    'vads_cust_email' => $clientEmail,
    'vads_order_info' => $vehicule['libelle']
];

logMessage("Données de notification simulées", $notificationData);

// Vérifier si le champ rgltdateheure existe
$checkSql = "SHOW COLUMNS FROM vehicules LIKE 'rgltdateheure'";
$checkResult = $conn->query($checkSql);
$rgltdateheureExists = $checkResult->num_rows > 0;

logMessage("Vérification du champ rgltdateheure", ['exists' => $rgltdateheureExists]);

// Mettre à jour le véhicule directement
output("Tentative de mise à jour du véhicule...");
logMessage("Tentative de mise à jour du véhicule");

try {
    if ($rgltdateheureExists) {
        $updateSql = "UPDATE vehicules SET 
                      statut = 'Vendu', 
                      client = ?, 
                      rgltreference = ?, 
                      dateheureachat = NOW(),
                      rgltdateheure = NOW()
                      WHERE id = ?";
    } else {
        $updateSql = "UPDATE vehicules SET 
                      statut = 'Vendu', 
                      client = ?, 
                      rgltreference = ?, 
                      dateheureachat = NOW()
                      WHERE id = ?";
    }

    $updateStmt = $conn->prepare($updateSql);
    $updateStmt->bind_param("ssi", $clientEmail, $transId, $vehiculeId);
    $updateResult = $updateStmt->execute();

    if ($updateResult) {
        output("SUCCÈS: Véhicule ID $vehiculeId mis à jour avec succès");
        logMessage("SUCCÈS: Véhicule ID $vehiculeId mis à jour avec succès");
        
        // Vérifier que la mise à jour a bien été effectuée
        $checkSql = "SELECT * FROM vehicules WHERE id = ?";
        $checkStmt = $conn->prepare($checkSql);
        $checkStmt->bind_param("i", $vehiculeId);
        $checkStmt->execute();
        $checkResult = $checkStmt->get_result();
        $updatedVehicule = $checkResult->fetch_assoc();
        
        output("Informations du véhicule après mise à jour:");
        output("ID: " . $updatedVehicule['id']);
        output("Statut: " . ($updatedVehicule['statut'] ?? 'Non défini'));
        output("Client: " . ($updatedVehicule['client'] ?? 'Aucun'));
        output("Référence de paiement: " . ($updatedVehicule['rgltreference'] ?? 'Aucune'));
        output("Date/heure d'achat: " . ($updatedVehicule['dateheureachat'] ?? 'Non définie'));
        
        if ($rgltdateheureExists) {
            output("Date/heure de règlement: " . ($updatedVehicule['rgltdateheure'] ?? 'Non définie'));
        }
        
        logMessage("Véhicule après mise à jour", $updatedVehicule);
    } else {
        output("ERREUR: Échec de la mise à jour du véhicule");
        output("Erreur MySQL: " . $conn->error);
        logMessage("ERREUR: Échec de la mise à jour du véhicule", ['mysql_error' => $conn->error]);
    }
} catch (Exception $e) {
    output("EXCEPTION: " . $e->getMessage());
    logMessage("EXCEPTION lors de la mise à jour", [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine()
    ]);
}

output("Test terminé");
logMessage("Test terminé");
?>
