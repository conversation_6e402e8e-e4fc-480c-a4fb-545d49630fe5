<?php
// Démarrer la session si ce n'est pas déjà fait
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Ajouter le style spécifique à cette page
$data['additional_head_content'] = '
<style>
    .countdown-container {
        position: relative;
        width: 100%;
        margin: 0 auto;
        text-align: center;
        display: flex;
        flex-direction: column;
    }

    .countdown-overlay {
        position: relative;
        background: rgba(0, 0, 0, 0.8);
        padding: 20px;
        color: white;
        text-align: center;
        width: 100%;
        transform: none;
        left: 0;
        top: 0;
    }

    .countdown-image-container {
        margin-top: 20px;
        width: 100%;
        max-width: 800px;
        margin-left: auto;
        margin-right: auto;
        height: calc(100vh - 400px);
    }

    .countdown-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
    }

    .countdown-title {
        font-family: \'Oswald\', sans-serif;
        font-size: 24px;
        margin-bottom: 15px;
        color: #CCCC66;
    }

    .countdown-timer {
        display: flex;
        justify-content: center;
        gap: 20px;
    }

    .countdown-item {
        text-align: center;
    }

    .countdown-number {
        font-family: \'Merriweather\', serif;
        font-size: 36px;
        font-weight: bold;
        color: #CCCC66;
    }

    .countdown-label {
        font-family: \'Oswald\', sans-serif;
        font-size: 14px;
        text-transform: uppercase;
        color: #fff;
    }
</style>';

// Ajouter le script de compte à rebours
$data['additional_footer_scripts'] = '
<script>
    // Fonction pour mettre à jour le compte à rebours
    function updateCountdown() {
        const endDate = new Date(\'' . END_DATE . '\');
        const now = new Date();
        const diff = endDate - now;

        if (diff <= 0) {
            // Redirection vers la page des véhicules si la date est passée
            window.location.href = \'/autres\';
            return;
        }

        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

        document.getElementById(\'days\').textContent = days;
        document.getElementById(\'hours\').textContent = hours;
        document.getElementById(\'minutes\').textContent = minutes;
    }

    // Mise à jour initiale
    updateCountdown();

    // Mise à jour toutes les minutes
    setInterval(updateCountdown, 60000);
</script>';

// Inclure le header commun
require_once __DIR__ . '/../templates/header.php';
?>

    <div id="site">
        <div id="middle">
            <div class="form-style-10">

                <div class="countdown-container">
                    <div class="countdown-overlay">
                        <div class="countdown-title">VENTE FLASH du 8 mai 2025 <br>Véhicules visibles dans</div>
                        <div class="countdown-timer">
                            <div class="countdown-item">
                                <div class="countdown-number" id="days">--</div>
                                <div class="countdown-label">Jours</div>
                            </div>
                            <div class="countdown-item">
                                <div class="countdown-number" id="hours">--</div>
                                <div class="countdown-label">Heures</div>
                            </div>
                            <div class="countdown-item">
                                <div class="countdown-number" id="minutes">--</div>
                                <div class="countdown-label">Minutes</div>
                            </div>
                        </div>
                    </div>
                    <div class="countdown-image-container">
                        <img src="public/img/site/wait-for-see.jpg" alt="En attente" class="countdown-image">
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php
// Inclure le footer commun
require_once __DIR__ . '/../templates/footer.php';
?>