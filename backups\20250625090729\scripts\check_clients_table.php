<?php
// Charger la configuration de la base de données
require_once __DIR__ . '/../app/config/database.php';

// Connexion à la base de données des clients
$conn = getClientsDbConnection();

// Vérifier si la table clients existe
$tableExists = false;
$result = $conn->query("SHOW TABLES LIKE 'clients'");
if ($result->num_rows > 0) {
    $tableExists = true;
    echo "La table 'clients' existe.\n";
} else {
    echo "La table 'clients' n'existe pas.\n";
}

// Si la table existe, vérifier sa structure
if ($tableExists) {
    $result = $conn->query("DESCRIBE clients");
    echo "Structure de la table 'clients':\n";
    while ($row = $result->fetch_assoc()) {
        echo "- " . $row['Field'] . " (" . $row['Type'] . ")\n";
    }
}

// Fermer la connexion
$conn->close();
?>
