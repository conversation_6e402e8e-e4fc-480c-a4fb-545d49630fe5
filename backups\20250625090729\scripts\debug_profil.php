<?php
// Démarrer la session
session_start();

// Charger la configuration
require_once __DIR__ . '/../app/config/config.php';
require_once __DIR__ . '/../app/config/database.php';

// Charger le contrôleur
require_once __DIR__ . '/../app/controllers/UtilisateurController.php';

// Créer une instance du contrôleur
$controller = new UtilisateurController();

// Vérifier si l'utilisateur est connecté
if (!isset($_SESSION['USER']) && !isset($_COOKIE['USERLOG'])) {
    echo "Aucun utilisateur connecté. Veuillez vous connecter d'abord.";
    exit;
}

// Si l'utilisateur est connecté via cookie mais pas via session
if (!isset($_SESSION['USER']) && isset($_COOKIE['USERLOG'])) {
    $_SESSION['USER'] = json_decode($_COOKIE['USERLOG'], true);
}

// Afficher les informations de l'utilisateur
echo "<h2>Informations de l'utilisateur</h2>";
echo "<pre>";
print_r($_SESSION['USER']);
echo "</pre>";

// Récupérer les informations complètes de l'utilisateur
$email = $_SESSION['USER']['email'];

// Connexion à la base de données
$conn = getDbConnection();

// Vérifier si l'utilisateur existe dans la table clients
$stmt = $conn->prepare("SELECT * FROM clients WHERE emailclient = ?");
$stmt->bind_param("s", $email);
$stmt->execute();
$result = $stmt->get_result();

echo "<h2>Résultat de la requête clients</h2>";
if ($result->num_rows > 0) {
    $client = $result->fetch_assoc();
    echo "<pre>";
    print_r($client);
    echo "</pre>";

    // Récupérer l'adresse de facturation du client
    $stmt = $conn->prepare("SELECT * FROM adresses_facturation WHERE af_emailclient = ?");
    $stmt->bind_param("s", $email);
    $stmt->execute();
    $adresseResult = $stmt->get_result();

    echo "<h2>Résultat de la requête adresses_facturation</h2>";
    if ($adresseResult->num_rows > 0) {
        $adresse = $adresseResult->fetch_assoc();
        echo "<pre>";
        print_r($adresse);
        echo "</pre>";
    } else {
        echo "Aucune adresse trouvée pour l'email: " . $email;
    }

    // Essayer d'afficher la vue du profil
    echo "<h2>Tentative d'affichage de la vue du profil</h2>";
    try {
        // Préparer les données pour la vue
        $data = [
            'title' => 'Mon Profil - Jeep Dodge GMC',
            'description' => 'Gérez votre profil utilisateur',
            'client' => $client,
            'adresse' => isset($adresse) ? $adresse : null
        ];

        // Extraire les données pour les rendre disponibles dans la vue
        extract($data);

        // Inclure la vue
        $viewPath = __DIR__ . '/../app/views/utilisateur/profil.php';
        if (file_exists($viewPath)) {
            echo "La vue existe à l'emplacement: " . $viewPath;
            echo "<hr>";
            include $viewPath;
        } else {
            echo "La vue n'existe pas à l'emplacement: " . $viewPath;
        }
    } catch (Exception $e) {
        echo "Erreur lors de l'affichage de la vue: " . $e->getMessage();
    }
} else {
    echo "Aucun utilisateur trouvé avec l'email: " . $email;
}

// Fermer la connexion
$conn->close();
?>
