<?php
// Charger la configuration de la base de données
require_once __DIR__ . '/../app/config/database.php';

// Connexion à la base de données des clients
$conn = getClientsDbConnection();

// Vérifier si la table adresses_client existe
$tableExists = false;
$result = $conn->query("SHOW TABLES LIKE 'adresses_client'");
if ($result->num_rows > 0) {
    $tableExists = true;
    echo "La table 'adresses_client' existe.\n";
} else {
    echo "La table 'adresses_client' n'existe pas.\n";
}

// Si la table existe, vérifier sa structure
if ($tableExists) {
    $result = $conn->query("DESCRIBE adresses_client");
    echo "Structure de la table 'adresses_client':\n";
    while ($row = $result->fetch_assoc()) {
        echo "- " . $row['Field'] . " (" . $row['Type'] . ")\n";
    }
}

// Fermer la connexion
$conn->close();
?>
