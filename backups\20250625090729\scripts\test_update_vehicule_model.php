<?php
// Script pour tester la mise à jour d'un véhicule en utilisant le modèle VehiculeModel modifié

// Activer l'affichage des erreurs pour le débogage
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Définir les chemins manuellement
$basePath = dirname(__DIR__);
$appPath = $basePath . '/app';

// Inclure les fichiers nécessaires
require_once $appPath . '/config/config.php';
require_once $appPath . '/config/database.php';
require_once $appPath . '/models/VehiculeModel.php';

// Fonction pour afficher un message
function output($message) {
    echo $message . "\n";
}

// Vérifier si un ID de véhicule est fourni en argument
$vehiculeId = isset($argv[1]) ? intval($argv[1]) : null;

// Si aucun ID n'est fourni, prendre le premier véhicule de la base
if (!$vehiculeId) {
    output("Aucun ID de véhicule fourni, utilisation du premier véhicule de la base");
    
    // Établir une connexion à la base de données
    $conn = getDbConnection();
    
    // Récupérer le premier véhicule
    $sql = "SELECT id FROM vehicules LIMIT 1";
    $result = $conn->query($sql);
    
    if ($result && $result->num_rows > 0) {
        $row = $result->fetch_assoc();
        $vehiculeId = $row['id'];
        output("Véhicule ID $vehiculeId sélectionné automatiquement");
    } else {
        output("Erreur: Aucun véhicule trouvé dans la base de données");
        exit(1);
    }
    
    $conn->close();
}

output("=== Test de mise à jour du véhicule ID: $vehiculeId avec le modèle VehiculeModel ===");

// Créer une instance du modèle VehiculeModel
$vehiculeModel = new VehiculeModel();

// Récupérer les informations du véhicule
$vehicule = $vehiculeModel->getVehiculeById($vehiculeId);

if (!$vehicule) {
    output("Erreur: Véhicule ID $vehiculeId non trouvé");
    exit(1);
}

output("Informations du véhicule avant mise à jour:");
output("ID: " . $vehicule['id']);
output("Référence: " . $vehicule['reference']);
output("Libellé: " . $vehicule['libelle']);
output("Descriptif: " . $vehicule['descriptif']);
output("Prix: " . $vehicule['prix'] . " €");
output("Statut actuel: " . ($vehicule['statut'] ?? 'Non défini'));
output("Client actuel: " . ($vehicule['client'] ?? 'Aucun'));
output("Référence de paiement: " . ($vehicule['rgltreference'] ?? 'Aucune'));

// Préparer les données pour la mise à jour
$data = $vehicule;
$data['libelle'] = $vehicule['libelle'] . ' (Test modèle ' . date('Y-m-d H:i:s') . ')';

output("\nTentative de mise à jour du véhicule avec les données suivantes:");
output("Nouveau libellé: " . $data['libelle']);

// Utiliser la méthode updateVehicule du modèle
$result = $vehiculeModel->updateVehicule($vehiculeId, $data);

if ($result) {
    output("\nSUCCÈS: Véhicule ID $vehiculeId mis à jour avec succès");
    
    // Vérifier que la mise à jour a bien été effectuée
    $updatedVehicule = $vehiculeModel->getVehiculeById($vehiculeId);
    
    output("\nInformations du véhicule après mise à jour:");
    output("ID: " . $updatedVehicule['id']);
    output("Référence: " . $updatedVehicule['reference']);
    output("Libellé: " . $updatedVehicule['libelle']);
    output("Descriptif: " . $updatedVehicule['descriptif']);
    output("Prix: " . $updatedVehicule['prix'] . " €");
    output("Statut: " . ($updatedVehicule['statut'] ?? 'Non défini'));
    output("Client: " . ($updatedVehicule['client'] ?? 'Aucun'));
    output("Référence de paiement: " . ($updatedVehicule['rgltreference'] ?? 'Aucune'));
    
    if ($updatedVehicule['libelle'] === $data['libelle']) {
        output("\nVérification: La mise à jour a fonctionné correctement.");
    } else {
        output("\nVérification: La mise à jour n'a pas fonctionné correctement.");
        output("Libellé attendu: " . $data['libelle']);
        output("Libellé actuel: " . $updatedVehicule['libelle']);
    }
} else {
    output("\nERREUR: Échec de la mise à jour du véhicule");
}

output("\n=== Test terminé ===");
?>
