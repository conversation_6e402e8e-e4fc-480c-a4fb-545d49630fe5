<?php
// Script pour tester l'API vehicule-images.php avec plus de détails sur les erreurs
// Ce script exécute l'API directement et affiche les erreurs PHP

// Activer l'affichage des erreurs
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Définir le chemin de base
define('BASE_PATH', dirname(__DIR__));

// Fonction pour afficher un message coloré
function coloredOutput($message, $type = 'info') {
    $colors = [
        'info' => "\033[0;36m", // Cyan
        'success' => "\033[0;32m", // Vert
        'warning' => "\033[0;33m", // Jaune
        'error' => "\033[0;31m", // Rouge
        'reset' => "\033[0m" // Réinitialiser
    ];
    
    echo $colors[$type] . $message . $colors['reset'] . PHP_EOL;
}

// Vérifier si une référence est fournie en argument
$reference = isset($argv[1]) ? $argv[1] : 'HALF_TRACK';

coloredOutput("Test de l'API vehicule-images.php pour la référence: $reference", 'info');
echo PHP_EOL;

// Vérifier si le fichier API existe
$apiFile = BASE_PATH . '/api/vehicule-images.php';
if (!file_exists($apiFile)) {
    coloredOutput("Erreur: Le fichier API n'existe pas: $apiFile", 'error');
    exit(1);
}

// Vérifier si le fichier de configuration existe
$configFile = BASE_PATH . '/app/config/config.php';
if (!file_exists($configFile)) {
    coloredOutput("Erreur: Le fichier de configuration n'existe pas: $configFile", 'error');
    exit(1);
}

// Vérifier si le modèle VehiculeModel existe
$modelFile = BASE_PATH . '/app/models/VehiculeModel.php';
if (!file_exists($modelFile)) {
    coloredOutput("Erreur: Le fichier du modèle n'existe pas: $modelFile", 'error');
    exit(1);
}

// Afficher les informations sur l'environnement
coloredOutput("Informations sur l'environnement:", 'info');
echo "PHP Version: " . phpversion() . PHP_EOL;
echo "OS: " . PHP_OS . PHP_EOL;
echo "SAPI: " . php_sapi_name() . PHP_EOL;
echo "BASE_PATH: " . BASE_PATH . PHP_EOL;
echo PHP_EOL;

// Charger la configuration
require_once $configFile;
echo "BASE_URL: " . (defined('BASE_URL') ? BASE_URL : 'Non défini') . PHP_EOL;
echo PHP_EOL;

// Tester l'API directement
coloredOutput("Test direct de l'API:", 'info');

// Sauvegarder la valeur actuelle de $_GET
$originalGet = $_GET;

// Définir la référence pour l'API
$_GET['reference'] = $reference;

// Capturer les erreurs
$errors = [];
set_error_handler(function($errno, $errstr, $errfile, $errline) use (&$errors) {
    $errors[] = [
        'type' => $errno,
        'message' => $errstr,
        'file' => $errfile,
        'line' => $errline
    ];
    return true;
});

// Capturer la sortie de l'API
ob_start();
try {
    include $apiFile;
} catch (Throwable $e) {
    echo json_encode([
        'error' => 'Exception: ' . $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ]);
}
$apiResponse = ob_get_clean();

// Restaurer le gestionnaire d'erreurs
restore_error_handler();

// Restaurer $_GET
$_GET = $originalGet;

// Afficher les erreurs
if (!empty($errors)) {
    coloredOutput("Erreurs PHP:", 'error');
    foreach ($errors as $error) {
        echo "Type: " . $error['type'] . PHP_EOL;
        echo "Message: " . $error['message'] . PHP_EOL;
        echo "Fichier: " . $error['file'] . PHP_EOL;
        echo "Ligne: " . $error['line'] . PHP_EOL;
        echo PHP_EOL;
    }
}

// Décoder la réponse JSON
$apiData = json_decode($apiResponse, true);

if ($apiData) {
    coloredOutput("Réponse de l'API (décodée):", 'success');
    echo json_encode($apiData, JSON_PRETTY_PRINT) . PHP_EOL;
} else {
    coloredOutput("Erreur lors du décodage de la réponse JSON", 'error');
    coloredOutput("Réponse brute:", 'error');
    echo $apiResponse . PHP_EOL;
}

// Tester le modèle directement
coloredOutput("Test direct du modèle VehiculeModel:", 'info');

// Charger le modèle
require_once $modelFile;

// Instancier le modèle
try {
    $vehiculeModel = new VehiculeModel();
    
    // Récupérer les images du véhicule
    $images = $vehiculeModel->getVehiculeImages($reference);
    
    coloredOutput("Images trouvées par le modèle:", 'success');
    echo "Nombre d'images: " . count($images) . PHP_EOL;
    
    if (count($images) > 0) {
        foreach ($images as $index => $image) {
            $absolutePath = BASE_PATH . '/' . $image;
            $exists = file_exists($absolutePath);
            echo ($index + 1) . ". " . $image . " (" . ($exists ? "Existe" : "N'existe pas") . ")" . PHP_EOL;
        }
    } else {
        coloredOutput("Aucune image trouvée", 'warning');
    }
} catch (Throwable $e) {
    coloredOutput("Exception lors de l'utilisation du modèle:", 'error');
    echo "Message: " . $e->getMessage() . PHP_EOL;
    echo "Fichier: " . $e->getFile() . PHP_EOL;
    echo "Ligne: " . $e->getLine() . PHP_EOL;
}

echo PHP_EOL;
coloredOutput("Fin du test", 'info');
?>
