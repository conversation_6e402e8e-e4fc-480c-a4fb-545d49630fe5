<?php
// Vérifier si la constante BASE_URL est définie
if (defined('BASE_URL')) {
    echo "La constante BASE_URL est définie: " . BASE_URL;
} else {
    echo "La constante BASE_URL n'est pas définie.";

    // Essayer de charger les fichiers de configuration
    $configFiles = [
        __DIR__ . '/../app/config/config.php',
        __DIR__ . '/../config/config.php',
        __DIR__ . '/../app/config.php',
        __DIR__ . '/../config.php'
    ];

    foreach ($configFiles as $file) {
        if (file_exists($file)) {
            echo "<br>Fichier de configuration trouvé: " . $file;
            include $file;

            if (defined('BASE_URL')) {
                echo "<br>Après inclusion, BASE_URL est définie: " . BASE_URL;
            } else {
                echo "<br>Après inclusion, BASE_URL n'est toujours pas définie.";
            }

            break;
        }
    }
}
?>
