<?php
// Activer la gestion des erreurs pour ce script
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Fonction pour journaliser les erreurs
function logError($message, $context = []) {
    $logFile = dirname(__DIR__) . '/logs/api_errors.log';
    $logDir = dirname($logFile);

    // Créer le répertoire de logs s'il n'existe pas
    if (!is_dir($logDir)) {
        @mkdir($logDir, 0777, true);
    }

    // Formater le message d'erreur
    $timestamp = date('Y-m-d H:i:s');
    $contextStr = !empty($context) ? ' ' . json_encode($context) : '';
    $logMessage = "[{$timestamp}] {$message}{$contextStr}" . PHP_EOL;

    // Écrire dans le fichier de log
    @file_put_contents($logFile, $logMessage, FILE_APPEND);

    // En mode CLI, afficher l'erreur dans STDERR pour ne pas interférer avec la sortie JSON
    if (php_sapi_name() === 'cli') {
        // Utiliser STDERR au lieu de STDOUT pour les messages de débogage
        fwrite(STDERR, "[{$timestamp}] {$message}" . PHP_EOL);
        if (!empty($context)) {
            fwrite(STDERR, "Contexte: " . json_encode($context, JSON_PRETTY_PRINT) . PHP_EOL);
        }
    }
}

// Gestionnaire d'exceptions
function handleException($e) {
    $errorDetails = [
        'message' => $e->getMessage(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'trace' => $e->getTraceAsString()
    ];

    logError('Exception: ' . $e->getMessage(), $errorDetails);

    // Vérifier si nous sommes en mode CLI
    $isCLI = (php_sapi_name() === 'cli');

    // Préparer la réponse d'erreur
    $errorResponse = [
        'error' => 'Une erreur est survenue lors du traitement de la requête.',
        'images' => [],
        'debug' => $isCLI ? $errorDetails : null
    ];

    // Retourner une réponse d'erreur
    if (!$isCLI) {
        header('Content-Type: application/json');
    }

    echo json_encode($errorResponse);
    exit;
}

// Définir le gestionnaire d'erreurs
set_error_handler(function($errno, $errstr, $errfile, $errline) {
    // Journaliser l'erreur
    logError("Erreur PHP: $errstr", [
        'errno' => $errno,
        'file' => $errfile,
        'line' => $errline
    ]);

    // Ne pas afficher l'erreur
    return true;
});

// Définir le gestionnaire d'exceptions
set_exception_handler('handleException');

try {
    // Vérifier si nous sommes en mode CLI ou HTTP
    $isCLI = (php_sapi_name() === 'cli');

    // Définir les en-têtes pour permettre l'accès AJAX (seulement en mode HTTP)
    if (!$isCLI) {
        header('Content-Type: application/json');
        header('Access-Control-Allow-Origin: *');
    }

    // Vérifier si APP_PATH est défini
    if (!defined('APP_PATH')) {
        define('APP_PATH', dirname(__DIR__) . '/app');
    }

    // Vérifier si BASE_PATH est défini
    if (!defined('BASE_PATH')) {
        define('BASE_PATH', dirname(__DIR__));
    }

    // Charger la configuration
    require_once APP_PATH . '/config/config.php';

    // Vérifier que BASE_URL est défini
    if (!defined('BASE_URL')) {
        define('BASE_URL', '/');
        logError('BASE_URL non défini, utilisation de la valeur par défaut: /');
    }

    // Charger le modèle VehiculeModel
    require_once APP_PATH . '/models/VehiculeModel.php';

    // Vérifier si la référence est fournie
    if (!isset($_GET['reference']) || empty($_GET['reference'])) {
        echo json_encode(['images' => [], 'error' => 'Référence non fournie']);
        exit;
    }

    $reference = $_GET['reference'];
    logError('Requête reçue pour la référence: ' . $reference . ' (Mode: ' . ($isCLI ? 'CLI' : 'HTTP') . ')');

    // Instancier le modèle
    $vehiculeModel = new VehiculeModel();

    // Récupérer les images du véhicule
    $images = $vehiculeModel->getVehiculeImages($reference);

    // Récupérer les informations de débogage
    $modelDebugInfo = isset(VehiculeModel::$lastDebugInfo) ? VehiculeModel::$lastDebugInfo : [];

    // Si aucune image n'est trouvée, utiliser l'image par défaut
    if (empty($images)) {
        $searchRef = strtoupper(str_replace('_', ' ', $reference));
        $defaultImage = "/img/" . $searchRef . ".png";
        $images = [$defaultImage];
        logError('Aucune image trouvée, utilisation de l\'image par défaut: ' . $defaultImage);
    } else {
        logError('Images trouvées: ' . count($images), ['images' => $images]);
    }

    // Encoder les URLs pour gérer les espaces
    $encodedImages = array_map(function($image) {
        // Préserver le chemin original sans modification
        $originalPath = $image;

        // Vérifier si le chemin contient déjà "public/img"
        if (strpos($originalPath, 'public/img') === 0) {
            // Le chemin est déjà correct, juste ajouter BASE_URL
            $image = BASE_URL . ltrim($originalPath, '/');
            return htmlspecialchars($image);
        }

        // Pour les autres cas, appliquer les transformations standard
        // Supprimer le /public si présent
        $image = str_replace('/public/', '/', $image);

        // S'assurer que le chemin commence par /
        if (function_exists('str_starts_with')) {
            if (!str_starts_with($image, '/')) {
                $image = '/' . $image;
            }
        } else {
            // Compatibilité PHP < 8.0
            if (substr($image, 0, 1) !== '/') {
                $image = '/' . $image;
            }
        }

        // Ajouter BASE_URL et échapper les caractères spéciaux
        // Utiliser ltrim pour éviter les doubles slashes
        $image = htmlspecialchars(BASE_URL . ltrim($image, '/'));
        return $image;
    }, $images);

    // Ajouter des informations de débogage à la réponse JSON
    $debug = [
        'timestamp' => date('Y-m-d H:i:s'),
        'reference' => $reference,
        'count' => count($encodedImages),
        'images' => $encodedImages,
        'model_debug' => $modelDebugInfo,
        'files_check' => []
    ];

    // Vérifier si les fichiers existent réellement
    foreach ($encodedImages as $key => $image) {
        // Extraire le chemin relatif de l'image à partir de l'URL complète
        $relativePath = str_replace(BASE_URL, '', $image);
        $relativePath = ltrim($relativePath, '/');

        // Corriger le problème de séparateur de répertoire manquant
        // Remplacer "publicimg" par "public/img" si nécessaire
        if (strpos($relativePath, 'publicimg') === 0) {
            $relativePath = str_replace('publicimg', 'public/img', $relativePath);
        }

        // Construire le chemin absolu
        $absolutePath = dirname(__DIR__) . '/' . $relativePath;

        // Vérifier si le fichier existe
        $exists = @file_exists($absolutePath);

        // Si le fichier n'existe pas, essayer de trouver une alternative
        if (!$exists) {
            // Essayer avec le chemin original du modèle
            $originalPath = $images[$key] ?? '';
            if (!empty($originalPath)) {
                $originalAbsolutePath = dirname(__DIR__) . '/' . $originalPath;
                $originalExists = @file_exists($originalAbsolutePath);

                if ($originalExists) {
                    // Utiliser le chemin original qui fonctionne
                    $encodedImages[$key] = BASE_URL . ltrim($originalPath, '/');
                    $exists = true;
                    $absolutePath = $originalAbsolutePath;
                    $relativePath = $originalPath;
                    logError('Utilisation du chemin original qui existe: ' . $originalPath);
                    continue;
                }
            }

            // Extraire le nom du fichier
            $pathInfo = pathinfo($relativePath);
            $fileName = $pathInfo['filename'] ?? '';
            $extension = isset($pathInfo['extension']) ? $pathInfo['extension'] : 'png';

            // Essayer de trouver le fichier dans le répertoire img
            $alternativePath = dirname(__DIR__) . '/public/img/' . $fileName . '.' . $extension;
            $alternativeExists = @file_exists($alternativePath);

            if ($alternativeExists) {
                // Remplacer l'URL dans le tableau des images
                $encodedImages[$key] = BASE_URL . 'public/img/' . $fileName . '.' . $extension;
                $exists = true;
                $absolutePath = $alternativePath;
                $relativePath = 'public/img/' . $fileName . '.' . $extension;
                logError('Alternative trouvée pour ' . $image . ': ' . $encodedImages[$key]);
            }
        }

        // Ajouter des informations de débogage
        $debug['files_check'][$relativePath] = [
            'exists' => $exists ? "Existe" : "N'existe pas",
            'absolute_path' => $absolutePath,
            'base_url' => BASE_URL,
            'relative_path' => $relativePath,
            'original_url' => $image,
            'original_model_path' => $images[$key] ?? 'N/A'
        ];
    }

    // Vérifier si nous avons des images valides
    $hasValidImages = false;

    // Si nous avons des informations de vérification de fichiers
    if (!empty($debug['files_check'])) {
        foreach ($debug['files_check'] as $check) {
            if (isset($check['exists']) && $check['exists'] === "Existe") {
                $hasValidImages = true;
                break;
            }
        }
    } else {
        // Si nous n'avons pas d'informations de vérification, mais que nous avons des images
        // Considérer que les images sont valides (confiance au modèle)
        $hasValidImages = !empty($encodedImages);
    }

    // Si aucune image valide n'a été trouvée, ajouter l'image par défaut
    if (empty($encodedImages) || !$hasValidImages) {
        $defaultImagePath = BASE_URL . 'public/img/image_not_found.png';
        $encodedImages = [$defaultImagePath];
        logError('Aucune image valide trouvée, utilisation de l\'image par défaut');
    } else {
        logError('Images valides trouvées: ' . count($encodedImages));
    }

    // Ajouter les informations de débogage à la réponse
    $response = [
        'images' => $encodedImages,
        'debug' => $debug
    ];

    // Retourner la réponse complète au format JSON
    echo json_encode($response);

} catch (Exception $e) {
    handleException($e);
}
?>
