<?php
// Charger la configuration de la base de données
require_once __DIR__ . '/../app/config/database.php';
require_once __DIR__ . '/../app/models/VehiculeModel.php';

// Définir la durée maximale d'un processus d'achat (en minutes)
$maxPurchaseDuration = 10; // 10 minutes par défaut

// Vérifier si une durée est spécifiée en ligne de commande
if (isset($argv[1]) && is_numeric($argv[1]) && $argv[1] > 0) {
    $maxPurchaseDuration = (int)$argv[1];
    echo "Durée maximale définie à $maxPurchaseDuration minutes.\n";
} else {
    echo "Utilisation de la durée maximale par défaut: $maxPurchaseDuration minutes.\n";
    echo "Pour spécifier une autre durée: php reset_abandoned_purchases.php <minutes>\n";
}

// Connexion à la base de données
$conn = getDbConnection();

// Récupérer les véhicules en "Achat en cours" dont le processus a été démarré il y a plus de $maxPurchaseDuration minutes
$sql = "SELECT id, libelle, client, dateheureachat
        FROM vehicules
        WHERE statut = 'Achat en cours'
        AND dateheureachat IS NOT NULL
        AND dateheureachat < DATE_SUB(NOW(), INTERVAL ? MINUTE)";

$stmt = $conn->prepare($sql);
$stmt->bind_param("i", $maxPurchaseDuration);
$stmt->execute();
$result = $stmt->get_result();

if ($result->num_rows > 0) {
    echo "Véhicules à réinitialiser trouvés : " . $result->num_rows . "\n";

    // Initialiser le modèle de véhicule
    $vehiculeModel = new VehiculeModel();

    // Parcourir les véhicules et les réinitialiser
    while ($row = $result->fetch_assoc()) {
        $id = $row['id'];
        $title = $row['libelle'];
        $client = $row['client'];
        $dateheureachat = $row['dateheureachat'];

        // Calculer le temps écoulé depuis le début du processus d'achat
        $startTime = new DateTime($dateheureachat);
        $now = new DateTime();
        $interval = $startTime->diff($now);
        $minutesElapsed = ($interval->days * 24 * 60) + ($interval->h * 60) + $interval->i;

        echo "Réinitialisation du véhicule ID $id, titre: $title, client: $client, démarré le: $dateheureachat (il y a $minutesElapsed minutes)\n";

        if ($vehiculeModel->resetVehiculeStatus($id)) {
            echo "  -> SUCCÈS: Statut réinitialisé à 'Disponible'\n";
        } else {
            echo "  -> ERREUR: Échec de la réinitialisation\n";
        }
    }
} else {
    echo "Aucun véhicule à réinitialiser trouvé.\n";
}

// Fermer la connexion
$conn->close();

echo "\nScript terminé. Les véhicules en 'Achat en cours' depuis plus de $maxPurchaseDuration minutes ont été réinitialisés.\n";
echo "Exécutez ce script régulièrement pour éviter que des véhicules ne restent bloqués en 'Achat en cours'.\n";
?>
