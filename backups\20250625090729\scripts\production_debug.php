<?php
// Script de diagnostic pour le serveur de production
// Ce script affiche des informations utiles sans dépendre des logs serveur

// Activer l'affichage des erreurs pour ce script uniquement
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

// Définir le chemin de base
define('BASE_PATH', dirname(__DIR__));

// Charger la configuration
if (file_exists(BASE_PATH . '/app/config/config.php')) {
    include_once BASE_PATH . '/app/config/config.php';
}

// Fonction pour vérifier si un fichier existe et est lisible
function check_file($path) {
    $result = [
        'exists' => file_exists($path),
        'readable' => is_readable($path),
        'size' => file_exists($path) ? filesize($path) : 0,
        'permissions' => file_exists($path) ? substr(sprintf('%o', fileperms($path)), -4) : 'N/A'
    ];
    return $result;
}

// Informations sur l'environnement
$environment = [
    'php_version' => phpversion(),
    'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
    'document_root' => $_SERVER['DOCUMENT_ROOT'] ?? 'Unknown',
    'script_filename' => $_SERVER['SCRIPT_FILENAME'] ?? 'Unknown',
    'base_path' => BASE_PATH,
    'base_url' => defined('BASE_URL') ? BASE_URL : 'Not defined',
    'operating_system' => PHP_OS,
    'glob_available' => function_exists('glob'),
    'curl_available' => function_exists('curl_init')
];

// Vérifier les répertoires importants
$directories = [
    'public/img' => BASE_PATH . '/public/img',
    'public/img/autres' => BASE_PATH . '/public/img/autres'
];

$dir_checks = [];
foreach ($directories as $name => $path) {
    $dir_checks[$name] = [
        'exists' => is_dir($path),
        'readable' => is_readable($path),
        'writable' => is_writable($path),
        'permissions' => is_dir($path) ? substr(sprintf('%o', fileperms($path)), -4) : 'N/A'
    ];

    // Si le répertoire existe, compter les fichiers
    if (is_dir($path) && is_readable($path)) {
        $files = scandir($path);
        $file_count = 0;
        $image_count = 0;

        foreach ($files as $file) {
            if ($file != '.' && $file != '..') {
                $file_count++;
                if (preg_match('/\.(jpg|jpeg|png|gif)$/i', $file)) {
                    $image_count++;
                }
            }
        }

        $dir_checks[$name]['file_count'] = $file_count;
        $dir_checks[$name]['image_count'] = $image_count;
    }
}

// Tester la fonction glob avec différents patterns
$test_patterns = [
    'HALF_TRACK' => [
        BASE_PATH . '/public/img/HALF*.{jpg,jpeg,png,gif}',
        BASE_PATH . '/public/img/HALF TRACK.{jpg,jpeg,png,gif}',
        BASE_PATH . '/public/img/autres/HALF*.{jpg,jpeg,png,gif}',
        BASE_PATH . '/public/img/autres/HALF TRACK.{jpg,jpeg,png,gif}'
    ],
    'DODGE_1' => [
        BASE_PATH . '/public/img/DODGE_1*.{jpg,jpeg,png,gif}',
        BASE_PATH . '/public/img/DODGE_1 (*).{jpg,jpeg,png,gif}',
        BASE_PATH . '/public/img/DODGE 1*.{jpg,jpeg,png,gif}'
    ]
];

$glob_tests = [];
foreach ($test_patterns as $reference => $patterns) {
    $glob_tests[$reference] = [];
    foreach ($patterns as $pattern) {
        $files = glob($pattern, GLOB_BRACE);
        $glob_tests[$reference][$pattern] = [
            'count' => count($files),
            'files' => array_map('basename', $files)
        ];
    }
}

// Tester l'API vehicule-images.php directement
$test_references = ['HALF_TRACK', 'DODGE_1'];
$api_tests = [];

foreach ($test_references as $reference) {
    // Charger le modèle VehiculeModel
    if (file_exists(BASE_PATH . '/app/models/VehiculeModel.php')) {
        require_once BASE_PATH . '/app/models/VehiculeModel.php';

        // Instancier le modèle
        $vehiculeModel = new VehiculeModel();

        // Récupérer les images du véhicule
        $images = $vehiculeModel->getVehiculeImages($reference);

        $api_tests[$reference] = [
            'model_result' => [
                'count' => count($images),
                'images' => $images
            ]
        ];

        // Si l'API est accessible, tester aussi via l'API
        if (function_exists('curl_init')) {
            // Utiliser une URL absolue pour le test en ligne de commande
            $api_url = 'http://localhost/api/vehicule-images.php?reference=' . urlencode($reference);

            // Si nous sommes dans un environnement web, essayer de construire une URL plus précise
            if (isset($_SERVER['HTTP_HOST'])) {
                $protocol = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ? 'https://' : 'http://';
                $host = $_SERVER['HTTP_HOST'];
                $path = rtrim(dirname($_SERVER['PHP_SELF']), '/scripts');
                $api_url = $protocol . $host . $path . '/api/vehicule-images.php?reference=' . urlencode($reference);
            }

            // Tester l'API directement en incluant le fichier
            ob_start();
            $_GET['reference'] = $reference;
            include_once(BASE_PATH . '/api/vehicule-images.php');
            $direct_response = ob_get_clean();
            $direct_result = json_decode($direct_response, true);

            $api_tests[$reference]['api_direct_result'] = [
                'method' => 'Direct include',
                'response' => $direct_result,
                'raw_response' => substr($direct_response, 0, 500) . (strlen($direct_response) > 500 ? '...' : '')
            ];

            // Essayer aussi avec curl pour comparer
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $api_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_TIMEOUT, 5);
            $response = curl_exec($ch);
            $info = curl_getinfo($ch);
            $error = curl_error($ch);
            curl_close($ch);

            $api_tests[$reference]['api_curl_result'] = [
                'url' => $api_url,
                'http_code' => $info['http_code'],
                'curl_error' => $error,
                'response' => json_decode($response, true),
                'raw_response' => substr($response, 0, 500) . (strlen($response) > 500 ? '...' : '')
            ];
        }
    } else {
        $api_tests[$reference] = [
            'error' => 'VehiculeModel.php not found'
        ];
    }
}

// Préparer la réponse
$result = [
    'environment' => $environment,
    'directories' => $dir_checks,
    'glob_tests' => $glob_tests,
    'api_tests' => $api_tests
];

// Afficher les résultats
header('Content-Type: text/html');
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Diagnostic de Production</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 {
            color: #2c3e50;
        }
        pre {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            overflow-x: auto;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .success {
            color: #28a745;
        }
        .warning {
            color: #ffc107;
        }
        .error {
            color: #dc3545;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            background-color: #fff;
            border-radius: 5px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <h1>Diagnostic de Production pour la Galerie d'Images</h1>
    <p>Ce rapport fournit des informations détaillées pour aider à diagnostiquer les problèmes de la galerie d'images.</p>

    <div class="section">
        <h2>Informations sur l'Environnement</h2>
        <table>
            <?php foreach ($environment as $key => $value): ?>
                <tr>
                    <th><?php echo htmlspecialchars(ucwords(str_replace('_', ' ', $key))); ?></th>
                    <td><?php echo htmlspecialchars($value); ?></td>
                </tr>
            <?php endforeach; ?>
        </table>
    </div>

    <div class="section">
        <h2>Vérification des Répertoires</h2>
        <table>
            <tr>
                <th>Répertoire</th>
                <th>Existe</th>
                <th>Lisible</th>
                <th>Inscriptible</th>
                <th>Permissions</th>
                <th>Nombre de Fichiers</th>
                <th>Nombre d'Images</th>
            </tr>
            <?php foreach ($dir_checks as $name => $check): ?>
                <tr>
                    <td><?php echo htmlspecialchars($name); ?></td>
                    <td class="<?php echo $check['exists'] ? 'success' : 'error'; ?>">
                        <?php echo $check['exists'] ? 'Oui' : 'Non'; ?>
                    </td>
                    <td class="<?php echo $check['readable'] ? 'success' : 'error'; ?>">
                        <?php echo $check['readable'] ? 'Oui' : 'Non'; ?>
                    </td>
                    <td class="<?php echo $check['writable'] ? 'success' : 'warning'; ?>">
                        <?php echo $check['writable'] ? 'Oui' : 'Non'; ?>
                    </td>
                    <td><?php echo htmlspecialchars($check['permissions']); ?></td>
                    <td><?php echo isset($check['file_count']) ? $check['file_count'] : 'N/A'; ?></td>
                    <td><?php echo isset($check['image_count']) ? $check['image_count'] : 'N/A'; ?></td>
                </tr>
            <?php endforeach; ?>
        </table>
    </div>

    <div class="section">
        <h2>Tests de la Fonction glob()</h2>
        <?php foreach ($glob_tests as $reference => $tests): ?>
            <h3>Référence: <?php echo htmlspecialchars($reference); ?></h3>
            <table>
                <tr>
                    <th>Pattern</th>
                    <th>Nombre de Fichiers</th>
                    <th>Fichiers Trouvés</th>
                </tr>
                <?php foreach ($tests as $pattern => $result): ?>
                    <tr>
                        <td><?php echo htmlspecialchars($pattern); ?></td>
                        <td class="<?php echo $result['count'] > 0 ? 'success' : 'warning'; ?>">
                            <?php echo $result['count']; ?>
                        </td>
                        <td>
                            <?php if ($result['count'] > 0): ?>
                                <ul>
                                    <?php foreach ($result['files'] as $file): ?>
                                        <li><?php echo htmlspecialchars($file); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            <?php else: ?>
                                <span class="warning">Aucun fichier trouvé</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                <?php endforeach; ?>
            </table>
        <?php endforeach; ?>
    </div>

    <div class="section">
        <h2>Tests de l'API</h2>
        <?php foreach ($api_tests as $reference => $test): ?>
            <h3>Référence: <?php echo htmlspecialchars($reference); ?></h3>

            <?php if (isset($test['error'])): ?>
                <div class="error">
                    <p>Erreur: <?php echo htmlspecialchars($test['error']); ?></p>
                </div>
            <?php else: ?>
                <h4>Résultat du Modèle</h4>
                <table>
                    <tr>
                        <th>Nombre d'Images</th>
                        <td class="<?php echo $test['model_result']['count'] > 0 ? 'success' : 'warning'; ?>">
                            <?php echo $test['model_result']['count']; ?>
                        </td>
                    </tr>
                    <tr>
                        <th>Images Trouvées</th>
                        <td>
                            <?php if ($test['model_result']['count'] > 0): ?>
                                <ul>
                                    <?php foreach ($test['model_result']['images'] as $image): ?>
                                        <li><?php echo htmlspecialchars($image); ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            <?php else: ?>
                                <span class="warning">Aucune image trouvée</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>

                <?php if (isset($test['api_result'])): ?>
                    <h4>Résultat de l'API</h4>
                    <table>
                        <tr>
                            <th>URL</th>
                            <td><?php echo htmlspecialchars($test['api_result']['url']); ?></td>
                        </tr>
                        <tr>
                            <th>Code HTTP</th>
                            <td class="<?php echo $test['api_result']['http_code'] == 200 ? 'success' : 'error'; ?>">
                                <?php echo $test['api_result']['http_code']; ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Réponse</th>
                            <td>
                                <pre><?php echo htmlspecialchars(json_encode($test['api_result']['response'], JSON_PRETTY_PRINT)); ?></pre>
                            </td>
                        </tr>
                    </table>
                <?php endif; ?>
            <?php endif; ?>
        <?php endforeach; ?>
    </div>
</body>
</html>
