<?php
// Charger la configuration de la base de données
require_once __DIR__ . '/../app/config/database.php';

// Connexion à la base de données
$conn = getDbConnection();

// Vérifier si la colonne dateheureachat existe déjà
$result = $conn->query("SHOW COLUMNS FROM vehicules LIKE 'dateheureachat'");
if ($result->num_rows == 0) {
    // La colonne n'existe pas, l'ajouter
    $sql = "ALTER TABLE vehicules ADD COLUMN dateheureachat DATETIME NULL";
    if ($conn->query($sql) === TRUE) {
        echo "La colonne 'dateheureachat' a été ajoutée avec succès à la table vehicules.\n";
    } else {
        echo "Erreur lors de l'ajout de la colonne 'dateheureachat': " . $conn->error . "\n";
    }
} else {
    echo "La colonne 'dateheureachat' existe déjà dans la table vehicules.\n";
}

// Fermer la connexion
$conn->close();
?>
