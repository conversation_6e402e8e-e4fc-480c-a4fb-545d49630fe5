<?php
// Désactiver l'affichage des erreurs pour éviter de corrompre le JSON
ini_set('display_errors', 0);
error_reporting(E_ALL);

// Démarrer la session si ce n'est pas déjà fait
if (session_status() === PHP_SESSION_NONE) {
    session_start();
}

// Définir l'en-tête JSON dès le début
header('Content-Type: application/json');

// Inclure les fichiers de configuration
try {
    require_once __DIR__ . '/../config/config.php';
    require_once __DIR__ . '/../config/database.php';
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => 'Erreur de configuration: ' . $e->getMessage()]);
    exit;
}

// Vérifier si l'utilisateur est connecté
if (!isset($_SESSION['USER']) && !isset($_COOKIE['USERLOG'])) {
    // Renvoyer une réponse d'erreur
    echo json_encode(['success' => false, 'message' => 'Utilisateur non connecté']);
    exit;
}

// Si l'utilisateur est connecté via cookie mais pas via session
if (!isset($_SESSION['USER']) && isset($_COOKIE['USERLOG'])) {
    $_SESSION['USER'] = json_decode($_COOKIE['USERLOG'], true);
}

// Vérifier si l'email est présent dans la session
if (!isset($_SESSION['USER']['email'])) {
    echo json_encode(['success' => false, 'message' => 'Email non trouvé dans la session']);
    exit;
}

// Récupérer l'email de l'utilisateur (clé principale)
$email = $_SESSION['USER']['email'];

// Ajouter des logs pour le débogage
error_log("Récupération des informations pour l'email: " . $email);

// Connexion à la base de données des clients
try {
    $db = getClientsDbConnection();
} catch (Exception $e) {
    error_log("Erreur de connexion à la base de données des clients: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Erreur de connexion à la base de données des clients']);
    exit;
}

// Récupérer les informations du client
try {
    $stmt = $db->prepare("SELECT * FROM clients WHERE emailclient = ? LIMIT 1");
    if (!$stmt) {
        throw new Exception($db->error);
    }

    $stmt->bind_param("s", $email);
    $stmt->execute();
    $result = $stmt->get_result();

    if ($result->num_rows === 0) {
        error_log("Aucun client trouvé pour l'email: " . $email);
        echo json_encode(['success' => false, 'message' => 'Client non trouvé']);
        exit;
    }

    error_log("Client trouvé pour l'email: " . $email);
    $client = $result->fetch_assoc();
} catch (Exception $e) {
    error_log("Erreur lors de la récupération du client: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Erreur lors de la récupération des informations client']);
    exit;
}

// Récupérer l'adresse de facturation
try {
    $stmt = $db->prepare("SELECT * FROM adresses_facturation WHERE af_emailclient = ? LIMIT 1");
    if (!$stmt) {
        throw new Exception($db->error);
    }

    $stmt->bind_param("s", $email);
    $stmt->execute();
    $adresseResult = $stmt->get_result();

    $adresse = null;
    if ($adresseResult->num_rows > 0) {
        $adresse = $adresseResult->fetch_assoc();
    }
} catch (Exception $e) {
    error_log("Erreur lors de la récupération de l'adresse: " . $e->getMessage());
    // On continue même si l'adresse n'est pas trouvée
}

// Préparer la réponse
try {
    $response = [
        'success' => true,
        'client' => [
            'nom' => $client['nomclient'] ?? 'Non renseigné',
            'prenom' => $client['prenomclient'] ?? 'Non renseigné',
            'email' => $client['emailclient'] ?? 'Non renseigné',
            'telephone' => $client['telclient'] ?? 'Non renseigné'
        ],
        'hasAdresse' => ($adresse !== null)
    ];

    // Log des informations client pour débogage
    error_log("Informations client: " . json_encode($response['client']));

    // Ajouter l'adresse si elle existe
    if ($adresse !== null) {
        $response['adresse'] = [
            'raisonSociale' => $adresse['af_raisonsocial'] ?? 'Non renseigné',
            'adresse' => $adresse['af_nomrue'] ?? 'Non renseigné',
            'codePostal' => $adresse['af_codepostal'] ?? 'Non renseigné',
            'ville' => $adresse['af_ville'] ?? 'Non renseigné',
            'pays' => $adresse['af_pays'] ?? 'Non renseigné'
        ];

        // Log des informations d'adresse pour débogage
        error_log("Informations adresse: " . json_encode($response['adresse']));
    } else {
        error_log("Aucune adresse trouvée pour le client");
    }

    // Fermer la connexion à la base de données
    $db->close();

    // Renvoyer la réponse
    echo json_encode($response);
} catch (Exception $e) {
    error_log("Erreur lors de la préparation de la réponse: " . $e->getMessage());
    echo json_encode(['success' => false, 'message' => 'Erreur lors de la préparation de la réponse']);
}
