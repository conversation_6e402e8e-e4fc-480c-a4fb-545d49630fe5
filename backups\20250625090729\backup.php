<?php

$source = __DIR__;
$backupDir = __DIR__ . '/backups';
if (!is_dir($backupDir)) {
    if (!mkdir($backupDir, 0755, true)) {
        die('Erreur lors de la création du répertoire de sauvegarde...');
    }
}
$dest = $backupDir . '/' . date('YmdHis');

function recurse_copy($src, $dst) {
    $dir = opendir($src);
    if (!$dir) {
        return;
    }
    if (!mkdir($dst, 0755, true) && !is_dir($dst)) {
        return;
    }
    while (false !== ($file = readdir($dir))) {
        if (($file != '.') && ($file != '..') && ($file != 'backups')) {
            $srcPath = $src . '/' . $file;
            $dstPath = $dst . '/' . $file;

            if (is_dir($srcPath)) {
                recurse_copy($srcPath, $dstPath);
            } else {
                if (!copy($srcPath, $dstPath)) {
                    echo "Erreur lors de la copie de $srcPath vers $dstPath\n";
                }
            }
        }
    }
    closedir($dir);
}

recurse_copy($source, $dest);

echo "Sauvegarde effectuée dans : " . $dest;

?>
