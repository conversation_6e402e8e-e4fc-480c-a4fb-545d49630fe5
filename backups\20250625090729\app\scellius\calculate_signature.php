<?php
// Activer les en-têtes CORS pour permettre les requêtes AJAX depuis le même domaine
header("Access-Control-Allow-Origin: *");
header("Content-Type: application/json; charset=UTF-8");
header("Access-Control-Allow-Methods: POST");
header("Access-Control-Allow-Headers: Content-Type, Access-Control-Allow-Headers, Authorization, X-Requested-With");

// Inclure la configuration Scellius
require_once 'scellius_module/config.php';

// Fonction pour calculer la signature
function calculate_scellius_signature(array $fields, string $secretKey, string $algo = SCELLIUS_SIGN_ALGO): string {
    error_log(print_r($fields, true));
    ksort($fields);
    $stringToSign = '';
    foreach ($fields as $key => $value) {
        if (strpos($key, 'vads_') === 0) {
            $stringToSign .= $value . '+';
        }
    }
    $stringToSign .= $secretKey;
    error_log($stringToSign);
    return base64_encode(hash_hmac(strtolower(str_replace('-', '', $algo)), $stringToSign, $secretKey, true));
}

// Récupérer les données JSON envoyées
$data = json_decode(file_get_contents("php://input"), true);

if ($data) {
    // Calculer la signature
    $signature = calculate_scellius_signature($data, SCELLIUS_SECRET_KEY);
    
    // Renvoyer la signature
    echo json_encode(['signature' => $signature]);
} else {
    http_response_code(400);
    echo json_encode(['error' => 'Données invalides']);
}