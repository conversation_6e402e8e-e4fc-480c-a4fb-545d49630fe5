<?php

// Créer une nouvelle image vide avec les dimensions d'origine
$width = 800;  // dimensions approximatives d'origine
$height = 600; // dimensions approximatives d'origine
$image = imagecreatetruecolor($width, $height);

// Créer un dégradé de gris clair
$grey = imagecolorallocate($image, 240, 240, 240);
imagefill($image, 0, 0, $grey);

// Ajouter le texte
$text = "En attente";
$black = imagecolorallocate($image, 0, 0, 0);

// Centrer le texte (utiliser une police intégrée)
$font = 5; // Plus grande police intégrée
$text_width = imagefontwidth($font) * strlen($text);
$text_height = imagefontheight($font);
$text_x = ($width - $text_width) / 2;
$text_y = ($height - $text_height) / 2;

imagestring($image, $font, $text_x, $text_y, $text, $black);

// Sauvegarder l'image
$output_path = __DIR__ . '/../public/img/site/wait-for-see.jpg';
imagejpeg($image, $output_path, 95);

// Libérer la mémoire
imagedestroy($image);

echo "L'image a été restaurée avec succès.\n";
?>