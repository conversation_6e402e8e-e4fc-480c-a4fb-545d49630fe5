<?php
// Script pour vérifier la compatibilité PHP
// Ce script vérifie si certaines fonctions PHP sont disponibles

echo "Vérification de la compatibilité PHP\n";
echo "=================================\n\n";

echo "Version PHP: " . phpversion() . "\n";
echo "OS: " . PHP_OS . "\n";
echo "SAPI: " . php_sapi_name() . "\n\n";

// Vérifier les fonctions
$functions = [
    'str_starts_with',
    'str_ends_with',
    'str_contains',
    'array_key_first',
    'array_key_last',
    'json_encode',
    'json_decode',
    'file_exists',
    'glob',
    'pathinfo',
    'dirname',
    'basename',
    'htmlspecialchars',
    'ob_start',
    'ob_get_clean',
    'set_error_handler',
    'set_exception_handler',
    'error_reporting',
    'ini_set'
];

echo "Vérification des fonctions:\n";
foreach ($functions as $function) {
    echo "- $function: " . (function_exists($function) ? "Disponible" : "Non disponible") . "\n";
}

echo "\nVérification des constantes:\n";
$constants = [
    'PHP_VERSION',
    'PHP_OS',
    'PHP_EOL',
    'DIRECTORY_SEPARATOR',
    'PATH_SEPARATOR',
    'JSON_PRETTY_PRINT',
    'E_ALL',
    'E_ERROR',
    'E_WARNING',
    'E_NOTICE'
];

foreach ($constants as $constant) {
    echo "- $constant: " . (defined($constant) ? "Définie" : "Non définie") . "\n";
}

echo "\nVérification des classes:\n";
$classes = [
    'Exception',
    'ErrorException',
    'DateTime',
    'DateTimeZone',
    'stdClass',
    'DirectoryIterator',
    'RecursiveDirectoryIterator',
    'RecursiveIteratorIterator'
];

foreach ($classes as $class) {
    echo "- $class: " . (class_exists($class) ? "Disponible" : "Non disponible") . "\n";
}

echo "\nVérification des extensions:\n";
$extensions = [
    'json',
    'mbstring',
    'curl',
    'fileinfo',
    'gd',
    'exif',
    'mysqli',
    'pdo',
    'pdo_mysql',
    'openssl',
    'zip'
];

foreach ($extensions as $extension) {
    echo "- $extension: " . (extension_loaded($extension) ? "Chargée" : "Non chargée") . "\n";
}

echo "\nVérification des paramètres PHP:\n";
$settings = [
    'display_errors',
    'error_reporting',
    'max_execution_time',
    'memory_limit',
    'post_max_size',
    'upload_max_filesize',
    'allow_url_fopen'
];

foreach ($settings as $setting) {
    echo "- $setting: " . ini_get($setting) . "\n";
}

echo "\nVérification terminée.\n";
?>
