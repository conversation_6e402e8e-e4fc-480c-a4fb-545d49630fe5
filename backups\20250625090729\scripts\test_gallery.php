<?php
// Définir les en-têtes pour permettre l'accès AJAX
header('Content-Type: text/html');

// Vérifier si APP_PATH est défini
if (!defined('APP_PATH')) {
    define('APP_PATH', dirname(__DIR__) . '/app');
}

// Charger le modèle VehiculeModel
require_once 'app/models/VehiculeModel.php';

// Référence à tester
$reference = isset($_GET['reference']) ? $_GET['reference'] : 'HALF_TRACK';

// Instancier le modèle
$vehiculeModel = new VehiculeModel();

// Récupérer les images du véhicule
$images = $vehiculeModel->getVehiculeImages($reference);

// Si aucune image n'est trouvée, utiliser l'image par défaut
if (empty($images)) {
    // Construire le chemin de l'image par défaut
    $searchRef = strtoupper(str_replace('_', ' ', $reference));
    $defaultImage = "public/img/" . $searchRef . ".png";
    $images = [$defaultImage];
}

// Ajouter le chemin complet pour chaque image
$baseUrl = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? "https://" : "http://") . $_SERVER['HTTP_HOST'] . "/";
$fullPathImages = [];
foreach ($images as $image) {
    // S'assurer que le chemin commence par "/"
    if (strpos($image, '/') !== 0 && strpos($image, 'http') !== 0) {
        $image = '/' . $image;
    }
    $fullPathImages[] = $baseUrl . ltrim($image, '/');
}
$images = $fullPathImages;

// Vérifier si les fichiers existent réellement
$fileCheck = [];
foreach ($images as $image) {
    $filePath = str_replace($baseUrl, '', $image);
    $absolutePath = $_SERVER['DOCUMENT_ROOT'] . '/' . $filePath;
    $exists = file_exists($absolutePath);
    $fileCheck[$filePath] = $exists ? "Existe" : "N'existe pas";
}
?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Galerie</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        h1 {
            color: #333;
        }
        .debug-info {
            background-color: #f5f5f5;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }
        .image-card {
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        .image-card img {
            width: 100%;
            height: 300px;
            object-fit: contain;
            background-color: #f9f9f9;
        }
        .image-info {
            padding: 10px;
        }
        .exists {
            color: green;
        }
        .not-exists {
            color: red;
        }
        .gallery {
            margin-top: 30px;
        }
        .gallery-container {
            position: relative;
            max-width: 800px;
            margin: 0 auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            overflow: hidden;
        }
        .gallery-image {
            width: 100%;
            height: 500px;
            object-fit: contain;
            background-color: #f9f9f9;
        }
        .gallery-nav {
            display: flex;
            justify-content: space-between;
            padding: 10px;
            background-color: #f5f5f5;
        }
        button {
            padding: 5px 15px;
            background-color: #4CAF50;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
    </style>
</head>
<body>
    <h1>Test Galerie pour <?php echo htmlspecialchars($reference); ?></h1>
    
    <div class="debug-info">
        <h2>Informations de débogage</h2>
        <p><strong>Référence:</strong> <?php echo htmlspecialchars($reference); ?></p>
        <p><strong>Nombre d'images trouvées:</strong> <?php echo count($images); ?></p>
        
        <h3>Vérification des fichiers:</h3>
        <ul>
            <?php foreach ($fileCheck as $path => $status): ?>
                <li>
                    <span class="<?php echo $status === 'Existe' ? 'exists' : 'not-exists'; ?>">
                        <?php echo htmlspecialchars($path); ?>: <?php echo $status; ?>
                    </span>
                </li>
            <?php endforeach; ?>
        </ul>
    </div>
    
    <h2>Images trouvées</h2>
    <div class="image-grid">
        <?php foreach ($images as $index => $image): ?>
            <div class="image-card">
                <img src="<?php echo htmlspecialchars($image); ?>" alt="Image <?php echo $index + 1; ?>">
                <div class="image-info">
                    <p><strong>URL:</strong> <?php echo htmlspecialchars($image); ?></p>
                    <p><strong>Index:</strong> <?php echo $index; ?></p>
                </div>
            </div>
        <?php endforeach; ?>
    </div>
    
    <div class="gallery">
        <h2>Test de la galerie</h2>
        <div class="gallery-container">
            <img id="galleryImage" class="gallery-image" src="<?php echo htmlspecialchars($images[0] ?? ''); ?>" alt="Image de la galerie">
            <div class="gallery-nav">
                <button id="prevBtn">Précédent</button>
                <span id="counter">1 / <?php echo count($images); ?></span>
                <button id="nextBtn">Suivant</button>
            </div>
        </div>
    </div>
    
    <script>
        // Variables pour la galerie
        let currentIndex = 0;
        const images = <?php echo json_encode($images); ?>;
        const galleryImage = document.getElementById('galleryImage');
        const counter = document.getElementById('counter');
        const prevBtn = document.getElementById('prevBtn');
        const nextBtn = document.getElementById('nextBtn');
        
        // Fonction pour afficher une image
        function displayImage(index) {
            if (images.length === 0) return;
            
            // S'assurer que l'index est valide
            if (index < 0) index = images.length - 1;
            if (index >= images.length) index = 0;
            
            currentIndex = index;
            galleryImage.src = images[currentIndex];
            counter.textContent = `${currentIndex + 1} / ${images.length}`;
            
            console.log('Affichage de l\'image:', images[currentIndex]);
        }
        
        // Événements pour les boutons
        prevBtn.addEventListener('click', () => {
            displayImage(currentIndex - 1);
        });
        
        nextBtn.addEventListener('click', () => {
            displayImage(currentIndex + 1);
        });
        
        // Gestion des erreurs d'image
        galleryImage.addEventListener('error', function() {
            console.error('Erreur de chargement de l\'image:', this.src);
            this.src = 'public/img/image_not_found.png';
            this.alt = 'Image non disponible';
        });
    </script>
</body>
</html>
