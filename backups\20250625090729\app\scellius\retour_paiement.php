<?php
// Inclure la configuration
require_once 'scellius_module/config.php';

// Fonction pour recalculer la signature (identique à celle dans index.php)
function calculate_scellius_signature(array $fields, string $secretKey, string $algo = SCELLIUS_SIGN_ALGO): string
{
    ksort($fields);
    $stringToSign = '';
    foreach ($fields as $key => $value) {
        if (strpos($key, 'vads_') === 0) {
            $stringToSign .= $value . '+';
        }
    }
    $stringToSign .= $secretKey;
    return base64_encode(hash_hmac(strtolower(str_replace('-', '', $algo)), $stringToSign, $secretKey, true));
}

// Initialisation des variables
$message = "Une erreur est survenue lors du traitement du retour de paiement.";
$is_signature_valid = false;
$transaction_details = [];

// Vérifier si des données POST sont reçues
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['signature'])) {
    $received_data = $_POST;
    $received_signature = $received_data['signature'];
    unset($received_data['signature']); // Retirer la signature des données pour le calcul

    // Calculer la signature attendue
    $expected_signature = calculate_scellius_signature($received_data, SCELLIUS_SECRET_KEY);

    // Vérifier la signature
    if ($expected_signature === $received_signature) {
        $is_signature_valid = true;
        $transaction_details = $received_data; // Stocker les détails pour affichage

        // Analyser le statut de la transaction (vads_trans_status)
        $status = $received_data['vads_trans_status'] ?? 'UNKNOWN';

        switch ($status) {
            case 'AUTHORISED':
            case 'CAPTURED':
                $message = "Paiement accepté ! Merci pour votre commande.";
                // Ici, vous pourriez mettre à jour le statut de la commande dans votre base de données
                // si vous n'utilisez pas la notification serveur-à-serveur, mais ce n'est pas recommandé.
                // Il est préférable de se fier à notification.php pour cela.
                break;
            case 'REFUSED':
                $message = "Paiement refusé par la banque.";
                break;
            case 'CANCELLED':
                $message = "Paiement annulé par l'utilisateur.";
                break;
            case 'EXPIRED':
                 $message = "La session de paiement a expiré.";
                 break; // L'accolade fermante était manquante après ce break
            case 'WAITING_AUTHORISATION':
            case 'WAITING_CAPTURE':
                 $message = "Paiement en attente de validation.";
                 break;
            default:
                $message = "Statut du paiement inconnu ou non finalisé.";
                break;
        } // Fin du switch

    } else {
        // Signature invalide - Risque de fraude !
        $message = "Erreur de sécurité : la signature du paiement est invalide. Veuillez contacter le support.";
        // Logguer cette tentative pour investigation
        error_log("Signature invalide reçue sur retour_paiement.php. Attendu: $expected_signature, Reçu: $received_signature. Données: " . print_r($received_data, true));
    }

} else {
    // Aucune donnée POST reçue - accès direct à la page ?
    $message = "Aucune information de paiement reçue.";
}

?>
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Retour Paiement Scellius</title>
    <style>
        body { font-family: sans-serif; padding: 20px; }
        .status-success { color: green; font-weight: bold; }
        .status-error { color: red; font-weight: bold; }
        .status-info { color: orange; font-weight: bold; }
        pre { background-color: #f0f0f0; padding: 10px; border: 1px solid #ccc; overflow-x: auto; }
    </style>
</head>
<body>

    <h1>Résultat de votre Paiement</h1>

    <?php
        $status_class = 'status-error'; // Défaut
        if ($is_signature_valid) {
            $status = $transaction_details['vads_trans_status'] ?? 'UNKNOWN';
            if (in_array($status, ['AUTHORISED', 'CAPTURED'])) {
                $status_class = 'status-success';
            } elseif (in_array($status, ['WAITING_AUTHORISATION', 'WAITING_CAPTURE', 'CANCELLED', 'EXPIRED'])) {
                 $status_class = 'status-info';
            }
        }
    ?>

    <p class="<?php echo $status_class; ?>"><?php echo htmlspecialchars($message); ?></p>

    <?php if ($is_signature_valid && !empty($transaction_details)): ?>
        <h2>Détails de la transaction (pour information)</h2>
        <p>ID Transaction: <?php echo htmlspecialchars($transaction_details['vads_trans_id'] ?? 'N/A'); ?></p>
        <p>Montant: <?php echo htmlspecialchars(($transaction_details['vads_amount'] ?? 0) / 100); ?> <?php echo htmlspecialchars($transaction_details['vads_currency'] ?? 'EUR'); ?></p>
        <p>Statut: <?php echo htmlspecialchars($transaction_details['vads_trans_status'] ?? 'N/A'); ?></p>
        <!-- Afficher d'autres détails si nécessaire -->
        <!-- <pre><?php // print_r($transaction_details); ?></pre> -->
    <?php endif; ?>

    <p><a href="index.php">Retourner à la page d'accueil</a></p>

</body>
</html>