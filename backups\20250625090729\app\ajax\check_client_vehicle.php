<?php
// Inclure les fichiers nécessaires
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../models/VehiculeModel.php';

// Activer les erreurs pour le débogage
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Définir l'en-tête pour JSON
header('Content-Type: application/json');

// Vérifier si l'email est fourni
if (!isset($_GET['email']) || empty($_GET['email'])) {
    echo json_encode(['success' => false, 'message' => 'Email non fourni', 'hasVehicle' => false]);
    exit;
}

$email = $_GET['email'];

try {
    // Initialiser le modèle de véhicule
    $vehiculeModel = new VehiculeModel();

    // Vérifier si le client a déjà acheté un véhicule
    $hasVehicle = $vehiculeModel->clientHasVehicle($email);

    // Vérifier si le client a un véhicule en cours d'achat
    $pendingVehicle = $vehiculeModel->getClientPendingVehicle($email);

    // Récupérer l'ID du véhicule en cours d'achat si disponible
    $pendingVehicleId = null;
    if ($pendingVehicle) {
        $pendingVehicleId = $pendingVehicle['id'];
    }

    // Retourner le résultat
    echo json_encode([
        'success' => true,
        'hasVehicle' => $hasVehicle,
        'pendingVehicle' => $pendingVehicle ? [
            'id' => $pendingVehicle['id'],
            'reference' => $pendingVehicle['reference'],
            'libelle' => $pendingVehicle['libelle']
        ] : null
    ]);
} catch (Exception $e) {
    // En cas d'erreur
    error_log("Erreur lors de la vérification du véhicule pour $email: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Erreur lors de la vérification',
        'hasVehicle' => false
    ]);
}
