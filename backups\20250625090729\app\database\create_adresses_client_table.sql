-- Création de la table adresses_client si elle n'existe pas déjà
CREATE TABLE IF NOT EXISTS adresses_client (
    id INT AUTO_INCREMENT PRIMARY KEY,
    client_uid VARCHAR(32) NOT NULL,
    adresse VARCHAR(255) NOT NULL,
    complement VARCHAR(255),
    code_postal VARCHAR(10) NOT NULL,
    ville VARCHAR(100) NOT NULL,
    pays VARCHAR(100) NOT NULL DEFAULT 'France',
    telephone VARCHAR(20),
    date_creation DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (client_uid) REFERENCES clients(uid) ON DELETE CASCADE
);
