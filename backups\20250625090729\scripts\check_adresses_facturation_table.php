<?php
// Charger la configuration de la base de données
require_once __DIR__ . '/../app/config/database.php';

// Connexion à la base de données
$conn = getDbConnection();

// Vérifier si la table adresses_facturation existe
$tableExists = false;
$result = $conn->query("SHOW TABLES LIKE 'adresses_facturation'");
if ($result->num_rows > 0) {
    $tableExists = true;
    echo "La table 'adresses_facturation' existe.\n";
} else {
    echo "La table 'adresses_facturation' n'existe pas.\n";
}

// Si la table existe, vérifier sa structure
if ($tableExists) {
    $result = $conn->query("DESCRIBE adresses_facturation");
    echo "Structure de la table 'adresses_facturation':\n";
    while ($row = $result->fetch_assoc()) {
        echo "- " . $row['Field'] . " (" . $row['Type'] . ")\n";
    }

    // Vérifier les relations avec la table clients
    echo "\nVérification des relations avec la table clients:\n";
    $result = $conn->query("SELECT * FROM adresses_facturation LIMIT 1");
    if ($result->num_rows > 0) {
        $row = $result->fetch_assoc();
        echo "Exemple d'enregistrement:\n";
        foreach ($row as $key => $value) {
            echo "- " . $key . ": " . $value . "\n";
        }
    } else {
        echo "Aucun enregistrement trouvé dans la table adresses_facturation.\n";
    }
}

// Fermer la connexion
$conn->close();
?>
