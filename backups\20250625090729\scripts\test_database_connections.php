<?php
// Charger la configuration de la base de données
require_once __DIR__ . '/../app/config/database.php';

echo "=== Test des connexions aux bases de données ===\n\n";

// Tester la connexion à la base de données des véhicules
echo "Connexion à la base de données des véhicules:\n";
echo "- Hôte: " . DB_HOST . "\n";
echo "- Utilisateur: " . DB_USER . "\n";
echo "- Base de données: " . DB_NAME . "\n";

try {
    $conn = getDbConnection();
    echo "- Statut: CONNECTÉ\n";
    
    // Vérifier si la table vehicules existe
    $result = $conn->query("SHOW TABLES LIKE 'vehicules'");
    if ($result->num_rows > 0) {
        echo "- Table 'vehicules' trouvée\n";
        
        // Compter le nombre de véhicules
        $result = $conn->query("SELECT COUNT(*) as count FROM vehicules");
        $row = $result->fetch_assoc();
        echo "- Nombre de véhicules: " . $row['count'] . "\n";
    } else {
        echo "- Table 'vehicules' non trouvée\n";
    }
    
    $conn->close();
} catch (Exception $e) {
    echo "- Statut: ERREUR - " . $e->getMessage() . "\n";
}

echo "\n";

// Tester la connexion à la base de données des clients
echo "Connexion à la base de données des clients:\n";
echo "- Hôte: " . DB_CLIENTS_HOST . "\n";
echo "- Utilisateur: " . DB_CLIENTS_USER . "\n";
echo "- Base de données: " . DB_CLIENTS_NAME . "\n";

try {
    $conn = getClientsDbConnection();
    echo "- Statut: CONNECTÉ\n";
    
    // Vérifier si la table clients existe
    $result = $conn->query("SHOW TABLES LIKE 'clients'");
    if ($result->num_rows > 0) {
        echo "- Table 'clients' trouvée\n";
        
        // Compter le nombre de clients
        $result = $conn->query("SELECT COUNT(*) as count FROM clients");
        $row = $result->fetch_assoc();
        echo "- Nombre de clients: " . $row['count'] . "\n";
    } else {
        echo "- Table 'clients' non trouvée\n";
    }
    
    // Vérifier si la table adresses_facturation existe
    $result = $conn->query("SHOW TABLES LIKE 'adresses_facturation'");
    if ($result->num_rows > 0) {
        echo "- Table 'adresses_facturation' trouvée\n";
        
        // Compter le nombre d'adresses
        $result = $conn->query("SELECT COUNT(*) as count FROM adresses_facturation");
        $row = $result->fetch_assoc();
        echo "- Nombre d'adresses: " . $row['count'] . "\n";
    } else {
        echo "- Table 'adresses_facturation' non trouvée\n";
    }
    
    $conn->close();
} catch (Exception $e) {
    echo "- Statut: ERREUR - " . $e->getMessage() . "\n";
}

echo "\n=== Fin du test ===\n";
?>
