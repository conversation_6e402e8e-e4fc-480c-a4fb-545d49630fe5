<?php
// Script pour générer des images d'exemple pour les véhicules

// Charger la configuration de la base de données
require_once __DIR__ . '/../app/config/database.php';
require_once __DIR__ . '/../app/models/VehiculeModel.php';

// Répertoire où les images seront stockées
$targetDir = __DIR__ . '/../public/img/autres/';

// Vérifier si le répertoire existe, sinon le créer
if (!is_dir($targetDir)) {
    mkdir($targetDir, 0777, true);
}

// Récupérer tous les véhicules de la catégorie "AUTRE VEHICULE"
$vehiculeModel = new VehiculeModel();
$vehicules = $vehiculeModel->getAllVehicules('AUTRE VEHICULE');

// Liste des véhicules pour lesquels nous devons créer des images
$vehiculesToCreate = [];
foreach ($vehicules as $vehicule) {
    $reference = $vehicule['reference'];
    $searchRef = str_replace('_', ' ', $reference);

    // Vérifier si l'image existe déjà
    $imagePath = $targetDir . $searchRef . '.png';
    if (!file_exists($imagePath)) {
        $vehiculesToCreate[] = $vehicule;
    }
}

// Si aucune image à créer, terminer
if (empty($vehiculesToCreate)) {
    echo "Toutes les images existent déjà.\n";
    exit;
}

// Fonction pour copier l'image existante pour les autres véhicules
function createVehicleImage($vehicule, $targetPath) {
    // Utiliser l'image HALF TRACK.png comme modèle
    $sourceImage = __DIR__ . '/../public/img/autres/HALF TRACK.png';

    if (file_exists($sourceImage)) {
        // Copier l'image
        copy($sourceImage, $targetPath);
        echo "Image copiée pour " . $vehicule['libelle'] . " à " . $targetPath . "\n";
    } else {
        echo "Image source introuvable : " . $sourceImage . "\n";
    }
}

// Créer les images manquantes
foreach ($vehiculesToCreate as $vehicule) {
    $reference = $vehicule['reference'];
    $searchRef = str_replace('_', ' ', $reference);
    $imagePath = $targetDir . $searchRef . '.png';
    createVehicleImage($vehicule, $imagePath);
}

echo "Génération des images terminée.\n";
?>
