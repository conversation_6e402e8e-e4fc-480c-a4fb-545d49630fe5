<?php
// Charger la configuration de la base de données
require_once __DIR__ . '/../app/config/database.php';
require_once __DIR__ . '/../app/models/VehiculeModel.php';

// Vérifier si l'ID du véhicule est fourni
if (!isset($argv[1]) || empty($argv[1])) {
    echo "Usage: php reset_vehicle_status.php <vehicle_id>\n";
    exit(1);
}

$vehicleId = (int)$argv[1];

// Initialiser le modèle de véhicule
$vehiculeModel = new VehiculeModel();

// Récupérer les informations du véhicule
$vehicule = $vehiculeModel->getVehiculeById($vehicleId);

if (!$vehicule) {
    echo "Erreur: Véhicule ID $vehicleId non trouvé.\n";
    exit(1);
}

echo "Véhicule trouvé:\n";
echo "  ID: " . $vehicule['id'] . "\n";
echo "  Référence: " . $vehicule['reference'] . "\n";
echo "  Libellé: " . $vehicule['libelle'] . "\n";
echo "  Statut actuel: " . $vehicule['statut'] . "\n";
echo "  Client: " . $vehicule['client'] . "\n";
echo "  Date/heure d'achat: " . ($vehicule['dateheureachat'] ?? 'Non défini') . "\n";

// Demander confirmation
echo "\nÊtes-vous sûr de vouloir réinitialiser le statut de ce véhicule à 'Disponible' ? (o/n): ";
$handle = fopen("php://stdin", "r");
$line = trim(fgets($handle));
fclose($handle);

if (strtolower($line) !== 'o' && strtolower($line) !== 'oui') {
    echo "Opération annulée.\n";
    exit(0);
}

// Réinitialiser le statut du véhicule
if ($vehiculeModel->resetVehiculeStatus($vehicleId)) {
    echo "SUCCÈS: Statut du véhicule ID $vehicleId réinitialisé à 'Disponible'.\n";
} else {
    echo "ERREUR: Échec de la réinitialisation du statut du véhicule ID $vehicleId.\n";
}
?>
