<?php

// Configuration Scellius V2 - À REMPLACER PAR VOS VRAIES VALEURS FOURNIES PAR LA BANQUE

// Identifiant de la boutique (Site ID)
define('SCELLIUS_SITE_ID', '30774574'); // Remplacez par votre identifiant de site

// Clé secrète (Secret Key) - NE JAMAIS LA PARTAGER PUBLIQUEMENT
define('SCELLIUS_SECRET_KEY', '5KLmzxQLYOgKCzGu'); // Remplacez par votre clé secrète
//define('SCELLIUS_SECRET_KEY', 'BdYvhNcbCvO7Yxdo'); // Remplacez par votre clé secrète

// Mode de fonctionnement ('TEST' ou 'PRODUCTION')
define('SCELLIUS_CTX_MODE', 'PRODUCTION'); // Mettez 'PRODUCTION' pour les paiements réels

// URL de la plateforme de paiement Scellius (fournie par la banque)
define('SCELLIUS_URL', 'https://scelliuspaiement.labanquepostale.fr/vads-payment/'); // URL pour Systempay, adaptez si nécessaire

// Version de l'API (vads_version)
define('SCELLIUS_API_VERSION', 'V2');

// Algorithme de signature (vads_sign_algorithm) - Ex: SHA-256
define('SCELLIUS_SIGN_ALGO', 'SHA-256'); // Vérifiez l'algorithme requis par votre banque

// URLs de notification et de retour
define('SCELLIUS_URL_RETURN', 'http://vf.jeep-dodge-gmc.com/');
define('SCELLIUS_URLNOTIFY', 'http://vf.jeep-dodge-gmc.com/app/scellius/notification.php');

// URL de retour après paiement (page de succès/échec pour l'utilisateur)
//define('SCELLIUS_URL_RETURN', 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . '/retour_paiement.php');

// URL de notification serveur à serveur (pour la confirmation automatique du paiement)
//define('SCELLIUS_URL_NOTIFY', 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']) . '/notification.php');

// Fuseau horaire pour la date de transaction (requis par Scellius)
date_default_timezone_set('Europe/Paris');

?>