<?php
// Inclure les fichiers nécessaires
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../models/VehiculeModel.php';

// Activer les erreurs pour le débogage
ini_set('display_errors', 1);
error_reporting(E_ALL);

// Définir l'en-tête pour JSON
header('Content-Type: application/json');

// Vérifier si le titre est fourni
if (!isset($_GET['title']) || empty($_GET['title'])) {
    echo json_encode(['success' => false, 'message' => 'Titre non fourni']);
    exit;
}

$title = $_GET['title'];

try {
    // Initialiser le modèle de véhicule
    $vehiculeModel = new VehiculeModel();
    
    // Récupérer l'ID du véhicule à partir de son titre
    $vehiculeId = $vehiculeModel->getVehiculeIdByTitle($title);
    
    if ($vehiculeId) {
        echo json_encode([
            'success' => true,
            'vehiculeId' => $vehiculeId
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Véhicule non trouvé'
        ]);
    }
} catch (Exception $e) {
    // En cas d'erreur
    error_log("Erreur lors de la récupération de l'ID du véhicule pour $title: " . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'Erreur lors de la récupération de l\'ID du véhicule'
    ]);
}
